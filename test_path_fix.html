<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片路径修复功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-case {
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-input, .test-output, .test-expected {
            margin: 5px 0;
            font-family: monospace;
            background: #f8f8f8;
            padding: 5px;
            border-radius: 4px;
        }
        .pass {
            color: #28a745;
            font-weight: bold;
        }
        .fail {
            color: #dc3545;
            font-weight: bold;
        }
        .summary {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>图片路径修复功能测试</h1>
    <div id="test-results"></div>
    <div id="summary" class="summary"></div>

    <script>
        // 图片路径修复函数
        function fixImagePath(imagePath) {
            if (!imagePath || typeof imagePath !== 'string') {
                return imagePath;
            }
            
            let fixedPath = imagePath;
            
            // 处理错误的HTTP协议前缀（如 http://tmp/ 应该是 /tmp/）
            if (fixedPath.startsWith('http://tmp/')) {
                fixedPath = fixedPath.replace('http://tmp/', '/tmp/');
                console.log('修正HTTP临时文件路径:', fixedPath);
            }
            
            // 处理URL编码问题
            if (fixedPath.indexOf('%') >= 0) {
                try {
                    fixedPath = decodeURIComponent(fixedPath);
                    console.log('解码路径:', fixedPath);
                } catch (e) {
                    console.warn('路径解码失败:', e);
                }
            }
            
            // 确保临时文件路径格式正确
            if (fixedPath.indexOf('tmp/') >= 0 && !fixedPath.startsWith('/')) {
                // 提取tmp路径部分
                const tmpIndex = fixedPath.indexOf('tmp/');
                fixedPath = fixedPath.substring(tmpIndex);
                if (!fixedPath.startsWith('/')) {
                    fixedPath = '/' + fixedPath;
                }
                console.log('修正临时文件路径:', fixedPath);
            }
            
            // 移除wxfile://前缀（如果存在）
            if (fixedPath.startsWith('wxfile://')) {
                fixedPath = fixedPath.replace('wxfile://', '');
                console.log('移除wxfile前缀:', fixedPath);
            }
            
            return fixedPath;
        }

        // 测试用例
        const testCases = [
            {
                input: 'http://tmp/05OyNpMSZU0Bed92532913e5f044cc38cbf36665e56b.png',
                expected: '/tmp/05OyNpMSZU0Bed92532913e5f044cc38cbf36665e56b.png',
                description: '修复HTTP协议前缀错误'
            },
            {
                input: 'wxfile:///tmp/test.jpg',
                expected: '/tmp/test.jpg',
                description: '移除wxfile前缀'
            },
            {
                input: 'tmp/test.png',
                expected: '/tmp/test.png',
                description: '添加根路径斜杠'
            },
            {
                input: '/tmp/test.jpg',
                expected: '/tmp/test.jpg',
                description: '正确路径保持不变'
            },
            {
                input: 'http%3A//tmp/test.png',
                expected: 'http://tmp/test.png',
                description: '处理URL编码'
            }
        ];

        // 运行测试
        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            const summaryDiv = document.getElementById('summary');
            
            let passedTests = 0;
            let totalTests = testCases.length;
            
            testCases.forEach((testCase, index) => {
                const result = fixImagePath(testCase.input);
                const passed = result === testCase.expected;
                
                if (passed) passedTests++;
                
                const testDiv = document.createElement('div');
                testDiv.className = 'test-case';
                testDiv.innerHTML = `
                    <div class="test-title">测试 ${index + 1}: ${testCase.description}</div>
                    <div class="test-input">输入: ${testCase.input}</div>
                    <div class="test-output">输出: ${result}</div>
                    <div class="test-expected">期望: ${testCase.expected}</div>
                    <div class="${passed ? 'pass' : 'fail'}">${passed ? '✅ 测试通过' : '❌ 测试失败'}</div>
                `;
                
                resultsDiv.appendChild(testDiv);
            });
            
            summaryDiv.innerHTML = `
                <h2>测试结果</h2>
                <p>通过: ${passedTests}/${totalTests}</p>
                <p class="${passedTests === totalTests ? 'pass' : 'fail'}">
                    ${passedTests === totalTests ? '🎉 所有测试通过！' : '⚠️ 部分测试失败'}
                </p>
            `;
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
