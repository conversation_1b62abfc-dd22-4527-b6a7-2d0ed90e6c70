// app.ts
interface IGlobalData {
  systemInfo: WechatMiniprogram.SystemInfo | null;
  deviceInfo: WechatMiniprogram.DeviceInfo | null;
  windowInfo: WechatMiniprogram.WindowInfo | null;
  appBaseInfo: WechatMiniprogram.AppBaseInfo | null;
  menuButtonInfo: WechatMiniprogram.Rect | null;
  navBarHeight: number;
  statusBarHeight: number;
  contentHeight: number; // 内容区域高度
  themeColor: string;
  settings: {
    saveHighQuality: boolean;
    autoBeautify: boolean;
    showTips: boolean;
    darkMode: boolean;
  };
}

interface IAppInstance {
  globalData: IGlobalData;
  safeApiCall<T>(apiFunc: () => T, defaultValue: T): Promise<T>;
  initSystemInfo(): Promise<void>;
  refreshSystemInfo(): void;
  calculateNavBarHeight(windowInfo: WechatMiniprogram.WindowInfo | null, menuButtonInfo: WechatMiniprogram.Rect | null, deviceInfo: WechatMiniprogram.DeviceInfo | null): void;
  onLaunch(): void;
  onShow(): void;
}

// 扩展微信小程序类型定义
declare namespace WechatMiniprogram {
  interface Wx {
    getWindowInfo(): WindowInfo;
    getDeviceInfo(): DeviceInfo;
    getAppBaseInfo(): AppBaseInfo;
    getSystemSetting(): SystemSetting;
    getAppAuthorizeSetting(): AppAuthorizeSetting;
  }

  interface SystemInfo {
    menuButtonBoundingClientRect?: Rect;
  }

  interface WindowInfo {
    pixelRatio: number;
    screenWidth: number;
    screenHeight: number;
    windowWidth: number;
    windowHeight: number;
    statusBarHeight: number;
    safeArea: SafeArea;
    screenTop: number;
    navigatorHeight: number;
  }

  interface DeviceInfo {
    brand: string;
    model: string;
    system: string;
    platform: string;
  }

  interface AppBaseInfo {
    SDKVersion: string;
    enableDebug: boolean;
    host: any;
    language: string;
    version: string;
    theme?: string;
  }

  interface SystemSetting {
    bluetoothEnabled: boolean;
    locationEnabled: boolean;
    wifiEnabled: boolean;
    deviceOrientation: string;
  }

  interface AppAuthorizeSetting {
    albumAuthorized: boolean;
    bluetoothAuthorized: boolean;
    cameraAuthorized: boolean;
    locationAuthorized: boolean;
    microphoneAuthorized: boolean;
    notificationAuthorized: boolean;
    phoneCalendarAuthorized: boolean;
  }
}

// 默认导航栏配置
const DEFAULT_NAV_CONFIG = {
  STATUS_BAR_HEIGHT: 44,
  STATUS_BAR_HEIGHT_NORMAL: 20,
  MENU_BUTTON_HEIGHT: 32,
  MENU_BUTTON_MARGIN: 4,
  NAV_BAR_EXTRA_MARGIN: 4,
  DEFAULT_SCREEN_WIDTH: 375,
  DEFAULT_SCREEN_HEIGHT: 667,
  DEFAULT_MENU_BUTTON_WIDTH: 87,
  DEFAULT_MENU_BUTTON_RIGHT: 7,
  MIN_NAV_BAR_HEIGHT: 48,
  MAX_NAV_BAR_HEIGHT: 88,
  NAV_BAR_HEIGHT: 60
};

// 默认设备信息
const DEFAULT_DEVICE_INFO: WechatMiniprogram.DeviceInfo = {
  brand: 'devtools',
  model: 'iPhone 6/7/8',
  system: 'iOS 10.0.1',
  platform: 'devtools'
};

// 默认窗口信息
const DEFAULT_WINDOW_INFO: WechatMiniprogram.WindowInfo = {
  pixelRatio: 2,
  screenWidth: DEFAULT_NAV_CONFIG.DEFAULT_SCREEN_WIDTH,
  screenHeight: DEFAULT_NAV_CONFIG.DEFAULT_SCREEN_HEIGHT,
  windowWidth: DEFAULT_NAV_CONFIG.DEFAULT_SCREEN_WIDTH,
  windowHeight: DEFAULT_NAV_CONFIG.DEFAULT_SCREEN_HEIGHT,
  statusBarHeight: DEFAULT_NAV_CONFIG.STATUS_BAR_HEIGHT_NORMAL,
  safeArea: {
    bottom: DEFAULT_NAV_CONFIG.DEFAULT_SCREEN_HEIGHT,
    height: DEFAULT_NAV_CONFIG.DEFAULT_SCREEN_HEIGHT - DEFAULT_NAV_CONFIG.STATUS_BAR_HEIGHT_NORMAL,
    left: 0,
    right: DEFAULT_NAV_CONFIG.DEFAULT_SCREEN_WIDTH,
    top: DEFAULT_NAV_CONFIG.STATUS_BAR_HEIGHT_NORMAL,
    width: DEFAULT_NAV_CONFIG.DEFAULT_SCREEN_WIDTH
  },
  screenTop: 0,
  navigatorHeight: DEFAULT_NAV_CONFIG.STATUS_BAR_HEIGHT_NORMAL + DEFAULT_NAV_CONFIG.MENU_BUTTON_HEIGHT + 2 * DEFAULT_NAV_CONFIG.MENU_BUTTON_MARGIN
};

// 默认应用基础信息
const DEFAULT_APP_BASE_INFO: WechatMiniprogram.AppBaseInfo = {
  SDKVersion: '3.0.0',
  enableDebug: false,
  host: {},
  language: 'zh_CN',
  version: '1.0.0',
  theme: 'light'
};

// 默认系统信息（兼容旧API）
const DEFAULT_SYSTEM_INFO: WechatMiniprogram.SystemInfo = {
  brand: DEFAULT_DEVICE_INFO.brand,
  model: DEFAULT_DEVICE_INFO.model,
  pixelRatio: DEFAULT_WINDOW_INFO.pixelRatio,
  screenWidth: DEFAULT_WINDOW_INFO.screenWidth,
  screenHeight: DEFAULT_WINDOW_INFO.screenHeight,
  windowWidth: DEFAULT_WINDOW_INFO.windowWidth,
  windowHeight: DEFAULT_WINDOW_INFO.windowHeight,
  statusBarHeight: DEFAULT_WINDOW_INFO.statusBarHeight,
  language: DEFAULT_APP_BASE_INFO.language,
  version: DEFAULT_APP_BASE_INFO.version,
  system: DEFAULT_DEVICE_INFO.system,
  platform: DEFAULT_DEVICE_INFO.platform,
  SDKVersion: DEFAULT_APP_BASE_INFO.SDKVersion,
  safeArea: DEFAULT_WINDOW_INFO.safeArea,
  albumAuthorized: false,
  benchmarkLevel: 1,
  bluetoothEnabled: false,
  cameraAuthorized: false,
  deviceOrientation: 'portrait',
  enableDebug: false,
  fontSizeSetting: 16,
  locationAuthorized: false,
  locationEnabled: false,
  locationReducedAccuracy: false,
  microphoneAuthorized: false,
  notificationAuthorized: false,
  notificationAlertAuthorized: false,
  notificationBadgeAuthorized: false,
  notificationSoundAuthorized: false,
  phoneCalendarAuthorized: false,
  wifiEnabled: false
} as WechatMiniprogram.SystemInfo;

// 默认胶囊按钮信息
const DEFAULT_MENU_BUTTON_INFO: WechatMiniprogram.Rect = {
  width: DEFAULT_NAV_CONFIG.DEFAULT_MENU_BUTTON_WIDTH,
  height: DEFAULT_NAV_CONFIG.MENU_BUTTON_HEIGHT,
  top: DEFAULT_NAV_CONFIG.STATUS_BAR_HEIGHT_NORMAL + DEFAULT_NAV_CONFIG.MENU_BUTTON_MARGIN,
  right: DEFAULT_NAV_CONFIG.DEFAULT_SCREEN_WIDTH - DEFAULT_NAV_CONFIG.DEFAULT_MENU_BUTTON_RIGHT,
  bottom: DEFAULT_NAV_CONFIG.STATUS_BAR_HEIGHT_NORMAL + DEFAULT_NAV_CONFIG.MENU_BUTTON_HEIGHT + DEFAULT_NAV_CONFIG.MENU_BUTTON_MARGIN,
  left: DEFAULT_NAV_CONFIG.DEFAULT_SCREEN_WIDTH - DEFAULT_NAV_CONFIG.DEFAULT_MENU_BUTTON_WIDTH - DEFAULT_NAV_CONFIG.DEFAULT_MENU_BUTTON_RIGHT
};

// 计算默认导航栏高度
const DEFAULT_NAV_BAR_HEIGHT = DEFAULT_MENU_BUTTON_INFO.bottom + DEFAULT_NAV_CONFIG.NAV_BAR_EXTRA_MARGIN;

// 默认内容区域高度
const DEFAULT_CONTENT_HEIGHT = DEFAULT_WINDOW_INFO.windowHeight - DEFAULT_NAV_BAR_HEIGHT;

App<IAppInstance>({
  globalData: {
    systemInfo: DEFAULT_SYSTEM_INFO,
    deviceInfo: DEFAULT_DEVICE_INFO,
    windowInfo: DEFAULT_WINDOW_INFO,
    appBaseInfo: DEFAULT_APP_BASE_INFO,
    menuButtonInfo: DEFAULT_MENU_BUTTON_INFO,
    navBarHeight: DEFAULT_NAV_BAR_HEIGHT,
    statusBarHeight: DEFAULT_NAV_CONFIG.STATUS_BAR_HEIGHT_NORMAL,
    contentHeight: DEFAULT_CONTENT_HEIGHT,
    themeColor: '#FF7D2C',
    settings: {
      saveHighQuality: false,
      autoBeautify: false,
      showTips: true,
      darkMode: false
    }
  },

  onLaunch: function() {
    console.log('App onLaunch - 开始初始化系统信息');
    
    // 在启动时直接使用同步方式初始化基本系统信息
    try {
      const api = wx as any;
      const sysInfo = api.getSystemInfoSync();
      
      if (sysInfo && typeof sysInfo === 'object') {
        // 提取设备信息
        this.globalData.deviceInfo = {
          brand: sysInfo.brand || DEFAULT_DEVICE_INFO.brand,
          model: sysInfo.model || DEFAULT_DEVICE_INFO.model,
          system: sysInfo.system || DEFAULT_DEVICE_INFO.system,
          platform: sysInfo.platform || DEFAULT_DEVICE_INFO.platform
        };
        
        // 提取窗口信息
        this.globalData.windowInfo = {
          pixelRatio: sysInfo.pixelRatio || DEFAULT_WINDOW_INFO.pixelRatio,
          screenWidth: sysInfo.screenWidth || DEFAULT_WINDOW_INFO.screenWidth,
          screenHeight: sysInfo.screenHeight || DEFAULT_WINDOW_INFO.screenHeight,
          windowWidth: sysInfo.windowWidth || DEFAULT_WINDOW_INFO.windowWidth,
          windowHeight: sysInfo.windowHeight || DEFAULT_WINDOW_INFO.windowHeight,
          statusBarHeight: sysInfo.statusBarHeight || DEFAULT_WINDOW_INFO.statusBarHeight,
          safeArea: sysInfo.safeArea || DEFAULT_WINDOW_INFO.safeArea,
          screenTop: DEFAULT_WINDOW_INFO.screenTop,
          navigatorHeight: DEFAULT_WINDOW_INFO.navigatorHeight
        };
        
        // 获取胶囊按钮信息
        let menuButtonInfo = DEFAULT_MENU_BUTTON_INFO;
        if (sysInfo.menuButtonBoundingClientRect) {
          menuButtonInfo = sysInfo.menuButtonBoundingClientRect;
        }
        this.globalData.menuButtonInfo = menuButtonInfo;
        
        // 更新系统信息
        this.globalData.systemInfo = sysInfo;
        
        // 计算导航栏高度
        this.calculateNavBarHeight(
          this.globalData.windowInfo,
          this.globalData.menuButtonInfo,
          this.globalData.deviceInfo
        );
        
        console.log('App onLaunch - 同步方式初始化系统信息完成');
      }
    } catch (error) {
      console.error('App onLaunch - 同步初始化失败，尝试异步方式:', error);
      // 如果同步方式失败，尝试异步方式
      this.initSystemInfo().catch(err => {
        console.error('异步初始化系统信息也失败:', err);
      });
    }
  },
  
  onShow: function() {
    if (!this.globalData.deviceInfo || !this.globalData.windowInfo) {
      this.refreshSystemInfo();
    }
  },

  safeApiCall: function<T>(apiFunc: () => T, defaultValue: T): Promise<T> {
    return new Promise<T>(resolve => {
      try {
        // 检查API函数是否存在
        if (typeof apiFunc !== 'function') {
          console.warn('API函数未定义，使用默认值');
          return resolve(defaultValue);
        }

        // 调用API并处理结果
        let result: T | null;
        try {
          // 使用类型断言处理结果
          const apiResult = apiFunc();
          result = apiResult || null;
          // 如果API直接返回null或undefined，立即使用默认值
          if (result === null || result === undefined) {
            console.warn('API直接返回null或undefined，使用默认值');
            return resolve(defaultValue);
          }
          
          // 如果是对象类型，检查是否为空对象或是否有必要的属性
          if (typeof result === 'object' && result !== null) {
            // 检查对象是否为空
            if (Object.keys(result).length === 0) {
              console.warn('API返回空对象，使用默认值');
              return resolve(defaultValue);
            }
            
            // 检查对象是否有必要的属性
            if (defaultValue && typeof defaultValue === 'object') {
              const expectedKeys = Object.keys(defaultValue as object);
              if (expectedKeys.length > 0) {
                const hasRequiredKeys = expectedKeys.some(key => {
                  const resultObj = result as Record<string, any>;
                  return key in resultObj && resultObj[key] !== null && resultObj[key] !== undefined;
                });
                
                if (!hasRequiredKeys) {
                  console.warn('API返回对象缺少必要属性，使用默认值');
                  return resolve(defaultValue);
                }
              }
            }
          }
        } catch (apiError) {
          console.warn('API直接调用失败:', apiError);
          return resolve(defaultValue);
        }
        
        // 处理Promise类型的返回值
        if (result instanceof Promise) {
          result
            .then(value => {
              if (value === null || value === undefined) {
                console.warn('API返回null或undefined，使用默认值');
                resolve(defaultValue);
              } else if (typeof value === 'object') {
                // 检查对象是否为空
                if (Object.keys(value).length === 0) {
                  console.warn('API返回空对象，使用默认值');
                  resolve(defaultValue);
                } else {
                  resolve(value as T);
                }
              } else {
                console.warn('API返回非对象值，使用默认值');
                resolve(defaultValue);
              }
            })
            .catch(error => {
              console.warn('API Promise执行失败:', error);
              resolve(defaultValue);
            });
          return;
        }

        // 处理同步返回值
        if (result === null || result === undefined) {
          console.warn('API返回null或undefined，使用默认值');
          resolve(defaultValue);
        } else if (typeof result === 'object') {
          // 检查对象是否为空
          if (Object.keys(result).length === 0) {
            console.warn('API返回空对象，使用默认值');
            resolve(defaultValue);
          } else {
            resolve(result);
          }
        } else {
          console.warn('API返回非对象值，使用默认值');
          resolve(defaultValue);
        }
      } catch (error) {
        console.warn('API调用失败:', error);
        resolve(defaultValue);
      }
    });
  },
  
  initSystemInfo: async function() {
    const startTime = Date.now();
    console.log('开始初始化系统信息...');
    
    // 检测是否在模拟器中运行
    const isInSimulator = () => {
      try {
        // 尝试通过系统信息判断
        const api = wx as any;
        if (typeof api.getSystemInfoSync !== 'function') {
          return false;
        }
        
        const sysInfo = api.getSystemInfoSync();
        if (sysInfo && (
          sysInfo.platform === 'devtools' || 
          sysInfo.brand === 'devtools' ||
          (sysInfo.model && sysInfo.model.includes('devtools'))
        )) {
          return true;
        }
        return false;
      } catch (e) {
        console.warn('检测模拟器环境失败:', e);
        return false;
      }
    };
    
    // 如果在模拟器中，直接使用getSystemInfoSync
    const inSimulator = isInSimulator();
    if (inSimulator) {
      console.log('检测到模拟器环境，直接使用getSystemInfoSync');
      
      try {
        // 在模拟器中，直接使用getSystemInfoSync获取所有信息
        const api = wx as any;
        // 确保getSystemInfoSync调用成功
        let sysInfo;
        try {
          sysInfo = api.getSystemInfoSync();
        } catch (e) {
          console.error('getSystemInfoSync调用失败:', e);
          sysInfo = {...DEFAULT_SYSTEM_INFO};
        }
        
        // 确保sysInfo不为null
        if (!sysInfo || typeof sysInfo !== 'object') {
          console.warn('getSystemInfoSync返回无效值，使用默认值');
          sysInfo = {...DEFAULT_SYSTEM_INFO};
        }
        
        // 提取设备信息
        const deviceInfo = {
          brand: sysInfo.brand || DEFAULT_DEVICE_INFO.brand,
          model: sysInfo.model || DEFAULT_DEVICE_INFO.model,
          system: sysInfo.system || DEFAULT_DEVICE_INFO.system,
          platform: sysInfo.platform || DEFAULT_DEVICE_INFO.platform
        };
        
        // 提取窗口信息
        const windowInfo = {
          pixelRatio: sysInfo.pixelRatio || DEFAULT_WINDOW_INFO.pixelRatio,
          screenWidth: sysInfo.screenWidth || DEFAULT_WINDOW_INFO.screenWidth,
          screenHeight: sysInfo.screenHeight || DEFAULT_WINDOW_INFO.screenHeight,
          windowWidth: sysInfo.windowWidth || DEFAULT_WINDOW_INFO.windowWidth,
          windowHeight: sysInfo.windowHeight || DEFAULT_WINDOW_INFO.windowHeight,
          statusBarHeight: sysInfo.statusBarHeight || DEFAULT_WINDOW_INFO.statusBarHeight,
          safeArea: sysInfo.safeArea || DEFAULT_WINDOW_INFO.safeArea,
          screenTop: DEFAULT_WINDOW_INFO.screenTop,
          navigatorHeight: DEFAULT_WINDOW_INFO.navigatorHeight
        };
        
        // 提取应用基础信息
        const appBaseInfo = {
          SDKVersion: sysInfo.SDKVersion || DEFAULT_APP_BASE_INFO.SDKVersion,
          enableDebug: sysInfo.enableDebug || DEFAULT_APP_BASE_INFO.enableDebug,
          host: DEFAULT_APP_BASE_INFO.host,
          language: sysInfo.language || DEFAULT_APP_BASE_INFO.language,
          version: sysInfo.version || DEFAULT_APP_BASE_INFO.version,
          theme: sysInfo.theme || DEFAULT_APP_BASE_INFO.theme
        };
        
        // 获取胶囊按钮信息
        let menuButtonInfo = DEFAULT_MENU_BUTTON_INFO;
        if (sysInfo.menuButtonBoundingClientRect) {
          menuButtonInfo = sysInfo.menuButtonBoundingClientRect;
        }
        
        // 更新全局数据
        this.globalData.deviceInfo = deviceInfo;
        this.globalData.windowInfo = windowInfo;
        this.globalData.appBaseInfo = appBaseInfo;
        this.globalData.menuButtonInfo = menuButtonInfo;
        this.globalData.systemInfo = sysInfo;
        
        // 计算导航栏高度
        this.calculateNavBarHeight(windowInfo, menuButtonInfo, deviceInfo);
        
        console.log('模拟器环境系统信息初始化完成:', this.globalData);
        return;
      } catch (e) {
        console.error('模拟器环境特殊处理失败:', e);
        // 继续使用标准流程
      }
    }
    
    // 添加重试机制
    const retryApiCall = async (apiName: string, defaultValue: any, maxRetries = 3, delay = 500) => {
      let lastError = null;
      
      // 检测是否在模拟器中运行
      const isInDevTools = () => {
        try {
          const api = wx as any;
          if (typeof api.getSystemInfoSync !== 'function') {
            return false;
          }
          
          const sysInfo = api.getSystemInfoSync();
          return sysInfo && (
            sysInfo.platform === 'devtools' || 
            sysInfo.brand === 'devtools' ||
            (sysInfo.model && sysInfo.model.includes('devtools'))
          );
        } catch (e) {
          return false;
        }
      };
      
      // 在模拟器环境中，直接返回默认值
      if (isInDevTools()) {
        console.log(`模拟器环境中不调用 ${apiName}，直接使用默认值`);
        return defaultValue;
      }
      
      // 对getDeviceInfo使用更多重试次数
      if (apiName === 'getDeviceInfo') {
        maxRetries = 5;
        delay = 800;
      }
      
      for (let i = 0; i <= maxRetries; i++) {
        try {
          if (i > 0) {
            console.log(`尝试第${i}次重试 ${apiName}...`);
            // 延迟一段时间再重试
            await new Promise(resolve => setTimeout(resolve, delay * Math.min(i, 3)));
          }
          
          const result = await this.safeApiCall(() => {
            try {
              const api = wx as any;
              if (api && typeof api[apiName] === 'function') {
                // 特别处理getDeviceInfo
                if (apiName === 'getDeviceInfo') {
                  try {
                    // 首先尝试使用getDeviceInfo
                    const deviceInfo = api[apiName]();
                    if (deviceInfo && typeof deviceInfo === 'object') {
                      return deviceInfo;
                    }
                    
                    // 如果失败，尝试从getSystemInfoSync获取设备信息
                    console.log('getDeviceInfo返回无效，尝试从getSystemInfoSync获取...');
                    if (api.getSystemInfoSync && typeof api.getSystemInfoSync === 'function') {
                      const sysInfo = api.getSystemInfoSync();
                      if (sysInfo && typeof sysInfo === 'object') {
                        return {
                          brand: sysInfo.brand || defaultValue.brand,
                          model: sysInfo.model || defaultValue.model,
                          system: sysInfo.system || defaultValue.system,
                          platform: sysInfo.platform || defaultValue.platform
                        };
                      }
                    }
                  } catch (e) {
                    console.error('获取设备信息失败:', e);
                  }
                  return {...defaultValue};
                }
                
                // 正常调用其他API
                return api[apiName]() || defaultValue;
              } else {
                console.warn(`${apiName} API不可用，使用默认值`);
                return defaultValue;
              }
            } catch (e) {
              console.error(`${apiName} 调用出错:`, e);
              return defaultValue;
            }
          }, defaultValue);
          if (result && typeof result === 'object' && Object.keys(result).length > 0) {
            if (i > 0) {
              console.log(`${apiName} 重试成功!`);
            }
            return result;
          }
        } catch (error) {
          lastError = error;
          console.warn(`${apiName} 调用失败 (尝试 ${i+1}/${maxRetries+1}):`, error);
        }
      }
      
      console.error(`${apiName} 达到最大重试次数，使用默认值`);
      return defaultValue;
    };
    
    // 检查API是否可用
    const apiAvailable = {
      getDeviceInfo: typeof (wx as any).getDeviceInfo === 'function',
      getWindowInfo: typeof (wx as any).getWindowInfo === 'function',
      getAppBaseInfo: typeof (wx as any).getAppBaseInfo === 'function',
      getMenuButtonBoundingClientRect: typeof (wx as any).getMenuButtonBoundingClientRect === 'function',
      getSystemInfoSync: typeof (wx as any).getSystemInfoSync === 'function'
    };
    
    console.log('API可用性检查:', apiAvailable);
    
    try {
      // 并行获取所有信息
      // 使用新API组合获取系统信息
      // 首先获取系统信息
      const api = wx as any;
      let sysInfo = null;
      
      // 检测是否在开发者工具中运行
      const isInDevTools = () => {
        try {
          if (typeof api.getSystemInfoSync !== 'function') {
            return false;
          }
          
          const info = api.getSystemInfoSync();
          return info && (
            info.platform === 'devtools' || 
            info.brand === 'devtools' ||
            (info.model && info.model.includes('devtools'))
          );
        } catch (e) {
          return false;
        }
      };
      
      try {
        // 尝试使用getSystemInfoSync获取基本信息
        sysInfo = api.getSystemInfoSync() || null;
      } catch (e) {
        console.error('getSystemInfoSync调用失败:', e);
      }
      
      // 如果getSystemInfoSync失败，使用默认值
      if (!sysInfo || typeof sysInfo !== 'object') {
        console.warn('getSystemInfoSync返回无效值，使用默认值');
        sysInfo = {...DEFAULT_SYSTEM_INFO};
      }
      
      // 从sysInfo中提取胶囊按钮信息
      let menuButtonInfo = DEFAULT_MENU_BUTTON_INFO;
      if (sysInfo.menuButtonBoundingClientRect) {
        menuButtonInfo = sysInfo.menuButtonBoundingClientRect;
      } else {
        // 如果sysInfo中没有胶囊按钮信息，尝试单独获取（但不在模拟器中）
        if (!isInSimulator() && !isInDevTools() && api.getMenuButtonBoundingClientRect) {
          try {
            const mbInfo = api.getMenuButtonBoundingClientRect();
            if (mbInfo && typeof mbInfo === 'object' && mbInfo.height > 0) {
              menuButtonInfo = mbInfo;
            }
          } catch (e) {
            console.warn('获取胶囊按钮信息失败:', e);
          }
        }
      }
      
      // 使用retryApiCall获取其他信息（除了胶囊按钮）
      const [systemSetting, appAuthorizeSetting, deviceInfo, windowInfo, appBaseInfo] = await Promise.all([
        retryApiCall('getSystemSetting', {
          bluetoothEnabled: false,
          locationEnabled: false,
          wifiEnabled: false,
          deviceOrientation: 'portrait'
        }),
        retryApiCall('getAppAuthorizeSetting', {
          albumAuthorized: false,
          bluetoothAuthorized: false,
          cameraAuthorized: false,
          locationAuthorized: false,
          microphoneAuthorized: false,
          notificationAuthorized: false,
          phoneCalendarAuthorized: false
        }),
        retryApiCall('getDeviceInfo', DEFAULT_DEVICE_INFO),
        retryApiCall('getWindowInfo', DEFAULT_WINDOW_INFO),
        retryApiCall('getAppBaseInfo', DEFAULT_APP_BASE_INFO)
      ]);
    
      const processTime = Date.now();
      console.log(`[Perf] API调用完成，耗时: ${processTime - startTime}ms`);
      
      // 1. 更新设备信息
      this.globalData.deviceInfo = deviceInfo;
      console.log('设备信息:', deviceInfo);
      
      // 2. 更新窗口信息
      this.globalData.windowInfo = windowInfo;
      if (windowInfo && typeof windowInfo.statusBarHeight === 'number') {
        this.globalData.statusBarHeight = windowInfo.statusBarHeight;
      }
      console.log('窗口信息:', windowInfo);
      
      // 3. 更新应用基础信息
      this.globalData.appBaseInfo = appBaseInfo;
      console.log('应用基础信息:', appBaseInfo);
      
      // 4. 更新胶囊按钮信息
      this.globalData.menuButtonInfo = menuButtonInfo;
      console.log('胶囊按钮信息:', menuButtonInfo);
      
      // 5. 构建兼容的systemInfo对象
      const mergedSystemInfo: WechatMiniprogram.SystemInfo = {
        ...DEFAULT_SYSTEM_INFO
      };
      
      // 合并系统设置和授权信息
      if (systemSetting) {
        (mergedSystemInfo as any).bluetoothEnabled = systemSetting.bluetoothEnabled;
        (mergedSystemInfo as any).locationEnabled = systemSetting.locationEnabled;
        (mergedSystemInfo as any).wifiEnabled = systemSetting.wifiEnabled;
        (mergedSystemInfo as any).deviceOrientation = systemSetting.deviceOrientation;
      }
      
      if (appAuthorizeSetting) {
        // 使用类型断言合并授权信息
        (mergedSystemInfo as any).albumAuthorized = appAuthorizeSetting.albumAuthorized;
        (mergedSystemInfo as any).bluetoothAuthorized = appAuthorizeSetting.bluetoothAuthorized;
        (mergedSystemInfo as any).cameraAuthorized = appAuthorizeSetting.cameraAuthorized;
        (mergedSystemInfo as any).locationAuthorized = appAuthorizeSetting.locationAuthorized;
        (mergedSystemInfo as any).microphoneAuthorized = appAuthorizeSetting.microphoneAuthorized;
        (mergedSystemInfo as any).notificationAuthorized = appAuthorizeSetting.notificationAuthorized;
        (mergedSystemInfo as any).phoneCalendarAuthorized = appAuthorizeSetting.phoneCalendarAuthorized;
      }
      
      // 确保各个信息来源的数据都被合并，新API优先级高
      if (deviceInfo && typeof deviceInfo === 'object') {
        // 确保关键属性存在
        const safeDeviceInfo = {
          brand: deviceInfo.brand || DEFAULT_DEVICE_INFO.brand,
          model: deviceInfo.model || DEFAULT_DEVICE_INFO.model,
          system: deviceInfo.system || DEFAULT_DEVICE_INFO.system,
          platform: deviceInfo.platform || DEFAULT_DEVICE_INFO.platform
        };
        
        mergedSystemInfo.brand = safeDeviceInfo.brand;
        mergedSystemInfo.model = safeDeviceInfo.model;
        mergedSystemInfo.system = safeDeviceInfo.system;
        mergedSystemInfo.platform = safeDeviceInfo.platform;
      }
      
      if (windowInfo) {
        mergedSystemInfo.pixelRatio = windowInfo.pixelRatio;
        mergedSystemInfo.screenWidth = windowInfo.screenWidth;
        mergedSystemInfo.screenHeight = windowInfo.screenHeight;
        mergedSystemInfo.windowWidth = windowInfo.windowWidth;
        mergedSystemInfo.windowHeight = windowInfo.windowHeight;
        mergedSystemInfo.statusBarHeight = windowInfo.statusBarHeight;
        mergedSystemInfo.safeArea = windowInfo.safeArea;
      }
      
      if (appBaseInfo) {
        mergedSystemInfo.language = appBaseInfo.language;
        mergedSystemInfo.version = appBaseInfo.version;
        mergedSystemInfo.SDKVersion = appBaseInfo.SDKVersion;
        mergedSystemInfo.enableDebug = appBaseInfo.enableDebug;
      }
      
      // 添加胶囊按钮信息到systemInfo中，兼容某些库的需求
      if (menuButtonInfo) {
        mergedSystemInfo.menuButtonBoundingClientRect = menuButtonInfo;
      }
      
      this.globalData.systemInfo = mergedSystemInfo;
      
      // 6. 计算导航栏高度
      this.calculateNavBarHeight(windowInfo, menuButtonInfo, deviceInfo);
      
      console.log('系统信息初始化完成:', this.globalData);
      console.log(`[Perf] App.initSystemInfo took ${Date.now() - startTime}ms`);
    } catch (error) {
      console.error('初始化系统信息失败:', error);
      
      // 使用默认值
      console.log('使用默认系统信息');
      this.globalData.systemInfo = DEFAULT_SYSTEM_INFO;
      this.globalData.statusBarHeight = DEFAULT_NAV_CONFIG.STATUS_BAR_HEIGHT_NORMAL;
      
      // 尝试从systemSetting和appAuthorizeSetting中提取有用信息
      try {
        console.log('合并可用的系统设置信息');
        // 尝试直接使用getSystemInfoSync获取信息
        const api = wx as any;
        let sysInfo = null;
        
        try {
          sysInfo = api.getSystemInfoSync();
        } catch (e) {
          console.error('getSystemInfoSync调用失败:', e);
        }
        
        if (sysInfo && typeof sysInfo === 'object') {
          // 提取系统设置信息
          const systemSetting = {
            bluetoothEnabled: sysInfo.bluetoothEnabled || false,
            locationEnabled: sysInfo.locationEnabled || false,
            wifiEnabled: sysInfo.wifiEnabled || false,
            deviceOrientation: sysInfo.deviceOrientation || 'portrait'
          };
          
          // 合并可用的系统设置
          if (this.globalData.systemInfo) {
            (this.globalData.systemInfo as any).bluetoothEnabled = systemSetting.bluetoothEnabled;
            (this.globalData.systemInfo as any).locationEnabled = systemSetting.locationEnabled;
            (this.globalData.systemInfo as any).wifiEnabled = systemSetting.wifiEnabled;
          }
        }
      } catch (settingError) {
        console.warn('获取系统设置失败:', settingError);
      }
      
      // 无论如何都要计算导航栏高度
      this.calculateNavBarHeight(
        this.globalData.windowInfo || DEFAULT_WINDOW_INFO, 
        this.globalData.menuButtonInfo || DEFAULT_MENU_BUTTON_INFO, 
        this.globalData.deviceInfo || DEFAULT_DEVICE_INFO
      );
    }
  },
  
  refreshSystemInfo: function() {
    console.log('刷新系统信息');
    
    // 检测是否在模拟器中运行
    const isInSimulator = () => {
      try {
        const api = wx as any;
        if (typeof api.getSystemInfoSync !== 'function') {
          return false;
        }
        
        const sysInfo = api.getSystemInfoSync();
        return sysInfo && (
          sysInfo.platform === 'devtools' || 
          sysInfo.brand === 'devtools' ||
          (sysInfo.model && sysInfo.model.includes('devtools'))
        );
      } catch (e) {
        return false;
      }
    };
    
    // 如果在模拟器中，使用特殊处理
    if (isInSimulator()) {
      console.log('模拟器环境下刷新系统信息，直接使用getSystemInfoSync');
      
      try {
        // 使用getSystemInfoSync获取信息
        const api = wx as any;
        
        // 确保getSystemInfoSync调用成功
        let sysInfo;
        try {
          sysInfo = api.getSystemInfoSync();
        } catch (e) {
          console.error('getSystemInfoSync调用失败:', e);
          sysInfo = {...DEFAULT_SYSTEM_INFO};
        }
        
        // 确保sysInfo不为null
        if (!sysInfo || typeof sysInfo !== 'object') {
          console.warn('getSystemInfoSync返回无效值，使用默认值');
          sysInfo = {...DEFAULT_SYSTEM_INFO};
        }
        
        // 更新全局数据
        this.globalData.systemInfo = sysInfo;
        
        // 提取设备信息
        this.globalData.deviceInfo = {
          brand: sysInfo.brand || DEFAULT_DEVICE_INFO.brand,
          model: sysInfo.model || DEFAULT_DEVICE_INFO.model,
          system: sysInfo.system || DEFAULT_DEVICE_INFO.system,
          platform: sysInfo.platform || DEFAULT_DEVICE_INFO.platform
        };
        
        // 提取窗口信息
        this.globalData.windowInfo = {
          pixelRatio: sysInfo.pixelRatio || DEFAULT_WINDOW_INFO.pixelRatio,
          screenWidth: sysInfo.screenWidth || DEFAULT_WINDOW_INFO.screenWidth,
          screenHeight: sysInfo.screenHeight || DEFAULT_WINDOW_INFO.screenHeight,
          windowWidth: sysInfo.windowWidth || DEFAULT_WINDOW_INFO.windowWidth,
          windowHeight: sysInfo.windowHeight || DEFAULT_WINDOW_INFO.windowHeight,
          statusBarHeight: sysInfo.statusBarHeight || DEFAULT_WINDOW_INFO.statusBarHeight,
          safeArea: sysInfo.safeArea || DEFAULT_WINDOW_INFO.safeArea,
          screenTop: DEFAULT_WINDOW_INFO.screenTop,
          navigatorHeight: DEFAULT_WINDOW_INFO.navigatorHeight
        };
        
        // 提取应用基础信息
        this.globalData.appBaseInfo = {
          SDKVersion: sysInfo.SDKVersion || DEFAULT_APP_BASE_INFO.SDKVersion,
          enableDebug: sysInfo.enableDebug || DEFAULT_APP_BASE_INFO.enableDebug,
          host: DEFAULT_APP_BASE_INFO.host,
          language: sysInfo.language || DEFAULT_APP_BASE_INFO.language,
          version: sysInfo.version || DEFAULT_APP_BASE_INFO.version,
          theme: sysInfo.theme || DEFAULT_APP_BASE_INFO.theme
        };
        
        // 获取胶囊按钮信息
        if (sysInfo.menuButtonBoundingClientRect) {
          this.globalData.menuButtonInfo = sysInfo.menuButtonBoundingClientRect;
        } else {
          this.globalData.menuButtonInfo = DEFAULT_MENU_BUTTON_INFO;
        }
        
        // 计算导航栏高度
        this.calculateNavBarHeight(
          this.globalData.windowInfo,
          this.globalData.menuButtonInfo,
          this.globalData.deviceInfo
        );
        
        return;
      } catch (e) {
        console.error('模拟器环境刷新系统信息失败:', e);
      }
    }
    
    if (!this.globalData.deviceInfo || !this.globalData.windowInfo) {
      this.initSystemInfo().catch(error => {
        console.error('刷新系统信息失败:', error);
      });
    }
  },
  
  calculateNavBarHeight: function(windowInfo: WechatMiniprogram.WindowInfo | null, menuButtonInfo: WechatMiniprogram.Rect | null, deviceInfo: WechatMiniprogram.DeviceInfo | null) {
    console.log('开始计算导航栏高度...');
    
    // 默认值
    let navBarHeight = DEFAULT_NAV_CONFIG.NAV_BAR_HEIGHT;
    let statusBarHeight = DEFAULT_NAV_CONFIG.STATUS_BAR_HEIGHT_NORMAL;
    
    // 检查设备信息是否有效
    const isDeviceInfoValid = deviceInfo && 
                            typeof deviceInfo === 'object' &&
                            typeof deviceInfo.model === 'string';
    
    // 1. 确定状态栏高度
    if (windowInfo && typeof windowInfo.statusBarHeight === 'number') {
      statusBarHeight = windowInfo.statusBarHeight;
      this.globalData.statusBarHeight = statusBarHeight;
      console.log('从windowInfo获取状态栏高度:', statusBarHeight);
    } else if (this.globalData.systemInfo && typeof this.globalData.systemInfo.statusBarHeight === 'number') {
      statusBarHeight = this.globalData.systemInfo.statusBarHeight;
      this.globalData.statusBarHeight = statusBarHeight;
      console.log('从systemInfo获取状态栏高度:', statusBarHeight);
    } else {
      // 根据设备类型判断状态栏高度
      if (isDeviceInfoValid && deviceInfo) {
        const model = deviceInfo.model.toLowerCase();
        // 检测是否为刘海屏iPhone
        if (model.includes('iphone') && 
            (model.includes('x') || model.includes('11') || model.includes('12') || 
             model.includes('13') || model.includes('14') || model.includes('15'))) {
          statusBarHeight = DEFAULT_NAV_CONFIG.STATUS_BAR_HEIGHT; // 44px for notch iPhones
          console.log('检测到刘海屏iPhone，使用44px状态栏高度');
        }
      }
      this.globalData.statusBarHeight = statusBarHeight;
      console.log('使用默认状态栏高度:', statusBarHeight);
    }
    
    // 2. 检查胶囊按钮信息是否有效
    const isMenuButtonInfoValid = menuButtonInfo && 
                                typeof menuButtonInfo.height === 'number' && 
                                typeof menuButtonInfo.top === 'number' &&
                                menuButtonInfo.height > 0 &&
                                menuButtonInfo.top > 0;
    
    if (isMenuButtonInfoValid && menuButtonInfo) {
      // 使用胶囊按钮信息计算导航栏高度
      // 导航栏高度 = 胶囊按钮底部到屏幕顶部的距离 + 底部边距
      const buttonBottom = menuButtonInfo.top + menuButtonInfo.height;
      navBarHeight = buttonBottom + DEFAULT_NAV_CONFIG.MENU_BUTTON_MARGIN;
      
      // 记录详细的计算过程
      console.log('胶囊按钮计算详情:', {
        buttonTop: menuButtonInfo.top,
        buttonHeight: menuButtonInfo.height,
        buttonBottom: buttonBottom,
        extraMargin: DEFAULT_NAV_CONFIG.MENU_BUTTON_MARGIN,
        calculatedNavBarHeight: navBarHeight
      });
      
      console.log('使用胶囊按钮信息计算的导航栏高度:', navBarHeight);
    } else {
      // 使用状态栏高度 + 固定值计算导航栏高度
      const menuButtonHeight = DEFAULT_NAV_CONFIG.MENU_BUTTON_HEIGHT;
      const menuButtonMargin = DEFAULT_NAV_CONFIG.MENU_BUTTON_MARGIN;
      
      navBarHeight = statusBarHeight + menuButtonHeight + 2 * menuButtonMargin;
      
      // 记录详细的计算过程
      console.log('状态栏计算详情:', {
        statusBarHeight: statusBarHeight,
        menuButtonHeight: menuButtonHeight,
        menuButtonMargin: menuButtonMargin,
        calculatedNavBarHeight: navBarHeight
      });
      
      console.log('使用状态栏高度计算的导航栏高度:', navBarHeight);
    }
    
    // 3. 确保导航栏高度在合理范围内
    const originalHeight = navBarHeight;
    
    if (navBarHeight < DEFAULT_NAV_CONFIG.MIN_NAV_BAR_HEIGHT) {
      navBarHeight = DEFAULT_NAV_CONFIG.MIN_NAV_BAR_HEIGHT;
      console.warn(`导航栏高度过小(${originalHeight})，使用最小值:`, navBarHeight);
    }
    
    if (navBarHeight > DEFAULT_NAV_CONFIG.MAX_NAV_BAR_HEIGHT) {
      navBarHeight = DEFAULT_NAV_CONFIG.MAX_NAV_BAR_HEIGHT;
      console.warn(`导航栏高度过大(${originalHeight})，使用最大值:`, navBarHeight);
    }
    
    this.globalData.navBarHeight = navBarHeight;
    
    // 4. 计算内容区域高度
    let contentHeight = DEFAULT_CONTENT_HEIGHT;
    
    if (windowInfo && typeof windowInfo.windowHeight === 'number') {
      contentHeight = windowInfo.windowHeight - navBarHeight;
      console.log('使用windowInfo计算内容高度:', {
        windowHeight: windowInfo.windowHeight,
        navBarHeight: navBarHeight,
        contentHeight: contentHeight
      });
    } else if (this.globalData.systemInfo && typeof this.globalData.systemInfo.windowHeight === 'number') {
      contentHeight = this.globalData.systemInfo.windowHeight - navBarHeight;
      console.log('使用systemInfo计算内容高度:', {
        windowHeight: this.globalData.systemInfo.windowHeight,
        navBarHeight: navBarHeight,
        contentHeight: contentHeight
      });
    } else {
      console.log('使用默认内容高度:', contentHeight);
    }
    
    // 确保内容高度为正数
    if (contentHeight <= 0) {
      contentHeight = DEFAULT_CONTENT_HEIGHT;
      console.warn('计算的内容高度无效，使用默认值:', contentHeight);
    }
    
    this.globalData.contentHeight = contentHeight;
    
    console.log('最终导航栏高度:', navBarHeight, '内容区域高度:', contentHeight);
  }
});