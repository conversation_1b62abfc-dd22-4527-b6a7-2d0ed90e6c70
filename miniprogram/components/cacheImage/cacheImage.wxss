 /* components/cacheImage/cacheImage.wxss */

.cache-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.cache-image {
  width: 100%;
  height: 100%;
  display: block;
  transition: opacity 0.3s;
}

.image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
}

.image-placeholder.loading {
  background-color: #f9f9f9;
}

.image-placeholder.error {
  background-color: #f0f0f0;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.loading-icon {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 0.8s linear infinite;
}

.iconfont.icon-image-error {
  color: #cccccc;
  font-size: 40rpx;
}

.hidden {
  opacity: 0;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}