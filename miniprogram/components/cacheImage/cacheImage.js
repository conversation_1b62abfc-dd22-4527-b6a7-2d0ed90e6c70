 // components/cacheImage/cacheImage.js
import imageCache from '../../utils/imageCache.js'

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 图片路径
    src: {
      type: String,
      value: ''
    },
    // 图片裁剪、缩放的模式
    mode: {
      type: String,
      value: 'aspectFill'
    },
    // 是否懒加载
    lazyLoad: {
      type: Boolean,
      value: true
    },
    // 是否显示加载中的占位符
    showLoading: {
      type: Boolean,
      value: true
    },
    // 是否创建并缓存缩略图
    useThumbnail: {
      type: Boolean,
      value: true
    },
    // 图片加载失败时的占位图
    placeholder: {
      type: String,
      value: ''
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    },
    // 图片类名
    imageClass: {
      type: String,
      value: ''
    },
    // 是否需要预览功能
    previewable: {
      type: Boolean,
      value: false
    },
    // 预览时的图片路径数组
    previewUrls: {
      type: Array,
      value: []
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    error: false,
    loading: true,
    cachedSrc: '', // 缓存的图片路径
    isDefault: false // 是否显示默认图
  },

  /**
   * 组件的生命周期
   */
  lifetimes: {
    attached: function() {
      this.loadImage()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 加载图片
    loadImage: function() {
      const { src, useThumbnail } = this.properties
      
      if (!src) {
        this.setData({
          loading: false,
          error: true,
          isDefault: true
        })
        return
      }
      
      // 尝试从缓存获取图片
      const cachedPath = imageCache.getFromCache(src)
      
      if (cachedPath) {
        // 如果已缓存，直接使用
        this.setData({
          cachedSrc: cachedPath,
          loading: false
        })
      } else {
        // 未缓存，开始加载
        this.setData({ loading: true })
        
        // 将图片保存到缓存
        imageCache.saveToCache(src, { createThumbnail: useThumbnail })
          .then(savedPath => {
            this.setData({
              cachedSrc: savedPath,
              loading: false,
              error: false
            })
          })
          .catch(err => {
            console.error('图片缓存失败:', err)
            // 缓存失败，直接使用原图
            this.setData({
              cachedSrc: src,
              loading: false
            })
          })
      }
    },
    
    // 图片加载失败
    handleError: function() {
      this.setData({
        error: true,
        loading: false,
        isDefault: true
      })
      
      this.triggerEvent('error', { src: this.properties.src })
    },
    
    // 图片加载完成
    handleLoad: function() {
      this.setData({
        loading: false,
        error: false
      })
      
      this.triggerEvent('load', { src: this.data.cachedSrc || this.properties.src })
    },
    
    // 预览图片
    handlePreview: function() {
      if (!this.properties.previewable) return
      
      const urls = this.properties.previewUrls.length > 0 
        ? this.properties.previewUrls 
        : [this.properties.src]
      
      wx.previewImage({
        current: this.properties.src,
        urls
      })
    }
  }
})