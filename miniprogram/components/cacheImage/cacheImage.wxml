 <!-- components/cacheImage/cacheImage.wxml -->
<view class="cache-image-container" style="{{customStyle}}">
  <!-- 加载中占位符 -->
  <view class="image-placeholder {{loading ? 'loading' : ''}}" wx:if="{{loading && showLoading}}">
    <view class="loading-icon"></view>
  </view>
  
  <!-- 错误占位符 -->
  <view class="image-placeholder error" wx:elif="{{error}}" style="{{placeholder ? 'background-image: url(' + placeholder + ');' : ''}}">
    <text class="iconfont icon-image-error" wx:if="{{!placeholder}}"></text>
  </view>
  
  <!-- 实际图片 -->
  <image 
    class="cache-image {{imageClass}} {{error ? 'hidden' : ''}}" 
    src="{{cachedSrc || src}}" 
    mode="{{mode}}" 
    lazy-load="{{lazyLoad}}"
    binderror="handleError" 
    bindload="handleLoad"
    bindtap="handlePreview">
  </image>
</view>