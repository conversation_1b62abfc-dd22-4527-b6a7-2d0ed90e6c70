// components/imageEditor/imageEditor.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    imagePath: {
      type: String,
      value: ''
    },
    rotationAngle: {
      type: Number,
      value: 0
    },
    isFlippedH: {
      type: Boolean,
      value: false
    },
    isFlippedV: {
      type: Boolean,
      value: false
    },
    brightness: {
      type: Number,
      value: 100
    },
    contrast: {
      type: Number,
      value: 100
    },
    cropRatio: {
      type: String,
      value: 'free'
    },
    filterStyle: {
      type: Object,
      value: {}
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    canvasWidth: 300,
    canvasHeight: 300,
    imageWidth: 0,
    imageHeight: 0,
    imageLeft: 0,
    imageTop: 0,
    scale: 1,
    canvas: null,
    ctx: null,
    imageObj: null,
    isImageLoaded: false,
    textItems: [], // 文本项列表
    showTextEditor: false, // 是否显示文本编辑器
    currentEditingTextIndex: -1, // 当前正在编辑的文本索引，-1表示新增
    isDraggingText: false, // 是否正在拖动文本
    dragTextIndex: -1, // 正在拖动的文本索引
    dragStartX: 0, // 拖动起始X
    dragStartY: 0,  // 拖动起始Y
    stickerItems: [], // 贴纸项列表
    isDraggingSticker: false, // 是否正在拖动贴纸
    dragStickerIndex: -1, // 正在拖动的贴纸索引
    isRotatingSticker: false, // 是否正在旋转贴纸
    isScalingSticker: false, // 是否正在缩放贴纸
    lastDistance: 0, // 上次两指间距离，用于计算缩放
    currentFilter: {}, // 当前应用的滤镜样式
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 初始化画布
    initCanvas: function(retryCount = 0) {
      try {
        // 先设置默认尺寸，确保有基础值
        this.setData({
          canvasWidth: 300,
          canvasHeight: 300
        });
        
        const query = this.createSelectorQuery();
        query.select('#imageCanvas')
          .fields({ node: true, size: true })
          .exec((res) => {
            if (!res || !res[0]) {
              console.error('获取画布节点失败，重试次数:', retryCount);
              
              // 最多重试3次
              if (retryCount < 3) {
                setTimeout(() => {
                  this.initCanvas(retryCount + 1);
                }, 300 * (retryCount + 1)); // 递增延迟时间
              } else {
                wx.showToast({
                  title: '画布初始化失败，请重试',
                  icon: 'none'
                });
              }
              return;
            }

            const canvas = res[0].node;
            const ctx = canvas.getContext('2d');
            
            // 保存canvas节点引用，确保后续可以正确访问
            this._canvasNode = canvas;
            
            // 设置画布大小
            let dpr = 1;
            try {
              // 使用新的API替代getSystemInfoSync
              const windowInfo = wx.getWindowInfo();
              dpr = windowInfo.pixelRatio || 1;
            } catch (error) {
              console.error('获取设备像素比失败:', error);
              // 如果新API不可用，尝试使用旧API
              try {
                const systemInfo = {
                  ...wx.getDeviceInfo(),
                  ...wx.getSystemSetting(),
                  ...wx.getAppBaseInfo(),
                  ...wx.getWindowInfo()
                };
                dpr = systemInfo.pixelRatio || 1;
              } catch (fallbackError) {
                console.error('获取设备像素比失败(备用方案):', fallbackError);
                dpr = 1;
              }
            }
            
            // 确保画布尺寸合理
            let canvasWidth = res[0].width;
            let canvasHeight = res[0].height;
            
            if (canvasWidth <= 0 || canvasHeight <= 0) {
              console.error('画布尺寸无效:', canvasWidth, canvasHeight);
              
              // 尝试获取容器尺寸
              try {
                const query = wx.createSelectorQuery().in(this);
                query.select('.image-editor').boundingClientRect(rect => {
                  if (rect && rect.width > 0 && rect.height > 0) {
                    canvasWidth = rect.width;
                    canvasHeight = rect.height;
                  } else {
                    // 使用默认尺寸
                    canvasWidth = 300;
                    canvasHeight = 300;
                  }
                }).exec();
              } catch (error) {
                console.error('获取容器尺寸失败:', error);
                // 使用默认尺寸
                canvasWidth = 300;
                canvasHeight = 300;
              }
              
              // 使用默认尺寸
              canvas.width = canvasWidth * dpr;
              canvas.height = canvasHeight * dpr;
              
              this.setData({
                canvasWidth: canvasWidth,
                canvasHeight: canvasHeight
              }, () => {
                console.log('画布尺寸已设置为默认值:', canvasWidth, canvasHeight);
              });
              
              // 直接保存引用，而不是通过setData
              this._canvasNode = canvas;
              this._canvasContext = ctx;
            } else {
              // 确保尺寸至少为最小值
              canvasWidth = Math.max(res[0].width, 300);
              canvasHeight = Math.max(res[0].height, 300);
              
              canvas.width = canvasWidth * dpr;
              canvas.height = canvasHeight * dpr;
              
              this.setData({
                canvasWidth: canvasWidth,
                canvasHeight: canvasHeight
              }, () => {
                console.log('画布尺寸已设置:', canvasWidth, canvasHeight);
              });
              
              // 直接保存引用，而不是通过setData
              this._canvasNode = canvas;
              this._canvasContext = ctx;
            }
            
            ctx.scale(dpr, dpr);
            
            // 如果已有图片路径，加载图片
            if (this.properties.imagePath) {
              // 延迟一点时间再加载图片，确保canvas已完全初始化
              setTimeout(() => {
                this.loadImage();
              }, 300); // 增加延迟时间，确保画布完全准备好
            }
          });
      } catch (error) {
        console.error('初始化画布失败:', error);
        
        // 最多重试3次
        if (retryCount < 3) {
          setTimeout(() => {
            this.initCanvas(retryCount + 1);
          }, 300 * (retryCount + 1)); // 递增延迟时间
        } else {
          wx.showToast({
            title: '初始化画布失败，请重试',
            icon: 'none'
          });
        }
      }
    },
    
    // 加载图片
    loadImage: function(retryCount = 0) {
      // 检查Canvas是否准备好
      if (!this._canvasNode || !this._canvasContext) {
        console.error('Canvas not ready for loadImage, retryCount:', retryCount);
        
        // 最多重试3次
        if (retryCount < 3) {
          setTimeout(() => {
            this.initCanvas();
            setTimeout(() => {
              this.loadImage(retryCount + 1);
            }, 300);
          }, 500 * (retryCount + 1));
        } else {
          wx.showToast({
            title: '画布初始化失败，请重试',
            icon: 'none'
          });
        }
        return;
      }

      // 检查图片路径是否有效
      if (!this.properties.imagePath) {
        console.error('图片路径为空');
        wx.showToast({
          title: '图片路径无效',
          icon: 'none'
        });
        return;
      }
      
      console.log('开始加载图片:', this.properties.imagePath);

      // 创建图片对象
      const imageObj = this._canvasNode.createImage();
      
      // 设置加载超时
      const loadTimeout = setTimeout(() => {
        console.warn('图片加载超时，尝试备用方法');
        this._loadImageWithFileSystem();
      }, 5000);
      
      // 图片加载成功处理
      imageObj.onload = () => {
        clearTimeout(loadTimeout);
        console.log('图片加载成功，尺寸:', imageObj.width, 'x', imageObj.height);
        
        // 计算图片在画布中的位置和大小
        const imgWidth = imageObj.width;
        const imgHeight = imageObj.height;
        
        // 计算缩放和位置，使图片居中显示
        let scale = 1;
        if (imgWidth > this.data.canvasWidth || imgHeight > this.data.canvasHeight) {
          const scaleX = this.data.canvasWidth / imgWidth;
          const scaleY = this.data.canvasHeight / imgHeight;
          scale = Math.min(scaleX, scaleY);
        }
        
        const scaledWidth = imgWidth * scale;
        const scaledHeight = imgHeight * scale;
        const left = (this.data.canvasWidth - scaledWidth) / 2;
        const top = (this.data.canvasHeight - scaledHeight) / 2;
        
        this.setData({
          imageObj: imageObj,
          imageWidth: scaledWidth,
          imageHeight: scaledHeight,
          imageLeft: left,
          imageTop: top,
          scale: scale,
          isImageLoaded: true
        }, () => {
          // 绘制图片
          this.drawImage();
        });
      };
      
      // 图片加载失败处理
      imageObj.onerror = (error) => {
        clearTimeout(loadTimeout);
        console.error('图片加载失败:', error, '路径:', this.properties.imagePath);
        
        // 检查文件是否存在
        wx.getFileInfo({
          filePath: this.properties.imagePath,
          success: (res) => {
            console.log('文件存在，大小:', res.size);
            // 文件存在但加载失败，可能是格式问题，尝试通过FileSystemManager读取
            this._loadImageWithFileSystem();
          },
          fail: (err) => {
            console.error('文件不存在或无法访问:', err);
            
            // 检查是否是网络图片
            if (this.properties.imagePath.startsWith('http')) {
              console.log('尝试下载网络图片');
              this._downloadAndLoadImage();
            } else {
              wx.showToast({
                title: '图片文件无法访问',
                icon: 'none'
              });
            }
          }
        });
      };
      
      // 设置图片源
      try {
        imageObj.src = this.properties.imagePath;
      } catch (error) {
        console.error('设置图片源失败:', error);
        this._loadImageWithFileSystem();
      }
    },
    
    // 使用文件系统管理器加载图片（备用方法）
    _loadImageWithFileSystem: function() {
      console.log('尝试使用文件系统管理器加载图片');
      const fs = wx.getFileSystemManager();
      
      try {
        // 读取文件为base64
        fs.readFile({
          filePath: this.properties.imagePath,
          encoding: 'base64',
          success: (res) => {
            console.log('文件读取成功，准备创建图片');
            // 创建新的图片对象
            const imageObj = this._canvasNode.createImage();
            
            imageObj.onload = () => {
              console.log('Base64图片加载成功');
              // 计算图片在画布中的位置和大小
              const imgWidth = imageObj.width;
              const imgHeight = imageObj.height;
              
              let scale = 1;
              if (imgWidth > this.data.canvasWidth || imgHeight > this.data.canvasHeight) {
                const scaleX = this.data.canvasWidth / imgWidth;
                const scaleY = this.data.canvasHeight / imgHeight;
                scale = Math.min(scaleX, scaleY);
              }
              
              const scaledWidth = imgWidth * scale;
              const scaledHeight = imgHeight * scale;
              const left = (this.data.canvasWidth - scaledWidth) / 2;
              const top = (this.data.canvasHeight - scaledHeight) / 2;
              
              this.setData({
                imageObj: imageObj,
                imageWidth: scaledWidth,
                imageHeight: scaledHeight,
                imageLeft: left,
                imageTop: top,
                scale: scale,
                isImageLoaded: true
              }, () => {
                // 绘制图片
                this.drawImage();
              });
            };
            
            imageObj.onerror = (err) => {
              console.error('Base64图片加载失败:', err);
              wx.showToast({
                title: '图片加载失败，请重试',
                icon: 'none'
              });
            };
            
            // 尝试确定正确的MIME类型
            let mimeType = 'image/png';
            const filePath = this.properties.imagePath.toLowerCase();
            if (filePath.endsWith('.jpg') || filePath.endsWith('.jpeg')) {
              mimeType = 'image/jpeg';
            } else if (filePath.endsWith('.gif')) {
              mimeType = 'image/gif';
            } else if (filePath.endsWith('.webp')) {
              mimeType = 'image/webp';
            }
            
            // 设置base64图片源
            imageObj.src = `data:${mimeType};base64,` + res.data;
          },
          fail: (err) => {
            console.error('读取文件失败:', err);
            wx.showToast({
              title: '读取图片失败',
              icon: 'none'
            });
          }
        });
      } catch (error) {
        console.error('文件系统操作失败:', error);
        wx.showToast({
          title: '图片处理失败',
          icon: 'none'
        });
      }
    },
    
    // 下载并加载网络图片
    _downloadAndLoadImage: function() {
      console.log('尝试下载网络图片:', this.properties.imagePath);
      
      wx.showLoading({
        title: '下载图片中...'
      });
      
      // 下载图片到临时文件
      wx.downloadFile({
        url: this.properties.imagePath,
        success: (res) => {
          if (res.statusCode === 200) {
            console.log('图片下载成功，临时路径:', res.tempFilePath);
            
            // 更新图片路径并加载
            this.setData({
              tempImagePath: res.tempFilePath
            }, () => {
              // 使用临时文件路径加载图片
              const imageObj = this._canvasNode.createImage();
              
              imageObj.onload = () => {
                console.log('临时图片加载成功');
                wx.hideLoading();
                
                // 计算图片在画布中的位置和大小
                const imgWidth = imageObj.width;
                const imgHeight = imageObj.height;
                
                let scale = 1;
                if (imgWidth > this.data.canvasWidth || imgHeight > this.data.canvasHeight) {
                  const scaleX = this.data.canvasWidth / imgWidth;
                  const scaleY = this.data.canvasHeight / imgHeight;
                  scale = Math.min(scaleX, scaleY);
                }
                
                const scaledWidth = imgWidth * scale;
                const scaledHeight = imgHeight * scale;
                const left = (this.data.canvasWidth - scaledWidth) / 2;
                const top = (this.data.canvasHeight - scaledHeight) / 2;
                
                this.setData({
                  imageObj: imageObj,
                  imageWidth: scaledWidth,
                  imageHeight: scaledHeight,
                  imageLeft: left,
                  imageTop: top,
                  scale: scale,
                  isImageLoaded: true
                }, () => {
                  // 绘制图片
                  this.drawImage();
                });
              };
              
              imageObj.onerror = (err) => {
                console.error('临时图片加载失败:', err);
                wx.hideLoading();
                wx.showToast({
                  title: '图片加载失败',
                  icon: 'none'
                });
              };
              
              imageObj.src = res.tempFilePath;
            });
          } else {
            console.error('图片下载失败，状态码:', res.statusCode);
            wx.hideLoading();
            wx.showToast({
              title: '图片下载失败',
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          console.error('下载图片失败:', err);
          wx.hideLoading();
          wx.showToast({
            title: '网络图片下载失败',
            icon: 'none'
          });
        }
      });
    },
     
     // 绘制图像
    drawImage: function() {
      if (!this._canvasContext || !this.data.isImageLoaded) {
        console.error('绘制图像失败：画布或图像未准备好');
        return;
      }
      
      const ctx = this._canvasContext;
      const img = this.data.imageObj;
      
      // 清空画布
      ctx.clearRect(0, 0, this.data.canvasWidth, this.data.canvasHeight);
      
      // 保存当前状态
      ctx.save();
      
      // 将原点移到图片中心点
      const centerX = this.data.imageLeft + this.data.imageWidth / 2;
      const centerY = this.data.imageTop + this.data.imageHeight / 2;
      ctx.translate(centerX, centerY);
      
      // 根据旋转角度旋转画布
      const angle = this.properties.rotationAngle * Math.PI / 180;
      ctx.rotate(angle);
      
      // 应用翻转
      let scaleX = 1;
      let scaleY = 1;
      if (this.properties.isFlippedH) {
        scaleX = -1;
      }
      if (this.properties.isFlippedV) {
        scaleY = -1;
      }
      ctx.scale(scaleX, scaleY);
      
      // 应用亮度和对比度
      let filterString = `brightness(${this.properties.brightness}%) contrast(${this.properties.contrast}%)`;
      
      // 应用当前滤镜
      if (this.data.currentFilter) {
        const filter = this.data.currentFilter;
        
        if (filter.saturate) filterString = `saturate(${filter.saturate}) ` + filterString;
        if (filter.contrast) filterString = `contrast(${filter.contrast}) ` + filterString;
        if (filter.brightness) filterString = `brightness(${filter.brightness}) ` + filterString;
        if (filter.sepia) filterString = `sepia(${filter.sepia}) ` + filterString;
        if (filter.hueRotate) filterString = `hue-rotate(${filter.hueRotate}deg) ` + filterString;
      }
      
      ctx.filter = filterString;
      
      // 处理裁剪比例
      let drawWidth = this.data.imageWidth;
      let drawHeight = this.data.imageHeight;
      
      // 根据裁剪比例调整绘制尺寸
      if (this.properties.cropRatio !== 'free') {
        let ratio = 1;
        if (this.properties.cropRatio === '1:1') {
          ratio = 1;
        } else if (this.properties.cropRatio === '4:3') {
          ratio = 4/3;
        } else if (this.properties.cropRatio === '16:9') {
          ratio = 16/9;
        }
        
        // 根据原始图片的宽高比和目标比例，决定如何裁剪
        const originalRatio = drawWidth / drawHeight;
        
        if (originalRatio > ratio) {
          // 原图更宽，需要裁剪宽度
          drawWidth = drawHeight * ratio;
        } else if (originalRatio < ratio) {
          // 原图更高，需要裁剪高度
          drawHeight = drawWidth / ratio;
        }
      }
      
      // 绘制图片
      ctx.drawImage(
        img, 
        -drawWidth / 2, 
        -drawHeight / 2, 
        drawWidth, 
        drawHeight
      );
      
      // 恢复保存的状态
      ctx.restore();
      
      // 绘制文本
      this.drawTextItems();
      
      // 绘制贴纸
      this.drawStickerItems();
    },
    
    // 绘制文本项
    drawTextItems: function() {
      if (!this._canvasContext || this.data.textItems.length === 0) return;
      
      const ctx = this._canvasContext;
      
      this.data.textItems.forEach((item, index) => {
        ctx.save();
        
        // 设置字体样式
        let fontStyle = '';
        if (item.bold) fontStyle += 'bold ';
        if (item.italic) fontStyle += 'italic ';
        
        ctx.font = `${fontStyle}${item.fontSize}px ${item.fontFamily}`;
        ctx.fillStyle = item.color;
        ctx.textAlign = item.alignment;
        
        // 添加描边效果
        if (item.useStroke) {
          ctx.strokeStyle = item.strokeColor;
          ctx.lineWidth = item.fontSize * 0.05; // 描边宽度为字号的5%
          ctx.strokeText(item.text, item.x, item.y);
        }
        
        // 绘制文本
        ctx.fillText(item.text, item.x, item.y);
        
        ctx.restore();
      });
    },
    
    // 绘制贴纸项
    drawStickerItems: function() {
      if (!this._canvasContext || this.data.stickerItems.length === 0) return;
      
      const ctx = this._canvasContext;
      
      this.data.stickerItems.forEach((item, index) => {
        ctx.save();
        
        // 移动到贴纸中心点
        ctx.translate(item.x, item.y);
        
        // 应用旋转
        ctx.rotate(item.rotation * Math.PI / 180);
        
        // 应用缩放
        ctx.scale(item.scale, item.scale);
        
        // 绘制贴纸
        ctx.drawImage(
          item.image, 
          -item.width / 2, 
          -item.height / 2, 
          item.width, 
          item.height
        );
        
        ctx.restore();
      });
    },
    
    // 应用锐化效果
    applySharpen: function() {
      if (!this.data.isImageLoaded || !this._canvasContext) return;
      
      const ctx = this._canvasContext;
      const imageData = ctx.getImageData(0, 0, this.data.canvasWidth, this.data.canvasHeight);
      
      // 创建锐化卷积核
      const kernel = [
        0, -1, 0,
        -1, 5, -1,
        0, -1, 0
      ];
      
      const sharpenedData = this.convolute(imageData, kernel);
      ctx.putImageData(sharpenedData, 0, 0);
      
      // 重新绘制文本，因为锐化会覆盖文本
      this.drawTextItems();
      this.drawStickerItems();
    },
    
    // 卷积操作
    convolute: function(imageData, kernel) {
      const side = Math.round(Math.sqrt(kernel.length));
      const halfSide = Math.floor(side / 2);
      
      const src = imageData.data;
      const sw = imageData.width;
      const sh = imageData.height;
      
      // 创建输出图像数据
      const output = this._canvasContext.createImageData(sw, sh);
      const dst = output.data;
      
      // 遍历输出图像的每个像素
      for (let y = 0; y < sh; y++) {
        for (let x = 0; x < sw; x++) {
          const dstOff = (y * sw + x) * 4;
          
          // 计算当前像素的卷积结果
          let r = 0, g = 0, b = 0, a = 0;
          let weightSum = 0; // 记录实际使用的权重和
          
          for (let cy = 0; cy < side; cy++) {
            for (let cx = 0; cx < side; cx++) {
              const scy = y + cy - halfSide;
              const scx = x + cx - halfSide;
              
              if (scy >= 0 && scy < sh && scx >= 0 && scx < sw) {
                const srcOff = (scy * sw + scx) * 4;
                const wt = kernel[cy * side + cx];
                
                r += src[srcOff] * wt;
                g += src[srcOff + 1] * wt;
                b += src[srcOff + 2] * wt;
                a += src[srcOff + 3];
                weightSum += wt;
              }
            }
          }
          
          // 调整结果，确保边缘像素不会出现异常
          if (weightSum !== 0) {
            // 如果在边缘，则根据实际使用的权重调整
            dst[dstOff] = Math.max(0, Math.min(255, r));
            dst[dstOff + 1] = Math.max(0, Math.min(255, g));
            dst[dstOff + 2] = Math.max(0, Math.min(255, b));
          } else {
            // 如果完全在边缘外，则保持原始像素值
            dst[dstOff] = src[dstOff];
            dst[dstOff + 1] = src[dstOff + 1];
            dst[dstOff + 2] = src[dstOff + 2];
          }
          dst[dstOff + 3] = src[dstOff + 3]; // 保持原始 alpha
        }
      }
      
      return output;
    },
    
    // 打开文本编辑器
    openTextEditor: function() {
      this.setData({
        showTextEditor: true,
        currentEditingTextIndex: -1 // 新增文本
      });
    },
    
    // 编辑现有文本
    editText: function(e) {
      const index = e.currentTarget.dataset.index;
      this.setData({
        showTextEditor: true,
        currentEditingTextIndex: index
      });
    },
    
    // 关闭文本编辑器
    closeTextEditor: function() {
      this.setData({
        showTextEditor: false
      });
    },
    
    // 处理文本确认事件
    onTextConfirm: function(e) {
      const { textItems } = e.detail;
      this.setData({ textItems }, () => {
        this.drawImage(); // 重绘图片和文本
      });
    },
    
    // 开始拖动文本
    onTextTouchStart: function(e) {
      const index = e.currentTarget.dataset.index;
      this.setData({
        isDraggingText: true,
        dragTextIndex: index,
        dragStartX: e.touches[0].x,
        dragStartY: e.touches[0].y
      });
    },
    
    // 拖动文本
    onTextTouchMove: function(e) {
      if (!this.data.isDraggingText) return;
      
      const moveX = e.touches[0].x - this.data.dragStartX;
      const moveY = e.touches[0].y - this.data.dragStartY;
      
      const textItems = [...this.data.textItems];
      const index = this.data.dragTextIndex;
      
      if (textItems[index]) {
        textItems[index].x += moveX;
        textItems[index].y += moveY;
        
        this.setData({
          textItems: textItems,
          dragStartX: e.touches[0].x,
          dragStartY: e.touches[0].y
        });
        
        this.drawImage(); // 重绘图片和文本
      }
    },
    
    // 结束拖动文本
    onTextTouchEnd: function() {
      this.setData({
        isDraggingText: false,
        dragTextIndex: -1
      });
    },
    
    // 删除文本
    deleteText: function(e) {
      const index = e.currentTarget.dataset.index;
      const textItems = [...this.data.textItems];
      textItems.splice(index, 1);
      
      this.setData({ textItems }, () => {
        this.drawImage(); // 重绘图片和文本
      });
    },
    
    // 生成最终图片
    generateImage: function() {
      return new Promise((resolve, reject) => {
        if (!this.data.isImageLoaded) {
          reject(new Error('图片尚未加载'));
          return;
        }
        
        // 确保使用正确的canvas节点
        if (!this._canvasNode) {
          console.error('Canvas节点未初始化');
          reject(new Error('Canvas节点未初始化'));
          return;
        }
        
        wx.canvasToTempFilePath({
          canvas: this._canvasNode,
          success: (res) => {
            console.log('canvasToTempFilePath成功:', res.tempFilePath);
            resolve(res.tempFilePath);
          },
          fail: (error) => {
            console.error('canvasToTempFilePath失败:', error);
            reject(error);
          }
        }, this);
      });
    },
    
    // 当属性变化时，重绘图片
    observeProps: function() {
      if (this.data.isImageLoaded) {
        this.drawImage();
      }
    },
    
    // 开始拖动贴纸
    onStickerTouchStart: function(e) {
      const index = e.currentTarget.dataset.index;
      
      // 判断是否点击了旋转控制点
      const sticker = this.data.stickerItems[index];
      const touchX = e.touches[0].x;
      const touchY = e.touches[0].y;
      
      // 检查是否是双指触摸（用于缩放）
      if (e.touches.length === 2) {
        const dx = e.touches[1].x - e.touches[0].x;
        const dy = e.touches[1].y - e.touches[0].y;
        this.setData({
          isScalingSticker: true,
          dragStickerIndex: index,
          lastDistance: Math.sqrt(dx * dx + dy * dy)
        });
        return;
      }
      
      // 检查是否点击了旋转控制点（控制点在贴纸右上方20px处）
      const rotateHandleX = sticker.x + sticker.width * sticker.scale / 2;
      const rotateHandleY = sticker.y - sticker.height * sticker.scale / 2;
      const distance = Math.sqrt(
        Math.pow(touchX - rotateHandleX, 2) + 
        Math.pow(touchY - rotateHandleY, 2)
      );
      
      if (distance <= 20) {
        // 点击了旋转控制点
        this.setData({
          isRotatingSticker: true,
          dragStickerIndex: index,
          dragStartX: touchX,
          dragStartY: touchY
        });
      } else {
        // 拖动贴纸
        this.setData({
          isDraggingSticker: true,
          dragStickerIndex: index,
          dragStartX: touchX,
          dragStartY: touchY
        });
      }
    },
    
    // 拖动贴纸
    onStickerTouchMove: function(e) {
      if (!this.data.isDraggingSticker && !this.data.isRotatingSticker && !this.data.isScalingSticker) return;
      
      const stickerItems = [...this.data.stickerItems];
      const index = this.data.dragStickerIndex;
      
      // 处理缩放
      if (this.data.isScalingSticker && e.touches.length === 2) {
        const dx = e.touches[1].x - e.touches[0].x;
        const dy = e.touches[1].y - e.touches[0].y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        // 计算缩放比例变化
        const scale = distance / this.data.lastDistance;
        
        stickerItems[index].scale *= scale;
        
        // 限制最小和最大缩放
        stickerItems[index].scale = Math.max(0.2, Math.min(3, stickerItems[index].scale));
        
        this.setData({
          stickerItems: stickerItems,
          lastDistance: distance
        });
        
        this.drawImage(); // 重绘
        return;
      }
      
      // 处理旋转
      if (this.data.isRotatingSticker) {
        const touchX = e.touches[0].x;
        const touchY = e.touches[0].y;
        
        // 计算与贴纸中心的角度
        const centerX = stickerItems[index].x;
        const centerY = stickerItems[index].y;
        
        const startAngle = Math.atan2(
          this.data.dragStartY - centerY, 
          this.data.dragStartX - centerX
        );
        
        const endAngle = Math.atan2(
          touchY - centerY, 
          touchX - centerX
        );
        
        // 角度变化（弧度转角度）
        let rotation = (endAngle - startAngle) * 180 / Math.PI;
        
        // 更新贴纸旋转角度
        stickerItems[index].rotation = (stickerItems[index].rotation + rotation) % 360;
        
        this.setData({
          stickerItems: stickerItems,
          dragStartX: touchX,
          dragStartY: touchY
        });
        
        this.drawImage(); // 重绘
        return;
      }
      
      // 处理拖动
      if (this.data.isDraggingSticker) {
        const moveX = e.touches[0].x - this.data.dragStartX;
        const moveY = e.touches[0].y - this.data.dragStartY;
        
        stickerItems[index].x += moveX;
        stickerItems[index].y += moveY;
        
        this.setData({
          stickerItems: stickerItems,
          dragStartX: e.touches[0].x,
          dragStartY: e.touches[0].y
        });
        
        this.drawImage(); // 重绘
      }
    },
    
    // 结束拖动贴纸
    onStickerTouchEnd: function() {
      this.setData({
        isDraggingSticker: false,
        isRotatingSticker: false,
        isScalingSticker: false,
        dragStickerIndex: -1
      });
    },
    
    // 添加贴纸
    addSticker: function(stickerPath) {
      if (!this._canvasNode) {
        console.error('Canvas not ready for addSticker');
        return;
      }
      
      // 创建贴纸图片对象
      const stickerImg = this._canvasNode.createImage();
      
      stickerImg.onload = () => {
        // 计算保持宽高比的尺寸
        const aspectRatio = stickerImg.width / stickerImg.height;
        const width = 100;
        const height = width / aspectRatio;
        
        // 创建新贴纸对象
        const stickerObj = {
          image: stickerImg,
          path: stickerPath,
          x: this.data.canvasWidth / 2,  // 初始位置为画布中心
          y: this.data.canvasHeight / 2,
          width: width,  // 初始大小
          height: height,
          rotation: 0,  // 初始旋转角度
          scale: 1      // 初始缩放比例
        };
        
        // 添加到贴纸列表
        const stickerItems = [...this.data.stickerItems, stickerObj];
        this.setData({ stickerItems }, () => {
          // 重新绘制画布
          this.drawImage();
        });
      };
      
      stickerImg.onerror = (err) => {
        console.error('加载贴纸失败:', err);
        wx.showToast({
          title: '加载贴纸失败',
          icon: 'none'
        });
      };
      
      // 设置图片源
      stickerImg.src = stickerPath;
    },
    
    // 删除贴纸
    deleteSticker: function(index) {
      const stickerItems = [...this.data.stickerItems];
      stickerItems.splice(index, 1);
      
      this.setData({ stickerItems }, () => {
        this.drawImage(); // 重绘画布
      });
    },

    // 应用滤镜
    applyFilter: function(filterStyle) {
      this.setData({
        currentFilter: filterStyle || {}
      }, () => {
        this.drawImage();
      });
    },
  },
  
  observers: {
    'imagePath': function(newPath) {
      console.log('图片路径变化:', newPath);
      
      // 清除之前的图片加载状态和定时器
      if (this.data.isImageLoaded) {
        this.setData({ isImageLoaded: false });
      }
      
      if (this._loadImageTimer) {
        clearTimeout(this._loadImageTimer);
        this._loadImageTimer = null;
      }
      
      // 检查路径是否有效
      if (!newPath) {
        console.warn('图片路径为空');
        return;
      }
      
      // 检查Canvas是否已初始化
      if (!this._canvasContext || !this._canvasNode) {
        console.log('Canvas尚未初始化，等待初始化完成后加载图片');
        // 如果Canvas未初始化，尝试初始化
        if (!this._canvasInitializing) {
          this._canvasInitializing = true;
          setTimeout(() => {
            this.initCanvas();
            this._canvasInitializing = false;
          }, 300);
        }
        return;
      }
      
      // 延迟一点时间再加载图片，避免频繁加载
      this._loadImageTimer = setTimeout(() => {
        this.loadImage();
      }, 300);
    },
    'rotationAngle, isFlippedH, isFlippedV, brightness, contrast, cropRatio': function() {
      this.observeProps();
    }
  },
  
  lifetimes: {
    attached: function() {
      this.initCanvas();
    }
  }
});