<view class="image-editor">
  <canvas type="2d" id="imageCanvas" class="editor-canvas"></canvas>
  
  <!-- 文本项列表，用于拖动和编辑 -->
  <view class="text-overlays">
    <view 
      class="text-overlay" 
      wx:for="{{textItems}}" 
      wx:key="index"
      data-index="{{index}}"
      style="left: {{item.x}}px; top: {{item.y}}px; color: {{item.color}}; font-size: {{item.fontSize}}px;"
      bindtouchstart="onTextTouchStart"
      bindtouchmove="onTextTouchMove"
      bindtouchend="onTextTouchEnd"
      bindtap="editText">
      <view class="text-controls">
        <view class="text-delete" catchtap="deleteText" data-index="{{index}}">×</view>
      </view>
      {{item.text}}
    </view>
  </view>
  
  <!-- 悬浮的添加文本按钮 -->
  <view class="add-text-btn" bindtap="openTextEditor">
    <view class="add-text-icon">T+</view>
  </view>
  
  <!-- 文字编辑器组件 -->
  <text-editor 
    visible="{{showTextEditor}}"
    textItems="{{textItems}}"
    canvasWidth="{{canvasWidth}}"
    canvasHeight="{{canvasHeight}}"
    currentEditingIndex="{{currentEditingTextIndex}}"
    bind:close="closeTextEditor"
    bind:textconfirm="onTextConfirm">
  </text-editor>
</view> 