.image-editor {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.editor-canvas {
  width: 100%;
  height: 100%;
  min-width: 300px;
  min-height: 300px;
  background-color: #000;
}

/* 文本叠加层 */
.text-overlays {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.text-overlay {
  position: absolute;
  transform: translate(-50%, -50%); /* 使文本中心点对准指定位置 */
  padding: 5px;
  min-width: 40rpx;
  min-height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: auto;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.8);
  user-select: none;
}

.text-controls {
  position: absolute;
  top: -20px;
  right: -20px;
  display: flex;
}

.text-delete {
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(255, 0, 0, 0.8);
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
}

/* 添加文本按钮 */
.add-text-btn {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 80rpx;
  height: 80rpx;
  background-color: #07c160;
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.add-text-icon {
  font-size: 32rpx;
  font-weight: bold;
}