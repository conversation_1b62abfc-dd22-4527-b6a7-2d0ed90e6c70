<view class="text-editor-container" wx:if="{{visible}}">
  <view class="text-editor">
    <view class="editor-header">
      <text class="editor-title">添加文字</text>
      <view class="close-btn" bindtap="cancelTextAdd">×</view>
    </view>
    
    <scroll-view class="editor-content" scroll-y>
      <!-- 文本输入框 -->
      <view class="input-group">
        <textarea class="text-input" placeholder="请输入文字内容" value="{{currentText}}" bindinput="onInputChange" maxlength="100"></textarea>
      </view>
      
      <!-- 快捷输入 -->
      <view class="quick-input-section">
        <view class="section-title">快捷输入</view>
        <view class="quick-inputs">
          <button class="quick-btn" bindtap="insertDate">插入日期</button>
          <picker mode="selector" range="{{mealTypes}}" bindchange="selectMealType">
            <button class="quick-btn">选择餐次: {{selectedMealType || '请选择'}}</button>
          </picker>
          <button class="quick-btn" bindtap="insertMealType">插入餐次</button>
        </view>
      </view>
      
      <!-- 模板选择 -->
      <view class="templates-section">
        <view class="section-title">文字模板</view>
        <view class="templates-list">
          <view class="template-item" wx:for="{{templates}}" wx:key="index" data-index="{{index}}" bindtap="applyTemplate">
            {{item}}
          </view>
        </view>
        
        <!-- 自定义模板 -->
        <view class="custom-template">
          <input class="template-input" placeholder="添加自定义模板" value="{{customTemplate}}" bindinput="onCustomTemplateChange"></input>
          <button class="template-save-btn" bindtap="saveCustomTemplate">保存</button>
        </view>
        
        <!-- 学校名称设置 -->
        <view class="school-name-setting">
          <text class="setting-label">学校名称:</text>
          <input class="school-name-input" placeholder="用于替换模板中的{学校名}" value="{{schoolName}}" bindinput="setSchoolName"></input>
        </view>
      </view>
      
      <!-- 字体样式设置 -->
      <view class="style-section">
        <view class="section-title">字体样式</view>
        
        <!-- 字号 -->
        <view class="font-size">
          <text class="setting-label">字体大小:</text>
          <slider min="12" max="72" value="{{fontSize}}" show-value block-size="20" bindchange="onFontSizeChange"></slider>
        </view>
        
        <!-- 字体选择 -->
        <view class="font-family">
          <text class="setting-label">字体:</text>
          <picker mode="selector" range="{{['微软雅黑', '宋体', '黑体', '楷体']}}" bindchange="selectFontFamily">
            <view class="picker-view">{{fontFamily || '微软雅黑'}}</view>
          </picker>
        </view>
        
        <!-- 文本对齐 -->
        <view class="text-align">
          <text class="setting-label">对齐方式:</text>
          <view class="align-options">
            <view class="align-option {{alignment === 'left' ? 'active' : ''}}" data-align="left" bindtap="selectAlignment">左</view>
            <view class="align-option {{alignment === 'center' ? 'active' : ''}}" data-align="center" bindtap="selectAlignment">中</view>
            <view class="align-option {{alignment === 'right' ? 'active' : ''}}" data-align="right" bindtap="selectAlignment">右</view>
          </view>
        </view>
        
        <!-- 文本样式 -->
        <view class="text-style">
          <text class="setting-label">样式:</text>
          <view class="style-options">
            <view class="style-option {{bold ? 'active' : ''}}" bindtap="toggleBold">粗体</view>
            <view class="style-option {{italic ? 'active' : ''}}" bindtap="toggleItalic">斜体</view>
            <view class="style-option {{useStroke ? 'active' : ''}}" bindtap="toggleStroke">描边</view>
          </view>
        </view>
        
        <!-- 颜色选择 -->
        <view class="color-section">
          <text class="setting-label">文字颜色:</text>
          <view class="color-options">
            <view class="color-option" 
                  wx:for="{{colorOptions}}" 
                  wx:key="index"
                  style="background-color: {{item}};" 
                  data-color="{{item}}"
                  bindtap="selectColor">
              <view class="color-selected" wx:if="{{item === fontColor}}"></view>
            </view>
          </view>
          
          <!-- 描边颜色，仅当启用描边时显示 -->
          <block wx:if="{{useStroke}}">
            <text class="setting-label">描边颜色:</text>
            <view class="color-options">
              <view class="color-option" 
                    wx:for="{{colorOptions}}" 
                    wx:key="index"
                    style="background-color: {{item}};" 
                    data-color="{{item}}"
                    bindtap="selectStrokeColor">
                <view class="color-selected" wx:if="{{item === strokeColor}}"></view>
              </view>
            </view>
          </block>
        </view>
      </view>
      
      <!-- 文本预览 -->
      <view class="text-preview-section">
        <view class="section-title">预览</view>
        <view class="text-preview" 
              style="color: {{fontColor}}; font-size: {{fontSize}}px; text-align: {{alignment}}; font-family: {{fontFamily}}; font-weight: {{bold ? 'bold' : 'normal'}}; font-style: {{italic ? 'italic' : 'normal'}}; text-shadow: {{useStroke ? '1px 1px 2px ' + strokeColor + ', -1px -1px 2px ' + strokeColor + ', 1px -1px 2px ' + strokeColor + ', -1px 1px 2px ' + strokeColor : 'none'}};">
          {{currentText || '文字预览'}}
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部按钮 -->
    <view class="editor-footer">
      <button class="cancel-btn" bindtap="cancelTextAdd">取消</button>
      <button class="confirm-btn" bindtap="confirmTextAdd">确定</button>
    </view>
  </view>
</view> 