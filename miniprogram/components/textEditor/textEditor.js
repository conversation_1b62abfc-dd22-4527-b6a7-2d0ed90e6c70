// components/textEditor/textEditor.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    textItems: {
      type: Array,
      value: []
    },
    canvasWidth: {
      type: Number,
      value: 300
    },
    canvasHeight: {
      type: Number,
      value: 300
    },
    currentEditingIndex: {
      type: Number,
      value: -1
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    currentText: '',
    fontSize: 24,
    fontColor: '#ffffff',
    strokeColor: '#000000',
    useStroke: true,
    fontFamily: 'sans-serif',
    alignment: 'center',
    bold: false,
    italic: false,
    mealTypes: ['早餐', '午餐', '晚餐', '加餐'],
    selectedMealType: '',
    templates: [
      '今日菜单',
      '今日营养餐',
      '今日校园餐',
      '{学校名}今日{餐次}'
    ],
    schoolName: '',
    customTemplate: '',
    colorOptions: [
      '#ffffff', '#ff0000', '#ffff00', '#00ff00', 
      '#00ffff', '#0000ff', '#ff00ff', '#000000'
    ]
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached: function() {
      // 设置当前日期
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const day = now.getDate();
      
      // 根据当前时间判断可能的用餐时间
      let mealType = '';
      const hour = now.getHours();
      if (hour < 10) {
        mealType = '早餐';
      } else if (hour < 14) {
        mealType = '午餐';
      } else {
        mealType = '晚餐';
      }
      
      this.setData({ 
        currentDate: `${year}年${month}月${day}日`,
        selectedMealType: mealType
      });
      
      // 如果正在编辑现有文本
      if (this.properties.currentEditingIndex >= 0 && 
          this.properties.textItems[this.properties.currentEditingIndex]) {
        const item = this.properties.textItems[this.properties.currentEditingIndex];
        this.setData({
          currentText: item.text,
          fontSize: item.fontSize || this.data.fontSize,
          fontColor: item.color || this.data.fontColor,
          strokeColor: item.strokeColor || this.data.strokeColor,
          useStroke: item.useStroke !== undefined ? item.useStroke : this.data.useStroke,
          fontFamily: item.fontFamily || this.data.fontFamily,
          alignment: item.alignment || this.data.alignment,
          bold: item.bold || false,
          italic: item.italic || false
        });
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 输入文本变化
    onInputChange: function(e) {
      this.setData({
        currentText: e.detail.value
      });
    },
    
    // 改变字体大小
    onFontSizeChange: function(e) {
      this.setData({
        fontSize: parseInt(e.detail.value)
      });
    },
    
    // 改变字体颜色
    selectColor: function(e) {
      const color = e.currentTarget.dataset.color;
      this.setData({
        fontColor: color
      });
    },
    
    // 改变描边颜色
    selectStrokeColor: function(e) {
      const color = e.currentTarget.dataset.color;
      this.setData({
        strokeColor: color
      });
    },
    
    // 切换是否使用描边
    toggleStroke: function() {
      this.setData({
        useStroke: !this.data.useStroke
      });
    },
    
    // 切换粗体
    toggleBold: function() {
      this.setData({
        bold: !this.data.bold
      });
    },
    
    // 切换斜体
    toggleItalic: function() {
      this.setData({
        italic: !this.data.italic
      });
    },
    
    // 选择对齐方式
    selectAlignment: function(e) {
      const alignment = e.currentTarget.dataset.align;
      this.setData({
        alignment: alignment
      });
    },
    
    // 选择字体
    selectFontFamily: function(e) {
      const fontFamily = e.detail.value;
      this.setData({
        fontFamily: fontFamily
      });
    },
    
    // 选择餐次
    selectMealType: function(e) {
      const mealType = this.data.mealTypes[e.detail.value];
      this.setData({
        selectedMealType: mealType
      });
    },
    
    // 插入日期
    insertDate: function() {
      this.setData({
        currentText: this.data.currentText + this.data.currentDate
      });
    },
    
    // 插入餐次
    insertMealType: function() {
      this.setData({
        currentText: this.data.currentText + this.data.selectedMealType
      });
    },
    
    // 设置学校名称
    setSchoolName: function(e) {
      this.setData({
        schoolName: e.detail.value
      });
    },
    
    // 应用模板
    applyTemplate: function(e) {
      const templateIndex = e.currentTarget.dataset.index;
      let template = this.data.templates[templateIndex];
      
      // 替换模板中的变量
      template = template
        .replace('{学校名}', this.data.schoolName || '学校')
        .replace('{餐次}', this.data.selectedMealType || '餐次')
        .replace('{日期}', this.data.currentDate || '日期');
      
      this.setData({
        currentText: template
      });
    },
    
    // 自定义模板输入
    onCustomTemplateChange: function(e) {
      this.setData({
        customTemplate: e.detail.value
      });
    },
    
    // 保存自定义模板
    saveCustomTemplate: function() {
      if (this.data.customTemplate) {
        const templates = this.data.templates;
        templates.push(this.data.customTemplate);
        this.setData({
          templates: templates,
          customTemplate: ''
        });
      }
    },
    
    // 确认添加文字
    confirmTextAdd: function() {
      // 创建文本对象
      const textItem = {
        text: this.data.currentText,
        fontSize: this.data.fontSize,
        color: this.data.fontColor,
        strokeColor: this.data.strokeColor,
        useStroke: this.data.useStroke,
        fontFamily: this.data.fontFamily,
        bold: this.data.bold,
        italic: this.data.italic,
        alignment: this.data.alignment,
        x: this.properties.canvasWidth / 2, // 默认放在中间
        y: this.properties.canvasHeight / 2
      };
      
      let textItems = [...this.properties.textItems];
      
      // 如果是编辑现有文本
      if (this.properties.currentEditingIndex >= 0) {
        textItems[this.properties.currentEditingIndex] = textItem;
      } else {
        // 添加新文本
        textItems.push(textItem);
      }
      
      // 触发事件，将文本对象传递给父组件
      this.triggerEvent('textconfirm', {
        textItems: textItems,
        editIndex: this.properties.currentEditingIndex
      });
      
      // 关闭文本编辑器
      this.triggerEvent('close');
    },
    
    // 取消添加文字
    cancelTextAdd: function() {
      this.triggerEvent('close');
    }
  }
}); 