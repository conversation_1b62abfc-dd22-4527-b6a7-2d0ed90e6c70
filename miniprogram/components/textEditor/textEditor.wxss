.text-editor-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.text-editor {
  width: 90%;
  max-width: 600rpx;
  height: 85%;
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.editor-header {
  height: 90rpx;
  background-color: #f8f8f8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx;
  border-bottom: 1px solid #eee;
}

.editor-title {
  font-size: 34rpx;
  font-weight: bold;
}

.close-btn {
  font-size: 40rpx;
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
}

.editor-content {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.input-group {
  margin-bottom: 20rpx;
}

.text-input {
  width: 100%;
  height: 120rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  padding: 10rpx;
  box-sizing: border-box;
  font-size: 28rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  margin: 20rpx 0 10rpx;
  color: #333;
}

.quick-input-section {
  margin-bottom: 20rpx;
}

.quick-inputs {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.quick-btn {
  flex: 1;
  min-width: 160rpx;
  margin: 0;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 24rpx;
  padding: 0 10rpx;
}

.templates-section {
  margin-bottom: 20rpx;
}

.templates-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.template-item {
  background-color: #f0f0f0;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
  font-size: 24rpx;
  color: #333;
}

.custom-template {
  display: flex;
  margin-top: 15rpx;
  align-items: center;
}

.template-input {
  flex: 1;
  height: 60rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  padding: 0 10rpx;
  font-size: 24rpx;
}

.template-save-btn {
  width: 120rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 24rpx;
  margin-left: 10rpx;
  padding: 0;
}

.school-name-setting {
  margin-top: 15rpx;
}

.setting-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.school-name-input {
  height: 60rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  padding: 0 10rpx;
  font-size: 24rpx;
  width: 100%;
}

.style-section {
  margin-bottom: 20rpx;
}

.font-size {
  margin-bottom: 15rpx;
}

.font-family {
  margin-bottom: 15rpx;
  display: flex;
  align-items: center;
}

.picker-view {
  flex: 1;
  border: 1px solid #ddd;
  padding: 10rpx 20rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  margin-left: 10rpx;
}

.text-align {
  margin-bottom: 15rpx;
}

.align-options {
  display: flex;
  margin-top: 5rpx;
}

.align-option {
  flex: 1;
  text-align: center;
  border: 1px solid #ddd;
  padding: 8rpx 0;
  font-size: 24rpx;
}

.align-option:first-child {
  border-radius: 6rpx 0 0 6rpx;
}

.align-option:last-child {
  border-radius: 0 6rpx 6rpx 0;
}

.align-option.active {
  background-color: #07c160;
  color: #fff;
  border-color: #07c160;
}

.text-style {
  margin-bottom: 15rpx;
}

.style-options {
  display: flex;
  margin-top: 5rpx;
}

.style-option {
  flex: 1;
  text-align: center;
  border: 1px solid #ddd;
  padding: 8rpx 0;
  margin-right: 10rpx;
  font-size: 24rpx;
  border-radius: 6rpx;
}

.style-option:last-child {
  margin-right: 0;
}

.style-option.active {
  background-color: #07c160;
  color: #fff;
  border-color: #07c160;
}

.color-section {
  margin-bottom: 15rpx;
}

.color-options {
  display: flex;
  flex-wrap: wrap;
  margin-top: 5rpx;
  gap: 10rpx;
}

.color-option {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: 1px solid #ddd;
  position: relative;
  box-sizing: border-box;
}

.color-selected {
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #fff;
  border: 1px solid #666;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.text-preview-section {
  margin-bottom: 40rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}

.text-preview {
  padding: 20rpx;
  min-height: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  word-break: break-all;
}

.editor-footer {
  height: 90rpx;
  display: flex;
  border-top: 1px solid #eee;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 100%;
  line-height: 90rpx;
  margin: 0;
  border-radius: 0;
  font-size: 30rpx;
}

.cancel-btn {
  color: #333;
  background-color: #f8f8f8;
}

.confirm-btn {
  color: white;
  background-color: #07c160;
} 