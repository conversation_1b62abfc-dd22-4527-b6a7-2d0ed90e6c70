 Component({
  options: {
    multipleSlots: true // 在组件定义时的选项中启用多slot支持
  },
  /**
   * 组件的属性列表
   */
  properties: {
    extClass: {
      type: String,
      value: ''
    },
    title: {
      type: String,
      value: ''
    },
    background: {
      type: String,
      value: ''
    },
    color: {
      type: String,
      value: ''
    },
    back: {
      type: Boolean,
      value: true
    },
    loading: {
      type: Boolean,
      value: false
    },
    homeButton: {
      type: Boolean,
      value: false,
    },
    animated: {
      // 显示隐藏的时候opacity动画效果
      type: Boolean,
      value: true
    },
    show: {
      // 显示隐藏导航，隐藏的时候navigation-bar的高度占位还在
      type: Boolean,
      value: true,
      observer: '_showChange'
    },
    // back为true的时候，返回的页面深度
    delta: {
      type: Number,
      value: 1
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    displayStyle: '',
    statusBarHeight: 20,
    navBarHeight: 44
  },
  lifetimes: {
    attached() {
      try {
        // 获取胶囊按钮位置信息
        let rect;
        try {
          if (typeof wx.getMenuButtonBoundingClientRect === 'function') {
            rect = wx.getMenuButtonBoundingClientRect();
            // 检查返回值是否有效
            if (!rect || typeof rect !== 'object' || rect.width === undefined) {
              throw new Error('Invalid menu button rect');
            }
          } else {
            throw new Error('getMenuButtonBoundingClientRect API not available');
          }
        } catch (rectError) {
          console.warn('获取胶囊按钮位置失败，使用默认值:', rectError);
          rect = {
            left: 281,
            width: 87,
            height: 32,
            top: 24,
            bottom: 56
          };
        }

        // 获取窗口信息
        let windowInfo;
        try {
          if (typeof wx.getWindowInfo === 'function') {
            windowInfo = wx.getWindowInfo();
            // 检查返回值是否有效
            if (!windowInfo || typeof windowInfo !== 'object' || windowInfo.windowWidth === undefined) {
              throw new Error('Invalid window info');
            }
          } else {
            throw new Error('getWindowInfo API not available');
          }
        } catch (windowError) {
          console.warn('获取窗口信息失败，使用默认值:', windowError);
          windowInfo = {
            windowWidth: 375,
            windowHeight: 667,
            statusBarHeight: 20,
            safeArea: { top: 20 },
            platform: 'devtools'
          };
        }

        // 获取设备信息
        let deviceInfo;
        try {
          if (typeof wx.getDeviceInfo === 'function') {
            deviceInfo = wx.getDeviceInfo();
            // 检查返回值是否有效
            if (!deviceInfo || typeof deviceInfo !== 'object' || deviceInfo.platform === undefined) {
              throw new Error('Invalid device info');
            }
          } else {
            throw new Error('getDeviceInfo API not available');
          }
        } catch (deviceError) {
          console.warn('获取设备信息失败，使用默认值:', deviceError);
          deviceInfo = {
            platform: 'devtools'
          };
        }

        // 尝试从全局数据获取信息
        try {
          const app = getApp<IAppOption>();
          if (app && app.globalData) {
            const globalData = app.globalData;
            
            // 如果全局数据中有这些信息，优先使用全局数据
            if (globalData.menuButtonInfo && typeof globalData.menuButtonInfo === 'object') {
              rect = globalData.menuButtonInfo;
            }
            
            if (globalData.systemInfo && typeof globalData.systemInfo === 'object') {
              if (globalData.systemInfo.statusBarHeight !== undefined) {
                windowInfo.statusBarHeight = globalData.systemInfo.statusBarHeight;
              }
              
              if (globalData.systemInfo.platform !== undefined) {
                deviceInfo.platform = globalData.systemInfo.platform;
              }
            }
          }
        } catch (globalError) {
          console.warn('获取全局数据失败:', globalError);
        }

        const isAndroid = deviceInfo.platform === 'android';
        const isDevtools = deviceInfo.platform === 'devtools';

        // 计算导航栏高度
        // 胶囊按钮上下边距各为 4px，导航栏底部边距为 6px
        const navBarHeight = (rect.bottom - rect.top) + 10;

        // 设置导航栏样式
        const navBarStyle = isAndroid ? 'android' : '';
        const displayStyle = `--nav-bar-height: ${navBarHeight}px; --status-bar-height: ${windowInfo.statusBarHeight}px;`;
        
        // 确保 windowInfo.windowWidth 和 rect.left 是有效的数值
        const windowWidth = typeof windowInfo.windowWidth === 'number' ? windowInfo.windowWidth : 375;
        const rectLeft = typeof rect.left === 'number' ? rect.left : 281;
        
        this.setData({
          ios: !isAndroid,
          statusBarHeight: windowInfo.statusBarHeight || 20,
          navBarHeight: navBarHeight,
          displayStyle: displayStyle,
          innerPaddingRight: `padding-right: ${windowWidth - rectLeft}px`,
          leftWidth: `width: ${windowWidth - rectLeft}px`,
          safeAreaTop: isDevtools || isAndroid ? 
            `height: calc(${navBarHeight}px + ${windowInfo.safeArea?.top || 20}px); padding-top: ${windowInfo.safeArea?.top || 20}px` : 
            ''
        });
      } catch (error) {
        console.error('导航栏初始化失败:', error);
        // 使用默认值
        this.setData({
          ios: true,
          statusBarHeight: 20,
          navBarHeight: 44,
          displayStyle: '--nav-bar-height: 44px; --status-bar-height: 20px;',
          innerPaddingRight: 'padding-right: 87px',
          leftWidth: 'width: 288px',
          safeAreaTop: ''
        });
      }
    },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    _showChange(show: boolean) {
      const animated = this.data.animated
      let displayStyle = ''
      if (animated) {
        displayStyle = `opacity: ${
          show ? '1' : '0'
        };transition:opacity 0.5s;`
      } else {
        displayStyle = `display: ${show ? '' : 'none'}`
      }
      this.setData({
        displayStyle
      })
    },
    back() {
      const data = this.data
      if (data.delta) {
        wx.navigateBack({
          delta: data.delta
        })
      }
      this.triggerEvent('back', { delta: data.delta }, {})
    }
  },
})