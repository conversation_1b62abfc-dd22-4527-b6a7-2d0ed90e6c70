Component({
  options: {
    multipleSlots: true // 在组件定义时的选项中启用多slot支持
  },
  /**
   * 组件的属性列表
   */
  properties: {
    extClass: {
      type: String,
      value: ''
    },
    title: {
      type: String,
      value: ''
    },
    background: {
      type: String,
      value: ''
    },
    color: {
      type: String,
      value: ''
    },
    back: {
      type: Boolean,
      value: true
    },
    loading: {
      type: Boolean,
      value: false
    },
    homeButton: {
      type: Boolean,
      value: false,
    },
    animated: {
      // 显示隐藏的时候opacity动画效果
      type: Boolean,
      value: true
    },
    show: {
      // 显示隐藏导航，隐藏的时候navigation-bar的高度占位还在
      type: Boolean,
      value: true,
      observer: '_showChange'
    },
    // back为true的时候，返回的页面深度
    delta: {
      type: Number,
      value: 1
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    displayStyle: '',
    statusBarHeight: 20,
    navBarHeight: 44
  },
  attached() {
    try {
      // 获取系统信息 - 增加兼容性处理
      let deviceInfo = {};
      let windowInfo = {};
      
      // 尝试使用新API，如果不支持则回退到旧API
      try {
        deviceInfo = {
          ...wx.getDeviceInfo(),
          ...wx.getSystemSetting(),
          ...wx.getAppBaseInfo(),
          ...wx.getWindowInfo()
        };
        windowInfo = deviceInfo;
      } catch (apiError) {
        console.log('新API不可用，使用旧API替代');
        // 回退到旧API
        const sysInfo = wx.getSystemInfoSync();
        deviceInfo = sysInfo;
        windowInfo = sysInfo;
      }
      
      // 获取菜单按钮（右上角胶囊按钮）的位置信息
      let rect = wx.getMenuButtonBoundingClientRect ? wx.getMenuButtonBoundingClientRect() : null;
      
      // 如果获取失败，使用默认值
      if (!rect) {
        rect = {
          bottom: 56,
          height: 32,
          left: 281,
          right: 368,
          top: 24,
          width: 87
        };
      }
      
      // 尝试从全局数据获取信息
      try {
        const app = getApp();
        if (app && app.globalData) {
          const globalData = app.globalData;
          
          // 如果全局数据中有这些信息，优先使用全局数据
          if (globalData.menuButtonInfo && typeof globalData.menuButtonInfo === 'object') {
            rect = globalData.menuButtonInfo;
          }
          
          if (globalData.systemInfo && typeof globalData.systemInfo === 'object') {
            if (globalData.systemInfo.statusBarHeight !== undefined) {
              windowInfo.statusBarHeight = globalData.systemInfo.statusBarHeight;
            }
            
            if (globalData.systemInfo.platform !== undefined) {
              deviceInfo.platform = globalData.systemInfo.platform;
            }
          }
        }
      } catch (globalError) {
        console.warn('获取全局数据失败:', globalError);
      }

      const isAndroid = deviceInfo.platform === 'android';
      const isDevtools = deviceInfo.platform === 'devtools';

      // 计算导航栏高度
      // 胶囊按钮上下边距各为 4px，导航栏底部边距为 6px
      const navBarHeight = (rect.bottom - rect.top) + 10;

      // 设置导航栏样式
      const navBarStyle = isAndroid ? 'android' : '';
      const displayStyle = `--nav-bar-height: ${navBarHeight}px; --status-bar-height: ${windowInfo.statusBarHeight}px;`;
      
      // 确保 windowInfo.windowWidth 和 rect.left 是有效的数值
      const windowWidth = typeof windowInfo.windowWidth === 'number' ? windowInfo.windowWidth : 375;
      const rectLeft = typeof rect.left === 'number' ? rect.left : 281;
      
      this.setData({
        ios: !isAndroid,
        statusBarHeight: windowInfo.statusBarHeight || 20,
        navBarHeight: navBarHeight,
        displayStyle: displayStyle,
        innerPaddingRight: `padding-right: ${windowWidth - rectLeft}px`,
        leftWidth: `width: ${windowWidth - rectLeft}px`,
        safeAreaTop: isDevtools || isAndroid ? 
          `height: calc(${navBarHeight}px + ${windowInfo.safeArea?.top || 20}px); padding-top: ${windowInfo.safeArea?.top || 20}px` : 
          ''
      });
    } catch (error) {
      console.error('导航栏初始化失败:', error);
      // 使用默认值
      this.setData({
        ios: true,
        statusBarHeight: 20,
        navBarHeight: 44,
        displayStyle: '--nav-bar-height: 44px; --status-bar-height: 20px;',
        innerPaddingRight: 'padding-right: 87px',
        leftWidth: 'width: 288px',
        safeAreaTop: ''
      });
    }
  },
  
  _showChange(show) {
    const animated = this.data.animated;
    let displayStyle = '';
    if (animated) {
      displayStyle = `opacity: ${show ? '1' : '0'};transition:opacity 0.5s;`;
    } else {
      displayStyle = `display: ${show ? '' : 'none'}`;
    }
    this.setData({
      displayStyle
    });
  },
  
  /**
   * 组件的方法列表
   */
  methods: {
    back() {
      const data = this.data;
      if (data.back) {
        const delta = data.delta;
        wx.navigateBack({
          delta,
          fail() {
            // 如果返回失败，跳转到首页（tabBar页面）
            wx.switchTab({
              url: '/pages/index/index',
              fail() {
                // 如果switchTab也失败，尝试使用reLaunch
                wx.reLaunch({
                  url: '/pages/index/index'
                });
              }
            });
          }
        });
      }
      this.triggerEvent('back', { delta }, {});
    }
  }
})