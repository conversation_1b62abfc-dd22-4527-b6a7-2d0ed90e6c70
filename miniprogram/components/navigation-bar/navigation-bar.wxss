.navigation-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: #fff;
  z-index: 99;
}

.navigation-bar-inner {
  position: relative;
  display: flex;
  align-items: center;
  padding: 0 16px;
  box-sizing: border-box;
  width: 100%;
}

.left {
  position: relative;
  width: 88rpx;
  height: 100%;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 24px;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='24' viewBox='0 0 12 24'%3E  %3Cpath fill-opacity='.9' fill-rule='evenodd' d='M10 19.438L8.955 20.5l-7.666-7.79a1.02 1.02 0 0 1 0-1.42L8.955 3.5 10 4.563 2.682 12 10 19.438z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}

.center {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 500;
}

.right {
  width: 88rpx;
  height: 100%;
}
