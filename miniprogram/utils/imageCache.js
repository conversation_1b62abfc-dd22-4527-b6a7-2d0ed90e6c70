// utils/imageCache.js

/**
 * 图片缓存管理工具
 * 用于优化图片加载性能、减少存储占用和提高应用响应速度
 */
class ImageCache {
    constructor() {
      // 缓存配置
      this.config = {
        maxCacheSize: 100 * 1024 * 1024, // 最大缓存大小，默认100MB
        maxCacheAge: 7 * 24 * 60 * 60 * 1000, // 最大缓存时间，默认7天
        thumbnailWidth: 200, // 缩略图宽度
        thumbnailQuality: 80, // 缩略图质量
        cacheKeyPrefix: 'IMG_CACHE_',
        metaStorageKey: 'IMAGE_CACHE_META',
        isInitialized: false
      }
      
      // 初始化缓存元数据
      try {
        this.cacheMeta = wx.getStorageSync(this.config.metaStorageKey) || {
          totalSize: 0,
          items: {}
        }
      } catch (error) {
        console.error('初始化缓存元数据失败:', error)
        this.cacheMeta = {
          totalSize: 0,
          items: {}
        }
      }
    }

    /**
     * 初始化缓存系统
     */
    init() {
      if (this.config.isInitialized) {
        return;
      }

      try {
        // 确保缓存目录存在
        const fs = wx.getFileSystemManager()
        const cacheDir = `${wx.env.USER_DATA_PATH}/imageCache`
        try {
          fs.accessSync(cacheDir)
        } catch (e) {
          fs.mkdirSync(cacheDir, true)
        }

        // 清理过期缓存
        this._cleanExpiredCache().catch(err => {
          console.error('清理过期缓存失败:', err)
        })

        this.config.isInitialized = true
      } catch (error) {
        console.error('初始化图片缓存系统失败:', error)
        wx.showToast({
          title: '初始化缓存系统失败',
          icon: 'none'
        })
      }
    }
    
    /**
     * 保存图片到本地缓存
     * @param {string} imagePath - 图片路径
     * @param {Object} options - 缓存选项
     * @returns {Promise<string>} - 返回缓存后的图片路径
     */
    async saveToCache(imagePath, options = {}) {
      if (!imagePath) return Promise.reject('图片路径不能为空')
      
      try {
        // 生成缓存键
        const cacheKey = this._generateCacheKey(imagePath)
        
        // 检查是否已缓存
        if (this._isImageCached(cacheKey)) {
          return Promise.resolve(this._getLocalPath(cacheKey))
        }
        
        // 获取图片信息
        const imageInfo = await this._getImageInfo(imagePath)
        
        // 检查是否需要创建缩略图
        let targetPath = imagePath
        if (options.createThumbnail && imageInfo.width > this.config.thumbnailWidth) {
          targetPath = await this._createThumbnail(imagePath)
        }
        
        // 保存图片到本地
        const savedPath = await this._saveImageFile(targetPath, cacheKey)
        
        // 获取文件大小
        const fileInfo = await this._getFileInfo(savedPath)
        
        // 更新缓存元数据
        this._updateCacheMeta(cacheKey, {
          path: savedPath,
          size: fileInfo.size,
          width: imageInfo.width,
          height: imageInfo.height,
          timestamp: Date.now()
        })
        
        // 检查并清理缓存
        this._cleanCacheIfNeeded()
        
        return Promise.resolve(savedPath)
      } catch (error) {
        console.error('保存图片到缓存失败:', error)
        return Promise.reject(error)
      }
    }
    
    /**
     * 从缓存获取图片
     * @param {string} imagePath - 原始图片路径
     * @returns {string|null} - 缓存的图片路径或null
     */
    getFromCache(imagePath) {
      if (!imagePath) return null
      
      const cacheKey = this._generateCacheKey(imagePath)
      
      if (this._isImageCached(cacheKey)) {
        // 更新访问时间
        this.cacheMeta.items[cacheKey].lastAccessed = Date.now()
        wx.setStorageSync(this.config.metaStorageKey, this.cacheMeta)
        
        return this._getLocalPath(cacheKey)
      }
      
      return null
    }
    
    /**
     * 批量预加载图片到缓存
     * @param {Array<string>} imagePaths - 图片路径数组
     * @param {Object} options - 缓存选项
     * @returns {Promise<Array>} - 处理结果
     */
    async preloadImages(imagePaths, options = {}) {
      if (!Array.isArray(imagePaths) || imagePaths.length === 0) {
        return Promise.resolve([])
      }
      
      const tasks = imagePaths.map(path => this.saveToCache(path, options))
      return Promise.all(tasks.map(p => p.catch(err => ({ error: err }))))
    }
    
    /**
     * 清理过期或超量的缓存
     * @returns {Promise<boolean>} - 清理结果
     */
    async cleanCache() {
      try {
        // 清理过期缓存
        await this._cleanExpiredCache()
        
        // 如果仍然超出大小限制，则按最近最少使用原则清理
        if (this.cacheMeta.totalSize > this.config.maxCacheSize) {
          await this._cleanLRUCache()
        }
        
        return Promise.resolve(true)
      } catch (error) {
        console.error('清理缓存失败:', error)
        return Promise.reject(error)
      }
    }
    
    /**
     * 获取缓存状态
     * @returns {Object} - 缓存状态信息
     */
    getCacheStatus() {
      return {
        totalSize: this.cacheMeta.totalSize,
        itemCount: Object.keys(this.cacheMeta.items).length,
        maxSize: this.config.maxCacheSize,
        usagePercent: (this.cacheMeta.totalSize / this.config.maxCacheSize) * 100
      }
    }
    
    /**
     * 生成缓存键
     * @private
     * @param {string} imagePath - 图片路径
     * @returns {string} - 缓存键
     */
    _generateCacheKey(imagePath) {
      // 简单哈希函数
      let hash = 0
      for (let i = 0; i < imagePath.length; i++) {
        const char = imagePath.charCodeAt(i)
        hash = ((hash << 5) - hash) + char
        hash = hash & hash // 转换为32位整数
      }
      
      return this.config.cacheKeyPrefix + Math.abs(hash).toString(16)
    }
    
    /**
     * 检查图片是否已缓存
     * @private
     * @param {string} cacheKey - 缓存键
     * @returns {boolean} - 是否已缓存
     */
    _isImageCached(cacheKey) {
      const item = this.cacheMeta.items[cacheKey]
      if (!item) return false
      
      // 检查缓存是否过期
      const now = Date.now()
      if (now - item.timestamp > this.config.maxCacheAge) {
        // 过期了，需要重新缓存
        this._removeCacheItem(cacheKey)
        return false
      }
      
      // 检查文件是否存在
      try {
        const fileInfo = wx.getFileSystemManager().statSync(item.path)
        return fileInfo.isFile()
      } catch (e) {
        // 文件不存在，清除缓存记录
        this._removeCacheItem(cacheKey)
        return false
      }
    }
    
    /**
     * 获取本地缓存路径
     * @private
     * @param {string} cacheKey - 缓存键
     * @returns {string} - 本地路径
     */
    _getLocalPath(cacheKey) {
      return this.cacheMeta.items[cacheKey]?.path || ''
    }
    
    /**
     * 获取图片信息
     * @private
     * @param {string} imagePath - 图片路径
     * @returns {Promise<Object>} - 图片信息
     */
    _getImageInfo(imagePath) {
      return new Promise((resolve, reject) => {
        wx.getImageInfo({
          src: imagePath,
          success: resolve,
          fail: reject
        })
      })
    }
    
    /**
     * 创建图片缩略图
     * @private
     * @param {string} imagePath - 图片路径
     * @returns {Promise<string>} - 缩略图路径
     */
    _createThumbnail(imagePath) {
      return new Promise((resolve, reject) => {
        wx.compressImage({
          src: imagePath,
          quality: this.config.thumbnailQuality,
          compressedWidth: this.config.thumbnailWidth,
          success: res => resolve(res.tempFilePath),
          fail: reject
        })
      })
    }
    
    /**
     * 保存图片文件
     * @private
     * @param {string} imagePath - 图片路径
     * @param {string} cacheKey - 缓存键
     * @returns {Promise<string>} - 保存后的路径
     */
    _saveImageFile(imagePath, cacheKey) {
      return new Promise((resolve, reject) => {
        const fs = wx.getFileSystemManager()
        const savedPath = `${wx.env.USER_DATA_PATH}/${cacheKey}.jpg`
        
        fs.copyFile({
          srcPath: imagePath,
          destPath: savedPath,
          success: () => resolve(savedPath),
          fail: reject
        })
      })
    }
    
    /**
     * 获取文件信息
     * @private
     * @param {string} filePath - 文件路径
     * @returns {Promise<Object>} - 文件信息
     */
    _getFileInfo(filePath) {
      return new Promise((resolve, reject) => {
        const fs = wx.getFileSystemManager()
        fs.stat({
          path: filePath,
          success: res => resolve(res.stats),
          fail: reject
        })
      })
    }
    
    /**
     * 更新缓存元数据
     * @private
     * @param {string} cacheKey - 缓存键
     * @param {Object} itemData - 项数据
     */
    _updateCacheMeta(cacheKey, itemData) {
      // 如果该键已存在，先减去原来的大小
      if (this.cacheMeta.items[cacheKey]) {
        this.cacheMeta.totalSize -= this.cacheMeta.items[cacheKey].size || 0
      }
      
      // 添加或更新缓存项
      this.cacheMeta.items[cacheKey] = {
        ...itemData,
        lastAccessed: Date.now()
      }
      
      // 更新总大小
      this.cacheMeta.totalSize += itemData.size || 0
      
      // 保存元数据
      wx.setStorageSync(this.config.metaStorageKey, this.cacheMeta)
    }
    
    /**
     * 移除缓存项
     * @private
     * @param {string} cacheKey - 缓存键
     * @returns {Promise<boolean>} - 删除结果
     */
    async _removeCacheItem(cacheKey) {
      try {
        const item = this.cacheMeta.items[cacheKey]
        if (!item) return Promise.resolve(false)
        
        // 删除文件
        const fs = wx.getFileSystemManager()
        await new Promise((resolve, reject) => {
          fs.unlink({
            filePath: item.path,
            success: resolve,
            fail: (err) => {
              // 如果文件不存在，视为成功
              if (err.errMsg.includes('no such file or directory')) {
                resolve()
              } else {
                reject(err)
              }
            }
          })
        })
        
        // 更新缓存元数据
        this.cacheMeta.totalSize -= item.size || 0
        delete this.cacheMeta.items[cacheKey]
        wx.setStorageSync(this.config.metaStorageKey, this.cacheMeta)
        
        return Promise.resolve(true)
      } catch (error) {
        console.error('移除缓存项失败:', error)
        return Promise.reject(error)
      }
    }
    
    /**
     * 清理过期缓存
     * @private
     * @returns {Promise<number>} - 清理的数量
     */
    async _cleanExpiredCache() {
      const now = Date.now()
      const expiredKeys = Object.keys(this.cacheMeta.items).filter(key => 
        now - this.cacheMeta.items[key].timestamp > this.config.maxCacheAge
      )
      
      // 并发删除过期项
      const results = await Promise.all(
        expiredKeys.map(key => this._removeCacheItem(key).catch(() => false))
      )
      
      return Promise.resolve(results.filter(Boolean).length)
    }
    
    /**
     * 按最近最少使用原则清理缓存
     * @private
     * @returns {Promise<number>} - 清理的数量
     */
    async _cleanLRUCache() {
      // 按最后访问时间排序
      const sortedItems = Object.keys(this.cacheMeta.items)
        .map(key => ({
          key,
          lastAccessed: this.cacheMeta.items[key].lastAccessed,
          size: this.cacheMeta.items[key].size || 0
        }))
        .sort((a, b) => a.lastAccessed - b.lastAccessed)
      
      let clearedCount = 0
      let clearedSize = 0
      const targetSize = this.cacheMeta.totalSize - this.config.maxCacheSize * 0.7
      
      // 逐个删除，直到缓存大小降到目标值以下
      for (const item of sortedItems) {
        if (clearedSize >= targetSize) break
        
        try {
          await this._removeCacheItem(item.key)
          clearedCount++
          clearedSize += item.size
        } catch (error) {
          console.error('清理LRU缓存项失败:', error)
        }
      }
      
      return Promise.resolve(clearedCount)
    }
    
    /**
     * 如果需要则清理缓存
     * @private
     */
    async _cleanCacheIfNeeded() {
      // 如果缓存大小超过80%，启动清理
      if (this.cacheMeta.totalSize > this.config.maxCacheSize * 0.8) {
        this.cleanCache().catch(err => console.error('自动清理缓存失败:', err))
      }
    }
  }
  
  // 创建单例
  const imageCache = new ImageCache()
  
  module.exports = imageCache