 // utils/cacheManager.js
const imageCache = require('./imageCache.js')

/**
 * 缓存管理器
 * 用于管理各种缓存资源和优化存储空间
 */
class CacheManager {
  constructor() {
    // 缓存配置
    this.config = {
      autoCleanInterval: 24 * 60 * 60 * 1000, // 自动清理间隔，默认24小时
      lastCleanTime: 0,
      isInitialized: false
    }
    
    try {
      this.config.lastCleanTime = wx.getStorageSync('LAST_CACHE_CLEAN_TIME') || 0
    } catch (error) {
      console.error('读取缓存清理时间失败:', error)
    }
  }
  
  /**
   * 初始化缓存管理器
   */
  init() {
    try {
      if (this.config.isInitialized) {
        return;
      }

      // 检查是否需要自动清理
      this.checkAutoClean()
      
      // 初始化图片缓存
      imageCache.init()
      
      this.config.isInitialized = true
    } catch (error) {
      console.error('初始化缓存管理器失败:', error)
      wx.showToast({
        title: '初始化缓存失败',
        icon: 'none'
      })
    }
  }
  
  /**
   * 检查是否需要自动清理
   */
  async checkAutoClean() {
    try {
      const now = Date.now()
      if (now - this.config.lastCleanTime > this.config.autoCleanInterval) {
        await this.cleanAllCache()
        // 更新最后清理时间
        try {
          wx.setStorageSync('LAST_CACHE_CLEAN_TIME', now)
          this.config.lastCleanTime = now
        } catch (error) {
          console.error('保存缓存清理时间失败:', error)
        }
      }
    } catch (error) {
      console.error('检查自动清理失败:', error)
    }
  }
  
  /**
   * 预加载图片到缓存
   * @param {Array<string>} imagePaths - 图片路径数组
   * @param {Object} options - 预加载选项
   * @returns {Promise<Array>} - 处理结果
   */
  preloadImages(imagePaths, options = {}) {
    try {
      return imageCache.preloadImages(imagePaths, options)
    } catch (error) {
      console.error('预加载图片失败:', error)
      return Promise.reject(error)
    }
  }
  
  /**
   * 清理所有缓存
   * @returns {Promise<Object>} - 清理结果
   */
  async cleanAllCache() {
    try {
      // 清理图片缓存
      const imageResult = await imageCache.cleanCache()
      
      // 清理其他缓存（可根据需要扩展）
      // ...
      
      return {
        success: true,
        imageResult
      }
    } catch (error) {
      console.error('清理所有缓存失败:', error)
      return {
        success: false,
        error
      }
    }
  }
  
  /**
   * 获取缓存状态
   * @returns {Object} - 缓存状态
   */
  getCacheStatus() {
    try {
      const imageStatus = imageCache.getCacheStatus()
      
      return {
        success: true,
        image: imageStatus,
        lastCleanTime: this.config.lastCleanTime
      }
    } catch (error) {
      console.error('获取缓存状态失败:', error)
      return {
        success: false,
        error
      }
    }
  }
  
  /**
   * 计算本地存储空间占用
   * @returns {Promise<Object>} - 存储空间信息
   */
  getStorageInfo() {
    return new Promise((resolve, reject) => {
      wx.getStorageInfo({
        success: (res) => {
          resolve({
            success: true,
            ...res
          })
        },
        fail: (error) => {
          console.error('获取存储信息失败:', error)
          resolve({
            success: false,
            error
          })
        }
      })
    })
  }
  
  /**
   * 计算文件系统空间占用
   * @returns {Object} - 文件系统信息
   */
  getFileSystemInfo() {
    try {
      const fs = wx.getFileSystemManager()
      const res = fs.statSync(wx.env.USER_DATA_PATH)
      return {
        success: true,
        ...res
      }
    } catch (error) {
      console.error('获取文件系统信息失败:', error)
      return {
        success: false,
        error
      }
    }
  }
}

// 创建单例
const cacheManager = new CacheManager()

module.exports = cacheManager