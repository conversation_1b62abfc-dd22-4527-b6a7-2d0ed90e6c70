/**app.wxss**/
/* 引入字体图标 */
@import "./styles/iconfont.wxss";

/* 全局样式变量 - 浅色主题 */
page {
  /* 主色调 */
  --color-primary: #FF7D2C;
  --color-primary-light: #FF9F5A;
  --color-primary-dark: #E56B1F;
  
  /* 辅助色 */
  --color-success: #4CAF50;
  --color-info: #2196F3;
  --color-warning: #FFD600;
  --color-danger: #F44336;
  
  /* 文本颜色 */
  --color-text-primary: #333333;
  --color-text-regular: #666666;
  --color-text-secondary: #999999;
  --color-text-placeholder: #CCCCCC;
  
  /* 背景色 */
  --color-bg-base: #FFFFFF;
  --color-bg-light: #F5F5F5;
  --color-bg-lighter: #FAFAFA;
  
  /* 边框颜色 */
  --color-border: #EEEEEE;
  --color-border-light: #F2F2F2;
  
  /* 阴影 */
  --shadow-base: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-card: 0 4px 8px rgba(0, 0, 0, 0.08);
  
  /* 圆角 */
  --border-radius-small: 4px;
  --border-radius-base: 8px;
  --border-radius-large: 12px;
  --border-radius-circle: 50%;
  
  /* 间距 */
  --spacing-mini: 4px;
  --spacing-small: 8px;
  --spacing-base: 12px;
  --spacing-large: 16px;
  --spacing-xl: 24px;
  
  /* 字体大小 */
  --font-size-mini: 12px;
  --font-size-small: 14px;
  --font-size-base: 16px;
  --font-size-large: 18px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;
  
  /* 行高 */
  --line-height-tight: 1.3;
  --line-height-base: 1.5;
  --line-height-loose: 1.7;
  
  /* 操作元素高度 */
  --height-mini: 28px;
  --height-small: 32px;
  --height-base: 40px;
  --height-large: 44px;
  --height-xl: 56px;
  
  /* 设置页面背景色和字体 */
  background-color: var(--color-bg-base);
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
}

/* 深色模式样式变量 */
page.dark-mode {
  /* 主色调 */
  --color-primary: #FF9F5A;
  --color-primary-light: #FFBB86;
  --color-primary-dark: #E56B1F;
  
  /* 文本颜色 */
  --color-text-primary: #FFFFFF;
  --color-text-regular: #CCCCCC;
  --color-text-secondary: #AAAAAA;
  --color-text-placeholder: #666666;
  
  /* 背景色 */
  --color-bg-base: #121212;
  --color-bg-light: #1E1E1E;
  --color-bg-lighter: #2C2C2C;
  
  /* 边框颜色 */
  --color-border: #3D3D3D;
  --color-border-light: #333333;
  
  /* 阴影 */
  --shadow-base: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-card: 0 4px 8px rgba(0, 0, 0, 0.4);
}

/* 高对比度模式 */
page.high-contrast {
  --color-primary: #FF5500;
  --color-text-primary: #000000;
  --color-text-regular: #000000;
  --color-text-secondary: #333333;
  --color-bg-base: #FFFFFF;
  --color-bg-light: #F0F0F0;
  --color-border: #000000;
}

/* 高对比度深色模式 */
page.high-contrast.dark-mode {
  --color-primary: #FFAA00;
  --color-text-primary: #FFFFFF;
  --color-text-regular: #FFFFFF;
  --color-text-secondary: #DDDDDD;
  --color-bg-base: #000000;
  --color-bg-light: #1A1A1A;
  --color-border: #FFFFFF;
}

/* 大字体模式 */
page.large-text {
  --font-size-mini: 14px;
  --font-size-small: 16px;
  --font-size-base: 18px;
  --font-size-large: 20px;
  --font-size-xl: 22px;
  --font-size-xxl: 26px;
}

/* 常用布局类 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  box-sizing: border-box;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-grow {
  flex-grow: 1;
}

/* 常用文本样式 */
.text-primary {
  color: var(--color-primary);
}

.text-secondary {
  color: var(--color-text-secondary);
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-bold {
  font-weight: bold;
}

.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.text-multi-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 常用间距类 */
.m-mini { margin: var(--spacing-mini); }
.m-small { margin: var(--spacing-small); }
.m-base { margin: var(--spacing-base); }
.m-large { margin: var(--spacing-large); }
.m-xl { margin: var(--spacing-xl); }

.mt-mini { margin-top: var(--spacing-mini); }
.mt-small { margin-top: var(--spacing-small); }
.mt-base { margin-top: var(--spacing-base); }
.mt-large { margin-top: var(--spacing-large); }
.mt-xl { margin-top: var(--spacing-xl); }

.mb-mini { margin-bottom: var(--spacing-mini); }
.mb-small { margin-bottom: var(--spacing-small); }
.mb-base { margin-bottom: var(--spacing-base); }
.mb-large { margin-bottom: var(--spacing-large); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.p-mini { padding: var(--spacing-mini); }
.p-small { padding: var(--spacing-small); }
.p-base { padding: var(--spacing-base); }
.p-large { padding: var(--spacing-large); }
.p-xl { padding: var(--spacing-xl); }

/* 常用边框和圆角 */
.border {
  border: 1px solid var(--color-border);
}

.border-top {
  border-top: 1px solid var(--color-border);
}

.border-bottom {
  border-bottom: 1px solid var(--color-border);
}

.radius-small {
  border-radius: var(--border-radius-small);
}

.radius-base {
  border-radius: var(--border-radius-base);
}

.radius-large {
  border-radius: var(--border-radius-large);
}

.radius-circle {
  border-radius: var(--border-radius-circle);
}

/* 常用阴影 */
.shadow {
  box-shadow: var(--shadow-base);
}

.shadow-card {
  box-shadow: var(--shadow-card);
}

/* 主要按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--spacing-large);
  font-size: var(--font-size-base);
  border-radius: calc(var(--height-large) / 2);
  height: var(--height-large);
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.btn::after {
  display: none;
}

.btn:active {
  opacity: 0.8;
  transform: translateY(1px);
}

.btn-primary {
  background-color: var(--color-primary);
  color: #ffffff;
}

.btn-outline {
  background-color: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}

.btn-text {
  background-color: transparent;
  color: var(--color-primary);
  padding: 0;
}

.btn-icon {
  width: var(--height-large);
  padding: 0;
  border-radius: var(--border-radius-circle);
}

.btn-small {
  height: var(--height-small);
  font-size: var(--font-size-small);
  padding: 0 var(--spacing-base);
}

.btn-large {
  height: var(--height-xl);
  font-size: var(--font-size-large);
  padding: 0 var(--spacing-xl);
}

.btn-block {
  width: 100%;
}

.btn-disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 图标按钮 */
.icon-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: var(--border-radius-circle);
  transition: all 0.3s;
  background-color: transparent;
}

.icon-btn:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark-mode .icon-btn:active {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 卡片样式 */
.card {
  background-color: var(--color-bg-base);
  border-radius: var(--border-radius-base);
  box-shadow: var(--shadow-card);
  overflow: hidden;
  margin-bottom: var(--spacing-base);
}

.card-header {
  padding: var(--spacing-base) var(--spacing-large);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: var(--font-size-large);
  font-weight: 500;
  color: var(--color-text-primary);
}

.card-body {
  padding: var(--spacing-large);
}

.card-footer {
  padding: var(--spacing-base) var(--spacing-large);
  border-top: 1px solid var(--color-border);
}

/* 列表样式 */
.list {
  background-color: var(--color-bg-base);
}

.list-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-large);
  position: relative;
}

.list-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: var(--spacing-large);
  right: var(--spacing-large);
  bottom: 0;
  height: 1px;
  background-color: var(--color-border);
}

.list-icon {
  margin-right: var(--spacing-base);
  color: var(--color-primary);
}

.list-content {
  flex: 1;
}

.list-title {
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  margin-bottom: 4px;
}

.list-desc {
  font-size: var(--font-size-small);
  color: var(--color-text-secondary);
}

.list-extra {
  color: var(--color-text-secondary);
}

/* 表单样式 */
.form-group {
  margin-bottom: var(--spacing-large);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-mini);
  font-size: var(--font-size-base);
  color: var(--color-text-regular);
}

.form-input {
  width: 100%;
  height: var(--height-base);
  padding: 0 var(--spacing-base);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-small);
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  background-color: var(--color-bg-base);
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  padding: var(--spacing-base);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-small);
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  background-color: var(--color-bg-base);
  box-sizing: border-box;
  min-height: 100px;
}

.dark-mode .form-input,
.dark-mode .form-textarea {
  border-color: var(--color-border);
  background-color: var(--color-bg-light);
}

/* 安全区域适配 */
.safe-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

/* 无障碍设计相关 */
.a11y-click-area {
  min-height: 44px;
  min-width: 44px;
}

/* 简化UI模式 */
page.simplified-ui .card {
  box-shadow: none;
  border: 1px solid var(--color-border);
}

page.simplified-ui .btn {
  box-shadow: none;
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-in-out;
}

/* 优化后的图标按钮 */
.icon-button {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--color-bg-light);
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.icon-button:active {
  transform: scale(0.95);
  background-color: var(--color-bg-lighter);
}

.icon-button .iconfont {
  font-size: 22px;
  color: var(--color-text-regular);
}

.icon-button-primary {
  background-color: var(--color-primary);
}

.icon-button-primary .iconfont {
  color: #ffffff;
}

.icon-button-outline {
  background-color: transparent;
  border: 1px solid var(--color-border);
}

.icon-button-text {
  background-color: transparent;
}

/* 工具栏样式 */
.toolbar {
  display: flex;
  align-items: center;
  height: 56px;
  padding: 0 var(--spacing-base);
  background-color: var(--color-bg-base);
  border-top: 1px solid var(--color-border);
}

.toolbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.toolbar-icon {
  font-size: 24px;
  color: var(--color-text-secondary);
  margin-bottom: 2px;
}

.toolbar-text {
  font-size: var(--font-size-mini);
  color: var(--color-text-secondary);
}

.toolbar-item.active .toolbar-icon,
.toolbar-item.active .toolbar-text {
  color: var(--color-primary);
} 
