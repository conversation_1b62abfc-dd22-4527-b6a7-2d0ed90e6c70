<!-- crop-preview.wxml -->
<view class="container">
  <!-- 系统导航栏已启用，移除自定义导航栏 -->
  
  <!-- 图片预览区域 -->
  <view class="preview-section">
    <view class="image-wrapper {{showRuler ? 'with-ruler' : ''}}">
      <image 
        src="{{croppedImagePath}}" 
        mode="widthFix" 
        style="width: 100%; height: auto;"
        bindload="onImageLoad">
      </image>
      
      <!-- 标尺线 -->
      <block wx:if="{{showRuler}}">
        <view class="ruler-line horizontal"></view>
        <view class="ruler-line vertical"></view>
        <view class="ruler-corner top-left"></view>
        <view class="ruler-corner top-right"></view>
        <view class="ruler-corner bottom-left"></view>
        <view class="ruler-corner bottom-right"></view>
      </block>
    </view>
    
    <!-- 图片信息 -->
    <view class="image-info">
      <view class="info-row">
        <text class="info-label">原始尺寸:</text>
        <text class="info-value">{{originalInfo.width || 0}} × {{originalInfo.height || 0}} px</text>
      </view>
      <view class="info-row">
        <text class="info-label">原始大小:</text>
        <text class="info-value">{{originalFileSize}}</text>
      </view>
      <view class="info-row">
        <text class="info-label">裁剪尺寸:</text>
        <text class="info-value">{{croppedInfo.width || 0}} × {{croppedInfo.height || 0}} px</text>
      </view>
      <view class="info-row">
        <text class="info-label">当前大小:</text>
        <text class="info-value">{{currentFileSize}}</text>
      </view>
    </view>
  </view>
  
  <!-- 功能区域 -->
  <view class="functions-section">
    <view class="function-button" bindtap="toggleRuler">
      <text>{{showRuler ? '隐藏标尺' : '显示标尺'}}</text>
    </view>
    
    <view class="function-button" bindtap="showCompressDialog">
      <text>压缩大小</text>
    </view>
    
    <view class="function-button" bindtap="recrop">
      <text>重新裁剪</text>
    </view>
  </view>
  
  <!-- 底部操作栏 -->
  <view class="action-bar">
    <button class="share-btn" bindtap="shareToTimeline">分享</button>
    <button class="save-btn" bindtap="saveImage">保存</button>
  </view>
  
  <!-- 压缩对话框 -->
  <view class="modal-mask" wx:if="{{showCompressModal}}" bindtap="hideCompressDialog">
    <view class="compress-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">压缩图片大小</text>
        <text class="close-icon" bindtap="hideCompressDialog">×</text>
      </view>
      
      <view class="modal-body">
        <!-- 当前文件信息 -->
        <view class="size-info">
          <text class="size-label">当前大小: </text>
          <text class="size-value">{{currentFileSize}}</text>
        </view>
        <view wx:if="{{compressTip}}" class="compress-tip">
          <text>{{compressTip}}</text>
        </view>
        
        <!-- 目标大小输入 -->
        <view class="size-input-wrapper">
          <text class="size-label">目标大小: </text>
          <input 
            class="size-input" 
            type="number" 
            value="{{targetFileSize}}" 
            bindinput="onFileSizeInput"
            placeholder="输入目标KB大小"
          />
          <text class="size-unit">KB</text>
        </view>
        
        <!-- 质量滑块 -->
        <view class="quality-slider-wrapper">
          <text class="quality-label">压缩质量: {{compressQuality * 100}}%</text>
          <slider 
            min="10" 
            max="95" 
            value="{{compressQuality * 100}}" 
            bindchange="onQualityChange"
            activeColor="#4caf50"
            backgroundColor="#ddd"
            block-size="20"
          />
        </view>
        
        <!-- 压缩测试按钮 -->
        <view class="compress-test-button" bindtap="runCompressionTest">
          <text>测试不同压缩质量</text>
        </view>
        
        <!-- 压缩测试结果 -->
        <view class="compression-test-results" wx:if="{{compressionTest.length > 0}}">
          <view class="test-results-title">测试结果:</view>
          <view class="test-results-list">
            <view class="test-result-item" wx:for="{{compressionTest}}" wx:key="quality">
              <text class="test-quality">质量: {{item.quality * 100}}%</text>
              <text class="test-size">大小: {{item.size}}</text>
            </view>
          </view>
        </view>
        
        <!-- 压缩状态 -->
        <view class="compress-status" wx:if="{{compressStatus}}">
          <text>{{compressStatus}}</text>
        </view>
      </view>
      
      <view class="modal-footer">
        <button 
          class="cancel-button" 
          bindtap="hideCompressDialog"
          disabled="{{isCompressing}}"
        >取消</button>
        <button 
          class="compress-button" 
          bindtap="compressImage"
          disabled="{{isCompressing}}"
        >压缩</button>
      </view>
    </view>
  </view>
  
  <!-- 保存成功提示 -->
  <view class="save-success-toast" wx:if="{{showSaveSuccess}}">
    <icon type="success" size="24" color="#fff"></icon>
    <text>裁剪后的图片已保存</text>
  </view>
  
  <!-- 用于压缩的Canvas，不显示 -->
  <canvas type="2d" id="compressCanvas" style="width: 0px; height: 0px; position: absolute; left: -1000px; top: -1000px;"></canvas>
</view>