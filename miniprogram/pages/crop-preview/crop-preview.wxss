/* crop-preview.wxss */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #f8f8f8;
}

/* 移除自定义导航栏样式，使用系统默认导航栏 */

/* 预览区域 */
.preview-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 20rpx;
  padding-top: 40rpx;
  position: relative;
  overflow: visible;
  background-color: #f0f0f0;
  min-height: 0;
}

.image-wrapper {
  width: 90%;
  max-height: 65%;
  min-height: 300rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  border-radius: 8rpx;
  overflow: visible;
}

.with-ruler {
  border: 1px dashed #ccc;
}

/* 图片样式 */
image {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  display: block;
  background-color: transparent;
  object-fit: contain;
}

/* 标尺线 */
.ruler-line {
  position: absolute;
  background-color: rgba(0, 191, 255, 0.5);
  pointer-events: none;
}

.ruler-line.horizontal {
  width: 100%;
  height: 1px;
  top: 50%;
  left: 0;
}

.ruler-line.vertical {
  width: 1px;
  height: 100%;
  top: 0;
  left: 50%;
}

.ruler-corner {
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  background-color: rgba(0, 191, 255, 0.8);
  pointer-events: none;
}

.top-left {
  top: 0;
  left: 0;
}

.top-right {
  top: 0;
  right: 0;
}

.bottom-left {
  bottom: 0;
  left: 0;
}

.bottom-right {
  bottom: 0;
  right: 0;
}

/* 图片信息显示 */
.image-info {
  margin-top: 20rpx;
  width: 85%;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
  font-size: 28rpx;
  line-height: 1.5;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #666;
  font-size: 28rpx;
  flex: 0 0 120rpx; /* 固定宽度 */
}

.info-value {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
  text-align: right;
}

/* 功能区域 */
.functions-section {
  display: flex;
  justify-content: space-around;
  padding: 30rpx 20rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
}

.function-button {
  padding: 20rpx 30rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  text-align: center;
}

/* 压缩对话框 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.compress-modal {
  width: 85%;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.close-icon {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.modal-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.size-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.size-label {
  color: #666;
  font-size: 28rpx;
  width: 160rpx;
}

.size-value {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}

.size-input-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.size-input {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  margin-right: 10rpx;
  font-size: 28rpx;
}

.size-unit {
  color: #666;
  font-size: 28rpx;
  width: 60rpx;
}

.quality-slider-wrapper {
  margin-bottom: 30rpx;
}

.quality-label {
  color: #666;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  display: block;
}

.compress-test-button {
  text-align: center;
  padding: 20rpx 0;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  margin-bottom: 30rpx;
  font-size: 28rpx;
  color: #333;
}

.compression-test-results {
  margin-top: 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.test-results-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.test-results-list {
  display: flex;
  flex-direction: column;
}

.test-result-item {
  display: flex;
  justify-content: space-between;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #eee;
}

.test-result-item:last-child {
  border-bottom: none;
}

.test-quality {
  font-size: 26rpx;
  color: #666;
}

.test-size {
  font-size: 26rpx;
  color: #333;
}

.compress-status {
  text-align: center;
  padding: 20rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}

.modal-footer button {
  flex: 1;
  height: 100rpx;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  border: none;
}

.cancel-button {
  background-color: #f8f8f8;
  color: #666;
}

.compress-button {
  background-color: #07c160;
  color: #fff;
}

button[disabled] {
  background-color: #ddd !important;
  color: #999 !important;
}

/* 保存成功提示 */
.save-success-toast {
  position: fixed;
  bottom: 200rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 20rpx 40rpx;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.save-success-toast text {
  margin-left: 10rpx;
  font-size: 28rpx;
}

/* 底部操作栏 */
.action-bar {
  display: flex;
  justify-content: space-around;
  padding: 30rpx 20rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
}

.cancel-btn, .save-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  margin: 0 20rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 30rpx;
  border-radius: 40rpx;
}

.cancel-btn {
  background-color: #f2f2f2;
  color: #333;
}

.save-btn {
  background-color: #07c160;
  color: #fff;
}

.share-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f2f2f2;
  color: #07c160;
  flex: 1;
  margin: 0 20rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 30rpx;
  border-radius: 40rpx;
}

.compress-tip {
  color: #07c160;
  font-size: 26rpx;
  margin-bottom: 16rpx;
  text-align: left;
}