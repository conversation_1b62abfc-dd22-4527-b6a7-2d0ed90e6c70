const app = getApp();

Page({
  data: {
    croppedImagePath: '',    // 裁剪后的图片路径
    originalImagePath: '',   // 原始图片路径
    showSaveSuccess: false,  // 是否显示保存成功提示
    imageStyle: '',          // 图片样式
    croppedInfo: null,       // 裁剪后图片信息
    originalInfo: null,      // 原始图片信息
    displayWidth: 0,         // 显示宽度
    displayHeight: 0,        // 显示高度
    displayInfo: {           // 预览区域显示信息
      width: 0,
      height: 0,
      scale: 1               // 缩放比例
    },
    showRuler: true,         // 是否显示标尺
    // 文件大小压缩相关
    showCompressModal: false,  // 是否显示压缩对话框
    originalFileSize: 0,       // 原始文件大小(KB)
    currentFileSize: 0,        // 当前文件大小(KB)
    targetFileSize: '',        // 目标文件大小(KB)
    compressQuality: 0.8,      // 压缩质量 (0-1)
    compressStatus: '',        // 压缩状态信息
    compressionTest: [],       // 压缩测试结果
    isCompressing: false,      // 是否正在压缩中
    compressTip: ''            // 压缩提示
  },

  onLoad: function (options) {
    // 获取屏幕尺寸
    const systemInfo = wx.getSystemInfoSync();
    this.windowWidth = systemInfo.windowWidth;
    this.windowHeight = systemInfo.windowHeight;
    this.pixelRatio = systemInfo.pixelRatio || 2;
    console.log('屏幕尺寸:', systemInfo);
    
    // 尝试从多种方式获取图片路径
    try {
      // 1. 首先尝试从options参数获取
      if (options && options.croppedImagePath && options.originalImagePath) {
        console.log('从options获取图片路径');
        this.processImagePaths(options.croppedImagePath, options.originalImagePath);
        return;
      }
      
      // 2. 尝试从页面栈中获取
      const pages = getCurrentPages();
      if (pages.length > 1) {
        const prevPage = pages[pages.length - 2];
        if (prevPage && prevPage.data) {
          const prevData = prevPage.data;
          if (prevData.tempCroppedPath && prevData.tempOriginalPath) {
            console.log('从页面栈获取图片路径');
            this.processImagePaths(prevData.tempCroppedPath, prevData.tempOriginalPath);
            return;
          }
        }
      }
      
      // 3. 尝试从eventChannel获取
      try {
        const eventChannel = this.getOpenerEventChannel();
        if (eventChannel && typeof eventChannel.on === 'function') {
          console.log('尝试从eventChannel获取图片路径');
          eventChannel.on('acceptDataFromOpenerPage', (data) => {
            if (data && data.croppedImagePath && data.originalImagePath) {
              this.processImagePaths(data.croppedImagePath, data.originalImagePath);
            } else {
              console.error('eventChannel数据无效:', data);
              this.showError('数据传递错误');
            }
          });
        } else {
          console.warn('eventChannel不可用或on方法不存在');
          // 如果已经从其他方式获取了路径，就不显示错误
          if (!this.data.croppedImagePath) {
            this.showError('无法获取图片路径');
          }
        }
      } catch (channelError) {
        console.error('获取eventChannel失败:', channelError);
        if (!this.data.croppedImagePath) {
          this.showError('获取图片通道失败');
        }
      }
    } catch (error) {
      console.error('onLoad错误:', error);
      this.showError('页面加载错误');
    }
  },
  
  // 处理图片路径
  processImagePaths: function(croppedPath, originalPath) {
    console.log('处理图片路径:', croppedPath, originalPath);

    if (!croppedPath || !originalPath) {
      console.error('图片路径无效');
      this.showError('图片路径无效');
      return;
    }

    // 修复图片路径格式，确保跨平台兼容性
    let fixedCroppedPath = this.fixImagePath(croppedPath);
    let fixedOriginalPath = this.fixImagePath(originalPath);

    console.log('修正后的裁剪图片路径:', fixedCroppedPath);
    console.log('修正后的原始图片路径:', fixedOriginalPath);

    // 验证图片路径有效性
    this.validateAndSetImagePaths(fixedCroppedPath, fixedOriginalPath);
  },

  // 修复图片路径格式，确保跨平台兼容性
  fixImagePath: function(imagePath) {
    if (!imagePath || typeof imagePath !== 'string') {
      return imagePath;
    }

    let fixedPath = imagePath;

    // 处理错误的HTTP协议前缀（如 http://tmp/ 应该是 /tmp/）
    if (fixedPath.startsWith('http://tmp/')) {
      fixedPath = fixedPath.replace('http://tmp/', '/tmp/');
      console.log('修正HTTP临时文件路径:', fixedPath);
    }

    // 处理URL编码问题
    if (fixedPath.indexOf('%') >= 0) {
      try {
        fixedPath = decodeURIComponent(fixedPath);
        console.log('解码路径:', fixedPath);
      } catch (e) {
        console.warn('路径解码失败:', e);
      }
    }

    // 确保临时文件路径格式正确
    if (fixedPath.indexOf('tmp/') >= 0 && !fixedPath.startsWith('/')) {
      // 提取tmp路径部分
      const tmpIndex = fixedPath.indexOf('tmp/');
      fixedPath = fixedPath.substring(tmpIndex);
      if (!fixedPath.startsWith('/')) {
        fixedPath = '/' + fixedPath;
      }
      console.log('修正临时文件路径:', fixedPath);
    }

    // 移除wxfile://前缀（如果存在）
    if (fixedPath.startsWith('wxfile://')) {
      fixedPath = fixedPath.replace('wxfile://', '');
      console.log('移除wxfile前缀:', fixedPath);
    }

    return fixedPath;
  },

  // 验证并设置图片路径
  validateAndSetImagePaths: function(croppedPath, originalPath) {
    // 先验证裁剪图片是否存在
    wx.getFileInfo({
      filePath: croppedPath,
      success: (res) => {
        console.log('裁剪图片文件验证成功:', res);
        // 验证原始图片
        wx.getFileInfo({
          filePath: originalPath,
          success: (res2) => {
            console.log('原始图片文件验证成功:', res2);
            // 两个文件都存在，设置路径
            this.setData({
              croppedImagePath: croppedPath,
              originalImagePath: originalPath
            });

            // 获取图片信息
            this.loadImageInfo(croppedPath, originalPath);
          },
          fail: (err2) => {
            console.error('原始图片文件不存在:', err2);
            // 即使原始图片不存在，也尝试显示裁剪图片
            this.setData({
              croppedImagePath: croppedPath,
              originalImagePath: originalPath
            });
            this.loadImageInfo(croppedPath, originalPath);
          }
        });
      },
      fail: (err) => {
        console.error('裁剪图片文件不存在:', err);
        // 文件不存在，尝试直接使用路径（可能是网络图片）
        this.setData({
          croppedImagePath: croppedPath,
          originalImagePath: originalPath
        });
        this.loadImageInfo(croppedPath, originalPath);
      }
    });
  },

  // 加载图片信息
  loadImageInfo: function(croppedPath, originalPath) {
    // 延迟加载，确保页面已准备好
    setTimeout(() => {
      // 获取裁剪后的图片信息
      this.getCroppedImageInfo(croppedPath);

      // 获取原始图片信息
      this.getOriginalImageInfo(originalPath);

      // 获取文件大小信息
      this.getFileSizeKB(originalPath, size => this.setData({ originalFileSize: size }));
      this.getFileSizeKB(croppedPath, size => this.setData({ currentFileSize: size }));
    }, 200);
  },
  
  // 显示错误提示
  showError: function(message) {
    wx.showToast({
      title: message || '加载失败',
      icon: 'none',
      duration: 2000
    });
  },
  
  // 获取文件大小（字节转KB或MB，保留两位小数）
  getFileSizeKB(filePath, cb) {
    wx.getFileInfo({
      filePath,
      success: (res) => {
        const sizeKB = parseFloat(res.size / 1024).toFixed(2);
        // 如果大小超过1000KB，则转换为MB显示
        if (sizeKB > 1000) {
          const sizeMB = (sizeKB / 1024).toFixed(2);
          cb(sizeMB + ' MB');
        } else {
          cb(sizeKB + ' KB');
        }
      },
      fail: () => cb('--')
    });
  },
  
  // 获取裁剪后的图片信息
  getCroppedImageInfo(imagePath) {
    if (!imagePath) {
      console.error('裁剪图片路径为空');
      return;
    }

    console.log('开始获取裁剪图片信息，路径:', imagePath);

    wx.getImageInfo({
      src: imagePath,
      success: (res) => {
        console.log('获取裁剪后图片信息成功:', res);

        // 保存图片信息
        const croppedInfo = {
          width: res.width,
          height: res.height,
          orientation: res.orientation || 'up',
          type: res.type || 'unknown',
          path: res.path
        };

        // 计算图片显示尺寸，适应屏幕但保持原始比例
        const displaySize = this.calculateDisplaySize(croppedInfo.width, croppedInfo.height);

        // 更新数据
        this.setData({
          croppedInfo: croppedInfo,
          displayWidth: displaySize.width,
          displayHeight: displaySize.height,
          displayInfo: {
            width: croppedInfo.width,
            height: croppedInfo.height,
            scale: displaySize.scale
          }
        });

        console.log('裁剪图片显示尺寸:', displaySize);
      },
      fail: (err) => {
        console.error('获取裁剪图片信息失败:', err);
        // 尝试重新获取，使用不同的路径格式
        this.retryCroppedImageInfo(imagePath);
      }
    });
  },

  // 重试获取裁剪图片信息
  retryCroppedImageInfo(imagePath) {
    console.log('重试获取裁剪图片信息');

    // 尝试不同的路径格式
    const pathVariants = [
      imagePath,
      imagePath.startsWith('/') ? imagePath : '/' + imagePath,
      imagePath.replace(/^\/+/, ''),
      'wxfile://' + imagePath.replace(/^wxfile:\/\//, '')
    ];

    let retryIndex = 0;
    const tryNextPath = () => {
      if (retryIndex >= pathVariants.length) {
        console.error('所有路径格式都尝试失败');
        this.showError('图片加载失败');
        return;
      }

      const currentPath = pathVariants[retryIndex];
      console.log(`尝试路径格式 ${retryIndex + 1}:`, currentPath);

      wx.getImageInfo({
        src: currentPath,
        success: (res) => {
          console.log('重试成功，图片信息:', res);
          // 更新正确的路径
          this.setData({ croppedImagePath: currentPath });
          // 处理成功的结果
          this.processCroppedImageInfo(res);
        },
        fail: (err) => {
          console.warn(`路径格式 ${retryIndex + 1} 失败:`, err);
          retryIndex++;
          tryNextPath();
        }
      });
    };

    tryNextPath();
  },

  // 处理裁剪图片信息
  processCroppedImageInfo(res) {
    const croppedInfo = {
      width: res.width,
      height: res.height,
      orientation: res.orientation || 'up',
      type: res.type || 'unknown',
      path: res.path
    };

    const displaySize = this.calculateDisplaySize(croppedInfo.width, croppedInfo.height);

    this.setData({
      croppedInfo: croppedInfo,
      displayWidth: displaySize.width,
      displayHeight: displaySize.height,
      displayInfo: {
        width: croppedInfo.width,
        height: croppedInfo.height,
        scale: displaySize.scale
      }
    });
  },

  // 计算显示尺寸
  calculateDisplaySize(imageWidth, imageHeight) {
    const containerWidth = this.windowWidth * 0.85;
    const containerHeight = this.windowHeight * 0.5;

    const imageRatio = imageWidth / imageHeight;
    let displayWidth, displayHeight, scale;

    if (imageWidth > imageHeight) {
      // 宽图
      displayWidth = Math.min(containerWidth, imageWidth);
      displayHeight = displayWidth / imageRatio;

      // 确保高度不超过容器
      if (displayHeight > containerHeight) {
        displayHeight = containerHeight;
        displayWidth = displayHeight * imageRatio;
      }
    } else {
      // 高图
      displayHeight = Math.min(containerHeight, imageHeight);
      displayWidth = displayHeight * imageRatio;

      // 确保宽度不超过容器
      if (displayWidth > containerWidth) {
        displayWidth = containerWidth;
        displayHeight = displayWidth / imageRatio;
      }
    }

    scale = displayWidth / imageWidth;

    return {
      width: Math.round(displayWidth),
      height: Math.round(displayHeight),
      scale: scale
    };
  },

  // 处理裁剪图片信息
  processCroppedImageInfo: function(res) {
    const croppedInfo = {
      width: res.width,
      height: res.height,
      orientation: res.orientation || 'up',
      type: res.type || 'unknown',
      path: res.path
    };

    const displaySize = this.calculateDisplaySize(croppedInfo.width, croppedInfo.height);

    this.setData({
      croppedInfo: croppedInfo,
      displayWidth: displaySize.width,
      displayHeight: displaySize.height,
      displayInfo: {
        width: croppedInfo.width,
        height: croppedInfo.height,
        scale: displaySize.scale
      }
    });
  },
  
  // 获取原始图片信息
  getOriginalImageInfo: function(imagePath) {
    if (!imagePath) {
      console.error('原始图片路径为空');
      return;
    }
    
    console.log('开始获取原始图片信息，路径:', imagePath);
    
    // 尝试修复路径
    let fixedPath = imagePath;
    if (typeof imagePath === 'string') {
      // 确保路径是绝对路径
      if (!imagePath.startsWith('/') && !imagePath.startsWith('http://') && !imagePath.startsWith('wxfile://')) {
        fixedPath = '/' + imagePath;
        console.log('修正原始图片路径为绝对路径:', fixedPath);
      }
    }
    
    wx.getImageInfo({
      src: fixedPath,
      success: (res) => {
        console.log('获取原始图片信息成功:', res);
        
        // 保存原始图片信息
        const originalInfo = {
          width: res.width,
          height: res.height,
          orientation: res.orientation,
          type: res.type,
          path: res.path
        };
        
        this.setData({
          originalInfo: originalInfo
        });
      },
      fail: (err) => {
        console.error('获取原始图片信息失败:', err);
        
        // 如果获取失败，设置一些默认值
        this.setData({
          originalInfo: {
            width: 300,
            height: 300,
            path: this.data.originalImagePath
          }
        });
      }
    });
  },
  
  // 图片加载完成
  onImageLoad: function(e) {
    console.log('预览图片加载完成:', e.detail);

    const { width, height } = e.detail;

    // 确保显示的是实际裁剪后的尺寸信息
    let actualWidth = width;
    let actualHeight = height;

    // 如果已经获取到裁剪图片信息，使用真实尺寸
    if (this.data.croppedInfo) {
      actualWidth = this.data.croppedInfo.width;
      actualHeight = this.data.croppedInfo.height;
      console.log('使用裁剪图片真实尺寸:', actualWidth, 'x', actualHeight);
    } else {
      console.log('使用图片加载尺寸:', actualWidth, 'x', actualHeight);
    }

    // 计算显示比例
    const displayScale = this.data.displayWidth > 0 ? this.data.displayWidth / actualWidth : 1;

    // 更新显示信息
    this.setData({
      displayInfo: {
        width: actualWidth,
        height: actualHeight,
        scale: displayScale,
        containerWidth: this.data.displayWidth,
        containerHeight: this.data.displayHeight
      }
    });

    // 创建标尺图
    this.createRuler();

    // 确保图片正确显示后，隐藏加载状态
    wx.hideLoading();
  },
  
  // 创建标尺
  createRuler: function() {
    const query = wx.createSelectorQuery();
    query.select('.image-wrapper').boundingClientRect((rect) => {
      if (!rect) return;

      console.log('图片容器尺寸:', rect);
      this.wrapperWidth = rect.width;
      this.wrapperHeight = rect.height;

      // 更新显示信息
      this.setData({
        displayInfo: {
          ...this.data.displayInfo,
          containerWidth: Math.round(rect.width),
          containerHeight: Math.round(rect.height)
        }
      });
    }).exec();
  },
  
  // 切换标尺显示
  toggleRuler: function() {
    this.setData({
      showRuler: !this.data.showRuler
    });
  },

  // 阻止事件冒泡
  stopPropagation: function(e) {
    // 阻止事件冒泡，防止点击对话框内部时触发关闭对话框
    console.log('阻止冒泡');
  },
  
  // 显示压缩对话框
  showCompressDialog: function() {
    // 重新获取一次当前文件大小，确保数据准确性
    this.getFileSizeKB(this.data.croppedImagePath, size => {
      console.log('打开压缩对话框前重新获取文件大小:', size);
      this.setData({
        currentFileSize: size,
        showCompressModal: true,
        compressStatus: '',
        compressionTest: []
      });
      console.log('已设置showCompressModal为true');
    });
  },

  // 关闭压缩对话框
  hideCompressDialog: function() {
    console.log('关闭压缩对话框');
    this.setData({
      showCompressModal: false
    });
  },
  
  // 处理目标大小输入变化
  onFileSizeInput: function(e) {
    const targetSize = e.detail.value;
    this.setData({ targetFileSize: targetSize });
    if (targetSize && this.data.currentFileSize) {
      const targetSizeNum = parseFloat(targetSize);
      // 处理当前大小可能包含MB单位的情况
      let currentSize;
      if (typeof this.data.currentFileSize === 'string' && this.data.currentFileSize.indexOf('MB') > -1) {
        // 如果是MB单位，转换为KB进行比较
        currentSize = parseFloat(this.data.currentFileSize) * 1024;
      } else {
        currentSize = parseFloat(this.data.currentFileSize);
      }
      
      let estimatedQuality = 0.8;
      let compressTip = '';
      if (targetSizeNum >= currentSize) {
        estimatedQuality = 0.95;
        compressTip = '目标大于当前，无需压缩，将以最高质量导出';
      } else {
        const ratio = targetSizeNum / currentSize;
        if (ratio > 0.8) {
          estimatedQuality = 0.9;
        } else if (ratio > 0.6) {
          estimatedQuality = 0.8;
        } else if (ratio > 0.4) {
          estimatedQuality = 0.6;
        } else if (ratio > 0.2) {
          estimatedQuality = 0.4;
        } else if (ratio > 0.1) {
          estimatedQuality = 0.25;
        } else {
          estimatedQuality = 0.1;
        }
        compressTip = '';
      }
      this.setData({
        compressQuality: estimatedQuality.toFixed(2),
        compressTip
      });
    }
  },
  
  // 处理压缩质量滑块变化
  onQualityChange(e) {
    const quality = e.detail.value / 100;
    this.setData({
      compressQuality: quality.toFixed(2)
    });
  },
  
  // 运行压缩测试
  runCompressionTest() {
    if (this.data.isCompressing) return;
    const { croppedImagePath, croppedInfo } = this.data;
    this.setData({ 
      compressStatus: '正在测试压缩质量...',
      isCompressing: true
    });
    const qualityLevels = [0.9, 0.7, 0.5, 0.3, 0.1];
    const runTests = async () => {
      const results = [];
      for (const quality of qualityLevels) {
        try {
          const compressedPath = await this.compressWithQuality(croppedImagePath, croppedInfo, quality);
          await new Promise(resolve => setTimeout(resolve, 100)); // 避免文件未写完
          wx.getFileInfo({
            filePath: compressedPath,
            success: (res) => {
              const sizeKB = parseFloat(res.size / 1024).toFixed(2);
              let sizeDisplay;
              if (sizeKB > 1000) {
                const sizeMB = (sizeKB / 1024).toFixed(2);
                sizeDisplay = sizeMB + 'MB';
              } else {
                sizeDisplay = sizeKB + 'KB';
              }
              results.push({
                quality: quality,
                size: sizeDisplay
              });
              this.setData({
                compressionTest: [...results],
                compressStatus: `测试中: ${results.length}/${qualityLevels.length}`
              });
            },
            fail: () => {
              results.push({ quality: quality, size: '--' });
              this.setData({
                compressionTest: [...results],
                compressStatus: `测试中: ${results.length}/${qualityLevels.length}`
              });
            }
          });
        } catch (error) {
          results.push({ quality: quality, size: '--' });
          this.setData({
            compressionTest: [...results],
            compressStatus: `测试中: ${results.length}/${qualityLevels.length}`
          });
        }
      }
      this.setData({
        compressStatus: '测试完成，请选择合适的质量',
        isCompressing: false
      });
      return results;
    };
    runTests().catch(err => {
      this.setData({
        compressStatus: '测试失败',
        isCompressing: false
      });
    });
  },
  
  // 使用指定质量压缩图片
  compressWithQuality(imagePath, imageInfo, quality) {
    return new Promise((resolve, reject) => {
      wx.createSelectorQuery()
        .select('#compressCanvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          if (!res || !res[0] || !res[0].node) {
            reject(new Error('获取Canvas失败'));
            return;
          }
          
          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');
          
          // 设置canvas尺寸为图片尺寸
          canvas.width = imageInfo.width;
          canvas.height = imageInfo.height;
          
          // 创建图片对象
          const img = canvas.createImage();
          
          img.onload = () => {
            // 先填充白色背景
            ctx.fillStyle = '#FFFFFF';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制图片
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
            
            // 导出为图片，使用指定的压缩质量
            wx.canvasToTempFilePath({
              canvas: canvas,
              fileType: 'jpg',
              quality: quality,
              success: (res) => {
                resolve(res.tempFilePath);
              },
              fail: (err) => {
                reject(err);
              }
            });
          };
          
          img.onerror = (err) => {
            reject(new Error('图片加载失败'));
          };
          
          img.src = imagePath;
        });
    });
  },
  
  // 执行图片压缩
  compressImage() {
    if (this.data.isCompressing) {
      wx.showToast({
        title: '压缩处理中，请稍候',
        icon: 'none'
      });
      return;
    }
    
    const { targetFileSize, croppedImagePath, croppedInfo, compressQuality } = this.data;
    
    // 验证输入
    if (!targetFileSize || isNaN(parseInt(targetFileSize)) || parseInt(targetFileSize) <= 0) {
      wx.showToast({
        title: '请输入有效的目标大小',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ 
      compressStatus: '正在压缩...',
      isCompressing: true
    });
    wx.showLoading({ title: '压缩中...' });
    
    // 压缩策略：先用设定的质量压缩，如果大小不符合要求，再进行二次压缩
    this.adaptiveCompression(croppedImagePath, croppedInfo, parseFloat(compressQuality), parseInt(targetFileSize))
      .then(result => {
        wx.hideLoading();
        this.hideCompressDialog();
        this.setData({
          croppedImagePath: result.path,
          compressStatus: '压缩成功',
          isCompressing: false
        });
        this.getFileSizeKB(result.path, size => this.setData({ currentFileSize: size }));
        wx.showToast({
          title: `压缩成功`,
          icon: 'none',
          duration: 2000
        });
      })
      .catch(err => {
        console.error('压缩失败:', err);
        wx.hideLoading();
        this.setData({
          compressStatus: '压缩失败: ' + err.message,
          isCompressing: false
        });
        
        wx.showToast({
          title: '压缩失败',
          icon: 'none'
        });
      });
  },
  
  // 自适应压缩算法
  async adaptiveCompression(imagePath, imageInfo, initialQuality, targetSize) {
    // 最多尝试5次压缩
    let attempts = 0;
    let currentQuality = initialQuality;
    let bestResult = null;
    let lastSize = Infinity;
    
    while (attempts < 5) {
      attempts++;
      
      try {
        // 使用当前质量进行压缩
        const compressedPath = await this.compressWithQuality(imagePath, imageInfo, currentQuality);
        let fileSize;
        await new Promise(resolve => {
          this.getFileSizeKB(compressedPath, size => {
            // 如果返回值包含MB，需要提取数字部分进行比较
            if (typeof size === 'string' && size.indexOf('MB') > -1) {
              fileSize = parseFloat(size) * 1024; // 转回KB用于计算
            } else {
              fileSize = parseFloat(size);
            }
            resolve();
          });
        });
        
        console.log(`压缩尝试 #${attempts}: 质量=${currentQuality}, 大小=${fileSize}KB, 目标=${targetSize}KB`);
        
        // 保存这个结果
        const currentResult = { path: compressedPath, size: fileSize, quality: currentQuality };
        
        // 如果这是首次尝试或者更接近目标大小，更新最佳结果
        if (!bestResult || Math.abs(fileSize - targetSize) < Math.abs(lastSize - targetSize)) {
          bestResult = currentResult;
          lastSize = fileSize;
        }
        
        // 大小检查
        if (fileSize <= targetSize * 1.05 && fileSize >= targetSize * 0.95) {
          // 在目标范围内 (±5%)，压缩成功
          console.log('压缩达到目标范围');
          return currentResult;
        } else if (fileSize > targetSize) {
          // 仍然太大，降低质量
          currentQuality = Math.max(0.1, currentQuality - 0.15);
        } else {
          // 太小了，增加质量
          currentQuality = Math.min(0.95, currentQuality + 0.1);
        }
        
        // 更新状态
        this.setData({
          compressStatus: `压缩中: 第${attempts}次尝试, 当前${fileSize}KB`
        });
        
      } catch (error) {
        console.error(`压缩尝试 #${attempts} 失败:`, error);
      }
    }
    
    // 达到最大尝试次数，返回最接近的结果
    if (bestResult) {
      console.log('达到最大尝试次数，返回最佳结果:', bestResult);
      return bestResult;
    }
    
    throw new Error('无法达到目标大小');
  },
  
  // 返回上一页
  goBack() {
    // 获取当前页面栈
    const pages = getCurrentPages();
    console.log('当前页面栈:', pages);
    
    // 如果有上一页，直接返回
    if (pages.length > 1) {
      const prevPage = pages[pages.length - 2];
      console.log('返回到页面:', prevPage.route);
      
      // 首先尝试使用navigateBack返回
      wx.navigateBack({
        delta: 1,
        fail: function(err) {
          console.error('navigateBack失败:', err);
          // 如果navigateBack失败，尝试使用redirectTo
          wx.redirectTo({
            url: '/' + prevPage.route,
            fail: function(redirectErr) {
              console.error('redirectTo失败:', redirectErr);
              // 如果redirectTo也失败，尝试使用switchTab
              wx.switchTab({
                url: '/pages/index/index',
                fail: function() {
                  // 如果所有方法都失败，使用reLaunch
                  wx.reLaunch({
                    url: '/pages/index/index'
                  });
                }
              });
            }
          });
        }
      });
    } else {
      // 如果没有上一页，返回到首页
      wx.reLaunch({
        url: '/pages/index/index'
      });
    }
  },
  
  // 保存图片
  saveImage: function() {
    wx.showLoading({ title: '保存图片...' });
    
    // 保存当前图片路径，防止丢失
    const currentCroppedPath = this.data.croppedImagePath;
    const currentOriginalPath = this.data.originalImagePath;
    
    if (!currentCroppedPath) {
      wx.hideLoading();
      wx.showToast({
        title: '无效的图片路径',
        icon: 'none'
      });
      return;
    }
    
    console.log('保存裁剪后图片，当前路径:', currentCroppedPath);
    
    // 先检查图片是否有效
    wx.getFileInfo({
      filePath: currentCroppedPath,
      success: (fileInfo) => {
        if (fileInfo.size <= 0) {
          wx.hideLoading();
          wx.showToast({
            title: '图片无效',
            icon: 'none'
          });
          return;
        }
        
        this.getFileSizeKB(currentCroppedPath, size => this.setData({ currentFileSize: size }));
        
        console.log('保存裁剪后图片大小:', Math.round(fileInfo.size / 1024), 'KB');
        
        // 图片有效，保存到相册
        wx.saveImageToPhotosAlbum({
          filePath: currentCroppedPath,
          success: () => {
            wx.hideLoading();
            
            // 显示保存成功提示，确保保持原图路径不变
            this.setData({ 
              showSaveSuccess: true,
              // 确保保持原图路径不变
              croppedImagePath: currentCroppedPath,
              originalImagePath: currentOriginalPath
            });
            
            // 显示提示后直接返回上一页
            setTimeout(() => {
              this.goBack();
            }, 1000);
          },
          fail: (err) => {
            wx.hideLoading();
            
            // 获取保存到相册的权限可能被拒绝
            if (err.errMsg === "saveImageToPhotosAlbum:fail:auth denied" || err.errMsg === "saveImageToPhotosAlbum:fail auth deny") {
                wx.showModal({
                  title: '提示',
                  content: '需要保存图片到相册的权限，请授权',
                  success: (res) => {
                    if (res.confirm) {
                      wx.openSetting({
                        success: (settingRes) => {
                          if (settingRes.authSetting['scope.writePhotosAlbum']) {
                            wx.showToast({
                              title: '授权成功，请重新保存',
                              icon: 'none'
                            });
                          } else {
                            wx.showToast({
                              title: '授权失败，无法保存图片',
                              icon: 'none'
                            });
                          }
                        }
                      });
                    }
                  }
                });
              } else {
                wx.showToast({
                  title: '图片保存失败',
                  icon: 'none'
                });
                console.error('保存图片失败:', err);
              }
            }
          });
        },
        fail: (err) => {
          wx.hideLoading();
          wx.showToast({
            title: '图片读取失败',
            icon: 'none'
          });
          console.error('读取图片信息失败:', err);
        }
      });
  },
  
  // 重新裁剪
  recrop() {
    // 返回到裁剪页面，重新裁剪原始图片
    wx.navigateBack({
      delta: 1, // 返回一级页面
    });
  },

  // 分享到微信（图片）
  shareToTimeline() {
    const filePath = this.data.croppedImagePath;
    if (!filePath) {
      wx.showToast({ title: '无可分享图片', icon: 'none' });
      return;
    }
    wx.showActionSheet({
      itemList: ['分享到微信好友', '分享到朋友圈'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 分享到微信好友
          wx.shareFileMessage ? wx.shareFileMessage({
            filePath,
            fileType: 'image',
            success: () => {
              wx.showToast({ title: '已调起微信', icon: 'none' });
              // 分享成功后延迟返回上一页
              setTimeout(() => this.goBack(), 1000);
            },
            fail: () => wx.showToast({ title: '分享失败', icon: 'none' })
          }) : wx.showShareImageMenu({
            path: filePath,
            success: () => {
              wx.showToast({ title: '已调起微信', icon: 'none' });
              // 分享成功后延迟返回上一页
              setTimeout(() => this.goBack(), 1000);
            },
            fail: () => wx.showToast({ title: '分享失败', icon: 'none' })
          });
        } else if (res.tapIndex === 1) {
          // 分享到朋友圈
          wx.showShareImageMenu({
            path: filePath,
            success: () => {
              wx.showToast({ title: '已调起朋友圈', icon: 'none' });
              // 分享成功后延迟返回上一页
              setTimeout(() => this.goBack(), 1000);
            },
            fail: () => wx.showToast({ title: '分享失败', icon: 'none' })
          });
        }
      },
      fail: () => {}
    });
  },

  // 支持右上角转发到好友
  onShareAppMessage: function() {
    // 获取全局用户信息
    const app = getApp();
    const nickName = (app.globalData.userInfo && app.globalData.userInfo.nickName) || '用户';
    
    return {
      title: `${nickName}的图片作品`,
      path: '/pages/index/index',
      imageUrl: this.data.croppedImagePath
    };
  },

  // 支持右上角转发到朋友圈
  onShareTimeline() {
    return {
      title: '图片助手-分享我的图片',
      imageUrl: this.data.croppedImagePath
    };
  }
});