// 拼图页面
Page({
  data: {
    selectedImages: [], // 选中的图片路径数组
    selectedTemplate: 'template1', // 当前选择的模板
    spacing: 5, // 图片间距，单位px
    backgroundColor: '#FFFFFF', // 背景色
    canvasWidth: 0, // 画布宽度
    canvasHeight: 0, // 画布高度
    canSave: false, // 是否可以保存
    isSaving: false, // 是否正在保存
    
    // 定义各种模板的布局
    templates: {
      template1: { // 2张横排
        maxImages: 2,
        layout: (width, height, spacing, images) => {
          const itemWidth = (width - spacing) / 2;
          const itemHeight = height;
          return [
            { x: 0, y: 0, width: itemWidth, height: itemHeight },
            { x: itemWidth + spacing, y: 0, width: itemWidth, height: itemHeight }
          ];
        }
      },
      template2: { // 2张竖排
        maxImages: 2,
        layout: (width, height, spacing, images) => {
          const itemWidth = width;
          const itemHeight = (height - spacing) / 2;
          return [
            { x: 0, y: 0, width: itemWidth, height: itemHeight },
            { x: 0, y: itemHeight + spacing, width: itemWidth, height: itemHeight }
          ];
        }
      },
      template3: { // 4张方格
        maxImages: 4,
        layout: (width, height, spacing, images) => {
          const itemWidth = (width - spacing) / 2;
          const itemHeight = (height - spacing) / 2;
          return [
            { x: 0, y: 0, width: itemWidth, height: itemHeight },
            { x: itemWidth + spacing, y: 0, width: itemWidth, height: itemHeight },
            { x: 0, y: itemHeight + spacing, width: itemWidth, height: itemHeight },
            { x: itemWidth + spacing, y: itemHeight + spacing, width: itemWidth, height: itemHeight }
          ];
        }
      },
      template4: { // 3张横排
        maxImages: 3,
        layout: (width, height, spacing, images) => {
          const itemWidth = (width - spacing * 2) / 3;
          const itemHeight = height;
          return [
            { x: 0, y: 0, width: itemWidth, height: itemHeight },
            { x: itemWidth + spacing, y: 0, width: itemWidth, height: itemHeight },
            { x: (itemWidth + spacing) * 2, y: 0, width: itemWidth, height: itemHeight }
          ];
        }
      },
      template5: { // 6张网格
        maxImages: 6,
        layout: (width, height, spacing, images) => {
          const itemWidth = (width - spacing * 2) / 3;
          const itemHeight = (height - spacing) / 2;
          return [
            { x: 0, y: 0, width: itemWidth, height: itemHeight },
            { x: itemWidth + spacing, y: 0, width: itemWidth, height: itemHeight },
            { x: (itemWidth + spacing) * 2, y: 0, width: itemWidth, height: itemHeight },
            { x: 0, y: itemHeight + spacing, width: itemWidth, height: itemHeight },
            { x: itemWidth + spacing, y: itemHeight + spacing, width: itemWidth, height: itemHeight },
            { x: (itemWidth + spacing) * 2, y: itemHeight + spacing, width: itemWidth, height: itemHeight }
          ];
        }
      }
    }
  },
  
  onLoad: function(options) {
    // 如果有传入的图片路径，添加到选中图片列表
    if (options && options.imagePath) {
      this.setData({
        selectedImages: [options.imagePath]
      });
    }
    
    // 初始化画布
    this.initCanvas();
  },
  
  onReady: function() {
    // 在页面渲染完成后，更新画布尺寸并绘制拼图
    setTimeout(() => {
      this.updateCanvasSize();
    }, 300);
  },
  
  // 初始化画布
  initCanvas: function() {
    const query = wx.createSelectorQuery();
    query.select('#collageCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res || !res[0]) {
          console.error('获取画布节点失败');
          return;
        }
        
        const canvas = res[0].node;
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        
        // 设置画布尺寸
        this.updateCanvasSize();
      });
  },
  
  // 更新画布尺寸
  updateCanvasSize: function() {
    const query = wx.createSelectorQuery();
    query.select('.preview-container')
      .boundingClientRect()
      .exec((res) => {
        if (!res || !res[0]) {
          console.error('获取预览容器尺寸失败');
          return;
        }
        
        const containerWidth = res[0].width;
        const containerHeight = res[0].height;
        
        // 设置画布尺寸，保持16:9的比例
        let canvasWidth = containerWidth - 30; // 减去padding
        let canvasHeight = canvasWidth * 9 / 16;
        
        // 确保画布高度不超过容器高度
        if (canvasHeight > containerHeight - 30) {
          canvasHeight = containerHeight - 30;
          canvasWidth = canvasHeight * 16 / 9;
        }
        
        // 获取设备像素比
        const dpr = wx.getWindowInfo().pixelRatio;
        
        // 设置画布尺寸
        this.canvas.width = canvasWidth * dpr;
        this.canvas.height = canvasHeight * dpr;
        
        this.setData({
          canvasWidth: canvasWidth,
          canvasHeight: canvasHeight
        }, () => {
          // 设置完尺寸后绘制拼图
          this.drawCollage();
        });
        
        // 设置画布缩放
        this.ctx.scale(dpr, dpr);
      });
  },
  
  // 选择模板
  selectTemplate: function(e) {
    const template = e.currentTarget.dataset.template;
    this.setData({
      selectedTemplate: template
    }, () => {
      this.drawCollage();
    });
  },
  
  // 修改间距
  onSpacingChange: function(e) {
    this.setData({
      spacing: e.detail.value
    }, () => {
      this.drawCollage();
    });
  },
  
  // 选择背景色
  selectBackgroundColor: function(e) {
    const color = e.currentTarget.dataset.color;
    this.setData({
      backgroundColor: color
    }, () => {
      this.drawCollage();
    });
  },
  
  // 添加图片
  addImage: function() {
    const template = this.data.templates[this.data.selectedTemplate];
    if (this.data.selectedImages.length >= template.maxImages) {
      wx.showToast({
        title: `最多只能添加${template.maxImages}张图片`,
        icon: 'none'
      });
      return;
    }
    
    wx.chooseMedia({
      count: template.maxImages - this.data.selectedImages.length,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFiles = res.tempFiles;
        const newImages = tempFiles.map(file => file.tempFilePath);
        
        this.setData({
          selectedImages: [...this.data.selectedImages, ...newImages]
        }, () => {
          this.drawCollage();
          this.checkCanSave();
        });
      }
    });
  },
  
  // 移除图片
  removeImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const selectedImages = [...this.data.selectedImages];
    selectedImages.splice(index, 1);
    
    this.setData({
      selectedImages: selectedImages
    }, () => {
      this.drawCollage();
      this.checkCanSave();
    });
  },
  
  // 绘制拼图
  drawCollage: function() {
    if (!this.ctx || !this.canvas) {
      console.error('画布未初始化');
      return;
    }
    
    const { canvasWidth, canvasHeight, spacing, backgroundColor, selectedTemplate, selectedImages } = this.data;
    const template = this.data.templates[selectedTemplate];
    
    // 清空画布
    this.ctx.clearRect(0, 0, canvasWidth, canvasHeight);
    
    // 绘制背景
    this.ctx.fillStyle = backgroundColor;
    this.ctx.fillRect(0, 0, canvasWidth, canvasHeight);
    
    // 如果没有选择图片，不继续绘制
    if (selectedImages.length === 0) {
      return;
    }
    
    // 获取布局
    const layout = template.layout(canvasWidth, canvasHeight, spacing, selectedImages);
    
    // 绘制图片
    selectedImages.forEach((imagePath, index) => {
      if (index >= layout.length) return;
      
      const position = layout[index];
      
      // 创建图片对象
      const img = this.canvas.createImage();
      img.src = imagePath;
      
      img.onload = () => {
        // 计算图片绘制参数，保持原始比例
        const imgRatio = img.width / img.height;
        const cellRatio = position.width / position.height;
        
        let drawWidth, drawHeight, drawX, drawY;
        
        if (imgRatio > cellRatio) {
          // 图片更宽，以高度为基准
          drawHeight = position.height;
          drawWidth = drawHeight * imgRatio;
          drawX = position.x + (position.width - drawWidth) / 2;
          drawY = position.y;
        } else {
          // 图片更高，以宽度为基准
          drawWidth = position.width;
          drawHeight = drawWidth / imgRatio;
          drawX = position.x;
          drawY = position.y + (position.height - drawHeight) / 2;
        }
        
        // 绘制图片
        this.ctx.drawImage(img, drawX, drawY, drawWidth, drawHeight);
      };
      
      img.onerror = () => {
        console.error('加载图片失败:', imagePath);
      };
    });
  },
  
  // 检查是否可以保存
  checkCanSave: function() {
    const { selectedImages, selectedTemplate } = this.data;
    const template = this.data.templates[selectedTemplate];
    
    // 至少需要有一张图片才能保存
    const canSave = selectedImages.length > 0 && selectedImages.length <= template.maxImages;
    
    this.setData({
      canSave: canSave
    });
  },
  
  // 保存拼图
  saveCollage: function() {
    if (!this.canvas || !this.data.canSave) {
      return;
    }
    
    this.setData({
      isSaving: true
    });
    
    // 获取设备像素比
    const dpr = wx.getWindowInfo().pixelRatio;
    
    // 将画布内容转为图片
    wx.canvasToTempFilePath({
      canvas: this.canvas,
      x: 0,
      y: 0,
      width: this.data.canvasWidth * dpr,
      height: this.data.canvasHeight * dpr,
      destWidth: this.data.canvasWidth * dpr,
      destHeight: this.data.canvasHeight * dpr,
      success: (res) => {
        // 保存图片到相册
        wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            wx.showToast({
              title: '拼图已保存到相册',
              icon: 'success'
            });
            
            this.setData({
              isSaving: false
            });
          },
          fail: (err) => {
            console.error('保存图片失败:', err);
            wx.showToast({
              title: '保存失败，请检查权限',
              icon: 'none'
            });
            
            this.setData({
              isSaving: false
            });
          }
        });
      },
      fail: (err) => {
        console.error('生成图片失败:', err);
        wx.showToast({
          title: '生成图片失败',
          icon: 'none'
        });
        
        this.setData({
          isSaving: false
        });
      }
    });
  },
  
  // 取消拼图
  cancelCollage: function() {
    wx.navigateBack();
  }
}); 