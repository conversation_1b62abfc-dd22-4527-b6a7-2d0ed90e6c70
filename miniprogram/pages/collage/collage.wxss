.collage-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}

/* 预览区域 */
.preview-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px;
  background-color: #ffffff;
  min-height: 300px;
}

.collage-canvas {
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 模板选择区域 */
.section-title {
  font-size: 14px;
  color: #333;
  padding: 10px 15px;
  font-weight: bold;
}

.template-selector {
  background-color: #ffffff;
  margin-top: 10px;
  padding-bottom: 10px;
}

.template-scroll {
  white-space: nowrap;
  padding: 0 10px;
}

.template-item {
  display: inline-block;
  width: 80px;
  margin-right: 10px;
  text-align: center;
}

.template-preview {
  width: 70px;
  height: 70px;
  margin-bottom: 5px;
  border: 2px solid #eee;
  border-radius: 6px;
  background-color: #f5f5f5;
}

.template-item.active .template-preview {
  border-color: #07c160;
}

.template-item text {
  font-size: 12px;
  color: #666;
}

.template-item.active text {
  color: #07c160;
}

/* 模板预览样式 */
.template1 {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect x="5" y="25" width="42" height="50" fill="%23ddd"/><rect x="53" y="25" width="42" height="50" fill="%23ddd"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.template2 {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect x="25" y="5" width="50" height="42" fill="%23ddd"/><rect x="25" y="53" width="50" height="42" fill="%23ddd"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.template3 {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect x="5" y="5" width="42" height="42" fill="%23ddd"/><rect x="53" y="5" width="42" height="42" fill="%23ddd"/><rect x="5" y="53" width="42" height="42" fill="%23ddd"/><rect x="53" y="53" width="42" height="42" fill="%23ddd"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.template4 {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect x="5" y="25" width="28" height="50" fill="%23ddd"/><rect x="36" y="25" width="28" height="50" fill="%23ddd"/><rect x="67" y="25" width="28" height="50" fill="%23ddd"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.template5 {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect x="5" y="5" width="28" height="42" fill="%23ddd"/><rect x="36" y="5" width="28" height="42" fill="%23ddd"/><rect x="67" y="5" width="28" height="42" fill="%23ddd"/><rect x="5" y="53" width="28" height="42" fill="%23ddd"/><rect x="36" y="53" width="28" height="42" fill="%23ddd"/><rect x="67" y="53" width="28" height="42" fill="%23ddd"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 设置面板 */
.settings-panel {
  background-color: #ffffff;
  margin-top: 10px;
  padding: 10px 15px;
}

.setting-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.setting-label {
  width: 60px;
  font-size: 14px;
  color: #333;
}

.setting-item slider {
  flex: 1;
}

.setting-value {
  width: 50px;
  text-align: right;
  font-size: 14px;
  color: #666;
}

.color-picker {
  display: flex;
  flex: 1;
}

.color-item {
  width: 30px;
  height: 30px;
  margin-right: 10px;
  border-radius: 50%;
  border: 2px solid #eee;
}

.color-item.active {
  border-color: #07c160;
}

/* 图片管理区域 */
.images-panel {
  background-color: #ffffff;
  margin-top: 10px;
  padding-bottom: 15px;
}

.images-scroll {
  white-space: nowrap;
  padding: 0 10px;
}

.add-image {
  display: inline-block;
  width: 80px;
  height: 80px;
  margin-right: 10px;
  border: 2px dashed #ccc;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.add-icon {
  font-size: 30px;
  color: #ccc;
}

.image-item {
  display: inline-block;
  width: 80px;
  height: 80px;
  margin-right: 10px;
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

.image-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom-left-radius: 6px;
}

/* 底部工具栏 */
.bottom-toolbar {
  padding: 15px;
  background-color: #ffffff;
  margin-top: 10px;
}

.save-btn {
  width: 100%;
  background-color: #07c160;
  color: white;
  font-size: 16px;
  padding: 10px 0;
  border-radius: 4px;
}

.save-btn[disabled] {
  background-color: #ccc;
  color: #fff;
}

/* 加载中蒙层 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-content {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-icon {
  width: 40px;
  height: 40px;
  margin-bottom: 10px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 