<view class="collage-container">
  <!-- 使用自定义导航栏组件 -->
  <navigation-bar 
    title="图片拼图" 
    background="#ffffff" 
    color="#000000" 
    back="{{true}}" 
    bindback="cancelCollage">
  </navigation-bar>
  
  <!-- 图片预览区域 -->
  <view class="preview-container">
    <canvas type="2d" id="collageCanvas" class="collage-canvas"></canvas>
  </view>
  
  <!-- 拼图模板选择区域 -->
  <view class="template-selector">
    <view class="section-title">选择布局</view>
    <scroll-view scroll-x="true" class="template-scroll">
      <view class="template-item {{selectedTemplate === 'template1' ? 'active' : ''}}" 
            data-template="template1" 
            bindtap="selectTemplate">
        <view class="template-preview template1"></view>
        <text>2张横排</text>
      </view>
      <view class="template-item {{selectedTemplate === 'template2' ? 'active' : ''}}" 
            data-template="template2" 
            bindtap="selectTemplate">
        <view class="template-preview template2"></view>
        <text>2张竖排</text>
      </view>
      <view class="template-item {{selectedTemplate === 'template3' ? 'active' : ''}}" 
            data-template="template3" 
            bindtap="selectTemplate">
        <view class="template-preview template3"></view>
        <text>4张方格</text>
      </view>
      <view class="template-item {{selectedTemplate === 'template4' ? 'active' : ''}}" 
            data-template="template4" 
            bindtap="selectTemplate">
        <view class="template-preview template4"></view>
        <text>3张横排</text>
      </view>
      <view class="template-item {{selectedTemplate === 'template5' ? 'active' : ''}}" 
            data-template="template5" 
            bindtap="selectTemplate">
        <view class="template-preview template5"></view>
        <text>6张网格</text>
      </view>
    </scroll-view>
  </view>
  
  <!-- 间距和背景色设置 -->
  <view class="settings-panel">
    <view class="setting-item">
      <text class="setting-label">间距</text>
      <slider 
        min="0" 
        max="20" 
        value="{{spacing}}" 
        block-size="20"
        activeColor="#07c160"
        bindchange="onSpacingChange"
      />
      <text class="setting-value">{{spacing}}px</text>
    </view>
    
    <view class="setting-item">
      <text class="setting-label">背景色</text>
      <view class="color-picker">
        <view class="color-item {{backgroundColor === '#FFFFFF' ? 'active' : ''}}" 
              style="background-color: #FFFFFF;" 
              data-color="#FFFFFF" 
              bindtap="selectBackgroundColor"></view>
        <view class="color-item {{backgroundColor === '#000000' ? 'active' : ''}}" 
              style="background-color: #000000;" 
              data-color="#000000" 
              bindtap="selectBackgroundColor"></view>
        <view class="color-item {{backgroundColor === '#F5F5F5' ? 'active' : ''}}" 
              style="background-color: #F5F5F5;" 
              data-color="#F5F5F5" 
              bindtap="selectBackgroundColor"></view>
        <view class="color-item {{backgroundColor === '#FFD700' ? 'active' : ''}}" 
              style="background-color: #FFD700;" 
              data-color="#FFD700" 
              bindtap="selectBackgroundColor"></view>
        <view class="color-item {{backgroundColor === '#87CEEB' ? 'active' : ''}}" 
              style="background-color: #87CEEB;" 
              data-color="#87CEEB" 
              bindtap="selectBackgroundColor"></view>
      </view>
    </view>
  </view>
  
  <!-- 图片管理区域 -->
  <view class="images-panel">
    <view class="section-title">选择图片</view>
    <scroll-view scroll-x="true" class="images-scroll">
      <view class="add-image" bindtap="addImage">
        <view class="add-icon">+</view>
      </view>
      <block wx:for="{{selectedImages}}" wx:key="index">
        <view class="image-item">
          <image src="{{item}}" mode="aspectFill"></image>
          <view class="delete-btn" data-index="{{index}}" catchtap="removeImage">×</view>
        </view>
      </block>
    </scroll-view>
  </view>
  
  <!-- 底部工具栏 -->
  <view class="bottom-toolbar">
    <button class="save-btn" bindtap="saveCollage" disabled="{{!canSave}}">保存拼图</button>
  </view>
  
  <!-- 加载中蒙层 -->
  <view class="loading-mask" wx:if="{{isSaving}}">
    <view class="loading-content">
      <view class="loading-icon"></view>
      <text>保存中...</text>
    </view>
  </view>
</view> 