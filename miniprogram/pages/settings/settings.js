const app = getApp();

Page({
  data: {
    settings: {},
    navBarHeight: app.globalData.navBarHeight || 44,
    statusBarHeight: 20,
    menuButtonHeight: 32,
    themes: [
      { id: 'light', name: '浅色主题' },
      { id: 'dark', name: '深色主题' }
    ],
    isLowPowerMode: false,
    // 常见问题解答
    faqs: [
      {
        question: '如何批量处理多张图片？',
        answer: '在首页点击"批量处理"按钮，或者从相册选择多张图片，进入多图编辑器后可以同时应用滤镜、调整亮度和对比度等。'
      },
      {
        question: '为什么我的照片看起来颜色不太好？',
        answer: '食堂光线通常不理想，建议使用"食堂环境预设"或"增鲜"滤镜，可以自动调整适合食堂环境的参数。'
      },
      {
        question: '如何快速添加日期和餐次信息？',
        answer: '使用"快速模式"功能，它会自动根据当前时间添加日期和判断是早餐、午餐还是晚餐。'
      },
      {
        question: '如何保存我常用的编辑风格？',
        answer: '在编辑完成后，点击底部的"模板"按钮，选择"保存为模板"，下次编辑时可以直接应用该模板。'
      },
      {
        question: '图片编辑后保存在哪里？',
        answer: '编辑后的图片会保存到手机相册，同时也会在应用的"历史"中保留记录。'
      },
      {
        question: '如何恢复意外关闭的编辑？',
        answer: '应用会自动保存编辑状态，当你重新打开同一张图片时，会询问是否恢复上次的编辑。'
      }
    ],
    showFAQ: false
  },
  
  onLoad() {
    // 设置导航栏高度
    this.setNavBarHeight();
    
    // 加载设置
    this.loadSettings();
    
    // 获取当前低电量模式状态
    this.setData({
      isLowPowerMode: app.globalData.isLowPowerMode
    });
  },
  
  // 设置导航栏高度
  setNavBarHeight() {
    const systemInfo = {
      ...wx.getDeviceInfo(),
      ...wx.getSystemSetting(),
      ...wx.getAppBaseInfo(),
      ...wx.getWindowInfo()
    };
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
    
    const statusBarHeight = systemInfo.statusBarHeight;
    const menuButtonHeight = menuButtonInfo.height;
    const menuButtonTop = menuButtonInfo.top;
    
    // 计算导航栏高度 = 状态栏高度 + 菜单按钮上下边距 * 2 + 菜单按钮高度
    const navBarHeight = statusBarHeight + (menuButtonTop - statusBarHeight) * 2 + menuButtonHeight;
    
    this.setData({
      navBarHeight,
      statusBarHeight,
      menuButtonHeight
    });
  },
  
  // 加载设置
  loadSettings() {
    this.setData({
      settings: app.globalData.settings
    });
  },
  
  // 切换开关设置
  toggleSetting(e) {
    const { setting } = e.currentTarget.dataset;
    const newValue = !this.data.settings[setting];
    
    // 更新本地数据
    const settings = { ...this.data.settings };
    settings[setting] = newValue;
    this.setData({ settings });
    
    // 更新全局设置
    app.globalData.settings[setting] = newValue;
    
    // 保存设置
    app.saveUserSettings();
    
    // 如果是暗黑模式，应用主题
    if (setting === 'darkMode') {
      app.applyTheme();
      
      // 显示提示
      wx.showToast({
        title: newValue ? '已切换到深色模式' : '已切换到浅色模式',
        icon: 'none'
      });
    }
    
    // 如果是高对比度，应用高对比度设置
    if (setting === 'highContrast') {
      this.applyHighContrast(newValue);
      
      // 显示提示
      wx.showToast({
        title: newValue ? '已启用高对比度' : '已关闭高对比度',
        icon: 'none'
      });
    }
    
    // 如果是大字体，应用大字体设置
    if (setting === 'largeText') {
      this.applyLargeText(newValue);
      
      // 显示提示
      wx.showToast({
        title: newValue ? '已启用大字体' : '已关闭大字体',
        icon: 'none'
      });
    }
    
    // 如果是简化界面，应用简化界面设置
    if (setting === 'simplifiedUI') {
      this.applySimplifiedUI(newValue);
      
      // 显示提示
      wx.showToast({
        title: newValue ? '已启用简化界面' : '已关闭简化界面',
        icon: 'none'
      });
    }
    
    // 如果是详细模式，显示相应提示
    if (setting === 'detailMode') {
      // 显示提示
      wx.showToast({
        title: newValue ? '已启用详细模式' : '已切换到简单模式',
        icon: 'none'
      });
    }
  },
  
  // 应用高对比度设置
  applyHighContrast(enabled) {
    // 通过动态添加/移除类名来应用高对比度样式
    if (enabled) {
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      wx.nextTick(() => {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        if (currentPage && currentPage.selectComponent) {
          const pageEl = currentPage.selectComponent('.page');
          if (pageEl) {
            pageEl.addClass('high-contrast');
          }
        }
      });
    } else {
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      wx.nextTick(() => {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        if (currentPage && currentPage.selectComponent) {
          const pageEl = currentPage.selectComponent('.page');
          if (pageEl) {
            pageEl.removeClass('high-contrast');
          }
        }
      });
    }
  },
  
  // 应用大字体设置
  applyLargeText(enabled) {
    // 通过动态添加/移除类名来应用大字体样式
    if (enabled) {
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      wx.nextTick(() => {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        if (currentPage && currentPage.selectComponent) {
          const pageEl = currentPage.selectComponent('.page');
          if (pageEl) {
            pageEl.addClass('large-text');
          }
        }
      });
    } else {
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      wx.nextTick(() => {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        if (currentPage && currentPage.selectComponent) {
          const pageEl = currentPage.selectComponent('.page');
          if (pageEl) {
            pageEl.removeClass('large-text');
          }
        }
      });
    }
  },
  
  // 应用简化界面设置
  applySimplifiedUI(enabled) {
    // 通过动态添加/移除类名来应用简化界面样式
    if (enabled) {
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      wx.nextTick(() => {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        if (currentPage && currentPage.selectComponent) {
          const pageEl = currentPage.selectComponent('.page');
          if (pageEl) {
            pageEl.addClass('simplified-ui');
          }
        }
      });
    } else {
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      wx.nextTick(() => {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        if (currentPage && currentPage.selectComponent) {
          const pageEl = currentPage.selectComponent('.page');
          if (pageEl) {
            pageEl.removeClass('simplified-ui');
          }
        }
      });
    }
  },
  
  // 应用食堂环境预设
  applyFoodHallPreset() {
    // 获取食堂环境预设
    const preset = app.applyFoodHallPreset();
    
    // 保存预设到本地存储，以便编辑器使用
    try {
      wx.setStorageSync('currentEnvironmentPreset', preset);
      
      // 显示提示
      wx.showToast({
        title: '已应用食堂环境预设',
        icon: 'success'
      });
      
      // 显示预设详情
      setTimeout(() => {
        wx.showModal({
          title: '食堂环境预设已应用',
          content: '此预设优化了食堂光线下的图片效果，包括增强亮度、对比度和锐度，以及自动白平衡和噪点抑制。',
          showCancel: false,
          confirmText: '知道了'
        });
      }, 1000);
    } catch (error) {
      console.error('保存环境预设失败:', error);
      wx.showToast({
        title: '应用预设失败',
        icon: 'none'
      });
    }
  },
  
  // 导航返回
  navigateBack() {
    wx.navigateBack({
      delta: 1
    });
  },
  
  // 添加低电量模式切换功能
  toggleLowPowerMode(e) {
    const isLowPowerMode = e.detail.value;
    
    // 调用app中的切换方法
    app.toggleLowPowerMode();
    
    // 更新本地状态
    this.setData({
      isLowPowerMode: app.globalData.isLowPowerMode
    });
  },
  
  // 显示常见问题解答
  showFAQs: function() {
    this.setData({
      showFAQ: true
    });
  },
  
  // 隐藏常见问题解答
  hideFAQs: function() {
    this.setData({
      showFAQ: false
    });
  },
  
  // 跳转到使用指南页面
  goToGuide: function() {
    wx.navigateTo({
      url: '/pages/guide/guide'
    });
  }
}); 