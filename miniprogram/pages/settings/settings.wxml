<!--pages/settings/settings.wxml-->
<view class="page {{settings.darkMode ? 'dark-mode' : ''}} {{settings.highContrast ? 'high-contrast' : ''}} {{settings.largeText ? 'large-text' : ''}} {{settings.simplifiedUI ? 'simplified-ui' : ''}}">
  <!-- 使用统一的导航栏组件 -->
  <navigation-bar 
    title="设置" 
    showBack="{{true}}" 
    backgroundColor="{{settings.darkMode ? '#1E1E1E' : '#ffffff'}}"
    textColor="{{settings.darkMode ? '#ffffff' : '#000000'}}"
    bindback="navigateBack">
  </navigation-bar>
  
  <view class="settings-container" style="padding-top: {{statusBarHeight + navBarHeight}}px;">
    <!-- 主题设置 -->
    <view class="settings-section">
      <view class="section-title">主题设置</view>
      
      <view class="settings-item">
        <view class="item-info">
          <text class="item-label">深色模式</text>
          <text class="item-desc">降低亮度，保护眼睛</text>
        </view>
        <switch checked="{{settings.darkMode}}" color="#FF7D2C" bindchange="toggleSetting" data-setting="darkMode" />
      </view>
      
      <view class="settings-item">
        <view class="item-info">
          <text class="item-label">高对比度</text>
          <text class="item-desc">增强文字与背景对比度</text>
        </view>
        <switch checked="{{settings.highContrast}}" color="#FF7D2C" bindchange="toggleSetting" data-setting="highContrast" />
      </view>
    </view>
    
    <!-- 界面设置 -->
    <view class="settings-section">
      <view class="section-title">界面设置</view>
      
      <view class="settings-item">
        <view class="item-info">
          <text class="item-label">大字体</text>
          <text class="item-desc">增大文字显示尺寸</text>
        </view>
        <switch checked="{{settings.largeText}}" color="#FF7D2C" bindchange="toggleSetting" data-setting="largeText" />
      </view>
      
      <view class="settings-item">
        <view class="item-info">
          <text class="item-label">简化界面</text>
          <text class="item-desc">减少视觉元素，简化操作</text>
        </view>
        <switch checked="{{settings.simplifiedUI}}" color="#FF7D2C" bindchange="toggleSetting" data-setting="simplifiedUI" />
      </view>
      
      <view class="settings-item">
        <view class="item-info">
          <text class="item-label">详细模式</text>
          <text class="item-desc">显示全部编辑功能和选项</text>
        </view>
        <switch checked="{{settings.detailMode}}" color="#FF7D2C" bindchange="toggleSetting" data-setting="detailMode" />
      </view>
    </view>
    
    <!-- 编辑设置 -->
    <view class="settings-section">
      <view class="section-title">编辑设置</view>
      
      <view class="settings-item">
        <view class="item-info">
          <text class="item-label">高质量保存</text>
          <text class="item-desc">保存更高分辨率的图片</text>
        </view>
        <switch checked="{{settings.saveHighQuality}}" color="#FF7D2C" bindchange="toggleSetting" data-setting="saveHighQuality" />
      </view>
      
      <view class="settings-item">
        <view class="item-info">
          <text class="item-label">自动美化</text>
          <text class="item-desc">自动应用食物增鲜效果</text>
        </view>
        <switch checked="{{settings.autoBeautify}}" color="#FF7D2C" bindchange="toggleSetting" data-setting="autoBeautify" />
      </view>
      
      <view class="settings-item">
        <view class="item-info">
          <text class="item-label">操作提示</text>
          <text class="item-desc">显示功能使用提示</text>
        </view>
        <switch checked="{{settings.showTips}}" color="#FF7D2C" bindchange="toggleSetting" data-setting="showTips" />
      </view>
      
      <view class="settings-item">
        <view class="item-info">
          <text class="item-label">手势操作</text>
          <text class="item-desc">启用缩放、旋转等手势</text>
        </view>
        <switch checked="{{settings.gestures}}" color="#FF7D2C" bindchange="toggleSetting" data-setting="gestures" />
      </view>
    </view>
    
    <!-- 环境预设 -->
    <view class="settings-section">
      <view class="section-title">环境预设</view>
      
      <view class="settings-item" bindtap="applyFoodHallPreset">
        <view class="item-info">
          <text class="item-label">食堂环境预设</text>
          <text class="item-desc">优化食堂光线下的图片效果</text>
        </view>
        <view class="item-action">
          <text class="iconfont icon-right"></text>
        </view>
      </view>
      
      <view class="settings-item">
        <view class="item-info">
          <text class="item-label">低电量模式</text>
          <text class="item-desc">测试低电量模式效果</text>
        </view>
        <switch checked="{{isLowPowerMode}}" color="#FF7D2C" bindchange="toggleLowPowerMode" />
      </view>
    </view>
    
    <!-- 关于信息 -->
    <view class="settings-section">
      <view class="section-title">关于</view>
      
      <view class="about-info">
        <image class="app-logo" src="/images/logo.png" mode="aspectFit"></image>
        <text class="app-name">图片助手</text>
        <text class="app-version">版本 1.0.0</text>
        <text class="app-desc">为校园食堂义工家长提供图片管理工具</text>
      </view>
    </view>
    
    <!-- 常见问题解答按钮 -->
    <view class="settings-section">
      <view class="section-title">帮助</view>
      <view class="setting-item" bindtap="showFAQs">
        <view class="setting-label">常见问题解答</view>
        <view class="setting-action">
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
      <view class="setting-item" bindtap="goToGuide">
        <view class="setting-label">使用指南</view>
        <view class="setting-action">
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 常见问题解答弹窗 -->
<view class="faq-overlay" wx:if="{{showFAQ}}">
  <view class="faq-panel">
    <view class="faq-header">
      <text class="faq-title">常见问题解答</text>
      <view class="faq-close" bindtap="hideFAQs">×</view>
    </view>
    <scroll-view scroll-y="true" class="faq-content">
      <view class="faq-item" wx:for="{{faqs}}" wx:key="question">
        <view class="faq-question">{{item.question}}</view>
        <view class="faq-answer">{{item.answer}}</view>
      </view>
    </scroll-view>
  </view>
</view> 