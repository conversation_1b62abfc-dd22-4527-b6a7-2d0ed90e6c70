/* pages/settings/settings.wxss */

.page {
  min-height: 100vh;
  background-color: var(--color-bg-light);
}

.settings-container {
  padding: 0 0 40rpx 0;
}

/* 设置区块 */
.settings-section {
  margin: 30rpx 30rpx;
  border-radius: var(--border-radius-large);
  background-color: var(--color-bg-base);
  overflow: hidden;
  box-shadow: var(--shadow-base);
}

.section-title {
  padding: 30rpx;
  font-size: var(--font-size-base);
  font-weight: 500;
  color: var(--color-text-secondary);
}

/* 设置项 */
.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-top: 1px solid var(--color-border);
}

.item-info {
  flex: 1;
  margin-right: 20rpx;
}

.item-label {
  display: block;
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  margin-bottom: 6rpx;
}

.item-desc {
  display: block;
  font-size: var(--font-size-small);
  color: var(--color-text-secondary);
}

/* 设置项操作按钮 */
.item-action {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
}

.item-action .iconfont {
  font-size: 36rpx;
}

/* 关于信息 */
.about-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 30rpx;
}

.app-logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  border-radius: var(--border-radius-base);
}

.app-name {
  font-size: var(--font-size-large);
  font-weight: bold;
  color: var(--color-text-primary);
  margin-bottom: 10rpx;
}

.app-version {
  font-size: var(--font-size-small);
  color: var(--color-text-secondary);
  margin-bottom: 20rpx;
}

.app-desc {
  font-size: var(--font-size-small);
  color: var(--color-text-secondary);
  text-align: center;
}

/* 深色模式样式调整 */
.dark-mode .settings-section {
  background-color: var(--color-bg-base);
}

/* 简化界面样式调整 */
.simplified-ui .settings-section {
  box-shadow: none;
  border: 1px solid var(--color-border);
}

/* 大字体模式样式调整 */
.large-text .item-label {
  margin-bottom: 10rpx;
}

/* 高对比度模式样式调整 */
.high-contrast .settings-item {
  border-top: 2px solid var(--color-border);
}

/* 开关组件样式 */
switch {
  transform: scale(0.9);
} /* 常见问题解答样式 */
.faq-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.faq-panel {
  width: 85%;
  max-width: 350px;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 80vh;
}

.faq-header {
  padding: 15px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.faq-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.faq-close {
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  font-size: 20px;
  color: #999;
}

.faq-content {
  padding: 15px;
  max-height: calc(80vh - 50px);
}

.faq-item {
  margin-bottom: 20px;
}

.faq-item:last-child {
  margin-bottom: 0;
}

.faq-question {
  font-size: 16px;
  font-weight: bold;
  color: #07c160;
  margin-bottom: 8px;
}

.faq-answer {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}
