Page({
  data: {
    tools: [
      {
        id: 'crop',
        title: '智能裁剪',
        subtitle: '一键裁剪',
        description: '智能识别图片内容，快速裁剪，支持多种比例和自由裁剪',
        icon: '✂',
        bgColor: '#8D6E63'
      },
      {
        id: 'collage',
        title: '图片拼接',
        subtitle: '创意拼图',
        description: '支持多种布局模板，自由调整位置和大小，让照片更有创意',
        icon: '⊞',
        bgColor: '#795548'
      }
    ]
  },
  
  onLoad() {
    // 页面加载时的初始化逻辑
    this.initAdComponent();
  },
  
  // 初始化广告组件
  initAdComponent() {
    console.log('初始化广告组件');
  },
  
  // 广告相关事件处理
  adLoad() {
    console.log('广告加载成功');
  },
  
  adError(err) {
    console.error('广告加载失败', err);
  },
  
  adClose() {
    console.log('广告关闭');
  },
  
  // 导航到功能页面
  navigateToPage(e) {
    const page = e.currentTarget.dataset.page;
    const pages = {
      crop: '/pages/crop/crop',
      collage: '/pages/collage/collage',
      camera: '/pages/camera/camera',
      album: '/pages/album/album'
    };
    
    if (pages[page]) {
      wx.navigateTo({
        url: pages[page],
        fail: (err) => {
          console.error('页面跳转失败', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    }
  },
  
  // 打开工具详情
  openToolDetail(e) {
    const toolId = e.currentTarget.dataset.id;
    const tool = this.data.tools.find(item => item.id === toolId);
    
    if (tool) {
      this.navigateToPage({ currentTarget: { dataset: { page: toolId } } });
    }
  },
  
  // 显示反馈对话框
  showFeedback() {
    wx.showModal({
      title: '意见反馈',
      content: '如果您有任何建议或问题，欢迎通过以下方式反馈：\n1. 在线客服\n2. 发送邮件\n3. 微信群交流',
      showCancel: true,
      cancelText: '取消',
      confirmText: '联系我们',
      success: (res) => {
        if (res.confirm) {
          // 复制客服微信号
          wx.setClipboardData({
            data: 'customer_service_wechat',
            success: () => {
              wx.showToast({
                title: '已复制客服微信号',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  },

  // 支持右上角转发到好友
  onShareAppMessage: function() {
    return {
      title: '图片编辑小助手 - 让创作更简单',
      path: '/pages/index/index',
      imageUrl: '/images/app.png'
    };
  },
  
  // 支持右上角转发到朋友圈
  onShareTimeline: function() {
    return {
      title: '图片编辑小助手 - 让创作更简单',
      imageUrl: '/images/app.png'
    };
  }
});