<!--pages/mine/mine.wxml-->
<view class="container">
  <!-- 顶部标题区域 -->
  <view class="header">
    <text class="title">图片编辑小助手</text>
    <view class="subtitle">
      <text>专业图片处理</text>
      <text>让创作更简单</text>
    </view>
  </view>
  
  <!-- 功能特色区域 -->
  <view class="features-section">
    <view class="feature-grid">
      <view class="feature-item" bindtap="navigateToPage" data-page="crop">
        <view class="feature-icon crop-icon">✂</view>
        <text class="feature-text">智能裁剪</text>
      </view>
      <view class="feature-item" bindtap="navigateToPage" data-page="collage">
        <view class="feature-icon collage-icon">⊞</view>
        <text class="feature-text">拼图制作</text>
      </view>
      <view class="feature-item" bindtap="navigateToPage" data-page="camera">
        <view class="feature-icon camera-icon">📷</view>
        <text class="feature-text">拍照编辑</text>
      </view>
      <view class="feature-item" bindtap="navigateToPage" data-page="album">
        <view class="feature-icon album-icon">🖼</view>
        <text class="feature-text">相册管理</text>
      </view>
    </view>
  </view>
  
  <!-- 推荐工具集 -->
  <view class="tools-section">
    <view class="section-header">
      <text class="section-title">推荐工具集</text>
    </view>
    <view class="section-subtitle">精选工具集，为您学习助力</view>
    
    <!-- 工具卡片 -->
    <view class="tool-cards">
      <!-- 工具卡片列表 -->
      <view wx:for="{{tools}}" wx:key="id" class="tool-card" bindtap="openToolDetail" data-id="{{item.id}}">
        <view class="tool-icon" style="background-color: {{item.bgColor}}">{{item.title[0]}}</view>
        <view class="tool-info">
          <view class="tool-title">{{item.title}} <text class="tool-subtitle">{{item.subtitle}}</text></view>
          <view class="tool-desc">{{item.description}}</view>
        </view>
        <view class="tool-arrow">></view>
      </view>
    </view>
  </view>
  
  <!-- 分享推广区域 -->
  <view class="share-section">
    <view class="qr-container">
      <image class="qr-code" src="/images/app.png" mode="aspectFit"/>
    </view>
    <text class="share-text">分享小程序</text>
    <text class="share-tip">长按或截图保存二维码</text>
    
    <view class="share-buttons">
      <button class="share-btn" open-type="share">邀请好友</button>
      <button class="feedback-btn" bindtap="showFeedback">意见反馈</button>
    </view>
  </view>
</view>

<!-- 广告位 -->
<ad unit-id="adunit-4d32e59fd00c9e40" ad-type="video" ad-theme="white" bindload="adLoad" binderror="adError" bindclose="adClose"></ad>