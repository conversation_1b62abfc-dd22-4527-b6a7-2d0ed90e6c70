/* pages/mine/mine.wxss */
.container {
  min-height: 100vh;
  background-color: #F0EAD6;
  display: flex;
  flex-direction: column;
  padding: 40rpx 30rpx;
}

/* 顶部标题区域 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding-top: 60rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #5D4037;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #795548;
  display: flex;
  flex-direction: column;
  align-items: center;
  line-height: 1.4;
}

/* 功能特色区域 */
.features-section {
  margin: 30rpx 0;
  padding: 20rpx;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
  padding: 20rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.feature-item:active {
  transform: scale(0.95);
}

.feature-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #8D6E63;
  color: #FFF3E0;
}

.feature-text {
  font-size: 28rpx;
  color: #5D4037;
  font-weight: 500;
}

/* 推荐工具集区域 */
.tools-section {
  margin: 30rpx 0;
  padding: 30rpx;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #5D4037;
  position: relative;
  display: inline-block;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -8rpx;
  left: 0;
  width: 40rpx;
  height: 6rpx;
  background-color: #8D6E63;
  border-radius: 3rpx;
}

.section-subtitle {
  font-size: 28rpx;
  color: #795548;
  margin: 20rpx 0;
}

/* 工具卡片 */
.tool-cards {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.tool-card {
  display: flex;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  position: relative;
  transition: all 0.3s ease;
}

.tool-card:active {
  transform: scale(0.98);
}

.tool-icon {
  width: 88rpx;
  height: 88rpx;
  border-radius: 18rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFF3E0;
  font-size: 40rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tool-info {
  flex: 1;
  overflow: hidden;
}

.tool-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #5D4037;
  margin-bottom: 10rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tool-subtitle {
  font-size: 24rpx;
  color: #795548;
  font-weight: normal;
  margin-left: 10rpx;
}

.tool-desc {
  font-size: 26rpx;
  color: #795548;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-top: 6rpx;
}

.tool-arrow {
  width: 30rpx;
  height: 30rpx;
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #8D6E63;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 分享推广区域 */
.share-section {
  margin-top: 40rpx;
  padding: 40rpx;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qr-container {
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 20rpx;
  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.1);
}

.qr-code {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

.share-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #5D4037;
  margin-bottom: 16rpx;
}

.share-tip {
  font-size: 28rpx;
  color: #795548;
  margin-bottom: 40rpx;
}

.share-buttons {
  display: flex;
  gap: 30rpx;
  width: 100%;
}

.share-btn, .feedback-btn {
  flex: 1;
  background-color: #8D6E63;
  color: #FFF3E0;
  border: none;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.share-btn:active, .feedback-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 广告位样式 */
ad {
  margin-top: 40rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}