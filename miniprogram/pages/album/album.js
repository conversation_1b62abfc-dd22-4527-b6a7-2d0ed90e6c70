// pages/album/album.js
const app = getApp()

Page({
  data: {
    statusBarHeight: 0,
    navBarHeight: 0,
    isMultiSelect: true, // 默认开启多选模式
    selectedImages: [],
    maxSelect: 9,
    hasAlbumAuth: false,
    currentCategory: 'all', // 当前选中的分类：all, recent, food, favorite
    recentPhotos: [], // 最近照片
    albumPhotos: [], // 相册照片
    isEmpty: true, // 当前分类是否为空
    isRecentEmpty: true, // 最近照片是否为空
    isLoading: false, // 是否正在加载图片
    userSelectionHistory: [] // 用户选择历史记录
  },

  onLoad: function(options) {
    // 获取系统信息
    this.getSystemInfo();
    
    // 获取相册权限
    this.checkAlbumPermission();
    
    // 检查是否直接进入多选模式
    if (options && options.mode === 'multiSelect') {
      this.setData({
        isMultiSelect: true
      });
    }
    
    // 使用EventChannel获取额外数据
    const eventChannel = this.getOpenerEventChannel();
    if (eventChannel && eventChannel.on) {
      eventChannel.on('acceptDataFromOpenerPage', (data) => {
        if (data && data.isMultiSelect) {
          this.setData({
            isMultiSelect: true
          });
        }
      });
    }
    
    // 加载用户选择历史
    this.loadUserSelectionHistory();
  },
  
  onShow: function() {
    // 加载用户历史记录的照片，并清理无效记录
    this.validateAndCleanUserHistory();
    this.updateRecentPhotosFromHistory();
  },
  
  // 验证并清理用户历史记录
  validateAndCleanUserHistory: function() {
    try {
      const fs = wx.getFileSystemManager();
      const history = wx.getStorageSync('userSelectionHistory') || [];
      let hasChanges = false;
      
      // 过滤掉无效的历史记录
      const validHistory = history.filter(selection => {
        // 验证选择记录是否有效
        if (!selection || !selection.photos || !Array.isArray(selection.photos) || selection.photos.length === 0) {
          hasChanges = true;
          return false;
        }
        
        // 验证记录中的每张照片是否有效
        const validPhotos = selection.photos.filter(photo => {
          if (!photo || !photo.path) return false;
          
          // 检查文件是否存在
          let path = photo.path;
          if (path.indexOf('wxfile://') === 0) {
            path = path.replace('wxfile://', '');
          }
          
          try {
            fs.accessSync(path);
            return true;
          } catch (err) {
            // 文件不存在
            hasChanges = true;
            return false;
          }
        });
        
        // 更新选择记录中的照片
        selection.photos = validPhotos;
        
        // 如果没有有效照片，过滤掉这条记录
        return validPhotos.length > 0;
      });
      
      // 如果有变化，更新存储
      if (hasChanges || validHistory.length !== history.length) {
        wx.setStorageSync('userSelectionHistory', validHistory);
        this.setData({ userSelectionHistory: validHistory });
        console.log('已清理无效的历史记录');
      }
    } catch (error) {
      console.error('验证历史记录失败:', error);
    }
  },
  
  // 获取系统信息
  getSystemInfo: function() {
    // 使用新的API替代已废弃的wx.getSystemInfoSync
    const systemInfo = {
      ...wx.getDeviceInfo(),
      ...wx.getSystemSetting(),
      ...wx.getAppBaseInfo(),
      ...wx.getWindowInfo()
    };
    
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight || 20,
      navBarHeight: app.globalData.navBarHeight || 44
    });
  },
  
  // 选择照片 - 手动触发打开相册
  choosePhotos: function() {
    if (!this.data.hasAlbumAuth) {
      this.requestAlbumPermission();
      return;
    }
    
    this.setData({ isLoading: true });
    
    // 从系统相册获取照片
    wx.showLoading({ title: '加载照片中...' });
    
    // 使用chooseMedia替代chooseImage，支持更多格式
    wx.chooseMedia({
      count: 20, // 一次选择最多20张
      mediaType: ['image'],
      sourceType: ['album'],
      camera: 'back',
      success: (res) => {
        wx.hideLoading();
        
        // 处理照片数据
        const mediaFiles = res.tempFiles || [];
        const albumPhotos = mediaFiles.map((file, index) => {
          return { 
            path: file.tempFilePath, 
            selected: false, 
            index: 0,
            size: file.size,
            width: file.width, 
            height: file.height
          };
        });
        
        // 更新相册照片
        this.setData({ 
          albumPhotos, 
          isEmpty: albumPhotos.length === 0,
          isLoading: false
        });
      },
      fail: (err) => {
        console.error('选择照片失败:', err);
        wx.hideLoading();
        
        if (err.errMsg.indexOf('cancel') === -1) {
          wx.showToast({
            title: '无法访问照片',
            icon: 'none'
          });
        }
        
        this.setData({ isLoading: false });
      }
    });
  },
  
  // 加载用户选择历史
  loadUserSelectionHistory: function() {
    try {
      // 从本地存储获取用户选择历史
      const history = wx.getStorageSync('userSelectionHistory') || [];
      this.setData({ userSelectionHistory: history });
      
      // 更新最近照片
      this.updateRecentPhotosFromHistory();
    } catch (error) {
      console.error('加载用户选择历史失败:', error);
    }
  },
  
  // 从历史记录更新最近照片
  updateRecentPhotosFromHistory: function() {
    const { userSelectionHistory } = this.data;
    let recentPhotos = [];
    
    // 提取所有历史选择的照片（最多保留前30张）
    userSelectionHistory.forEach(selection => {
      if (selection && selection.photos) {
        recentPhotos = recentPhotos.concat(selection.photos);
      }
    });
    
    // 去除可能的重复项
    const uniquePaths = new Set();
    recentPhotos = recentPhotos.filter(photo => {
      if (uniquePaths.has(photo.path)) return false;
      uniquePaths.add(photo.path);
      return true;
    });
    
    // 检查每张照片文件是否仍然存在
    const fs = wx.getFileSystemManager();
    const validPhotos = [];
    
    for (const photo of recentPhotos) {
      let isValid = false;
      
      // 处理路径格式
      let path = photo.path;
      if (path.indexOf('wxfile://') === 0) {
        path = path.replace('wxfile://', '');
      }
      
      // 尝试多种路径格式
      const pathVariations = [
        path,
        path.startsWith('/') ? path : '/' + path
      ];
      
      // 如果是临时文件路径，添加更多变种
      if (path.includes('tmp_') || path.match(/[A-Za-z0-9]{6,}/)) {
        const fileName = path.split('/').pop();
        if (!path.includes('/tmp/')) {
          pathVariations.push('/tmp/' + fileName);
        }
      }
      
      // 检查所有路径变种
      for (const testPath of pathVariations) {
        try {
          fs.accessSync(testPath);
          // 文件存在，更新路径并标记为有效
          photo.path = testPath.startsWith('/') ? 'wxfile://' + testPath : testPath;
          isValid = true;
          break;
        } catch (err) {
          // 路径无效，继续尝试下一个
          continue;
        }
      }
      
      // 只保留有效的照片
      if (isValid) {
        validPhotos.push(photo);
      } else {
        console.log('从最近照片中移除无效文件:', photo.path);
      }
    }
    
    // 最多显示30张最近照片
    const result = validPhotos.slice(0, 30);
    
    // 更新数据
    this.setData({ 
      recentPhotos: result,
      isRecentEmpty: result.length === 0
    });
    
    // 清理历史记录中的无效路径
    this._cleanInvalidPathsInHistory();
  },
  
  // 清理历史记录中的无效文件路径
  _cleanInvalidPathsInHistory: function() {
    try {
      const history = [...this.data.userSelectionHistory];
      const fs = wx.getFileSystemManager();
      let hasChanges = false;
      
      // 遍历所有历史记录
      for (let i = 0; i < history.length; i++) {
        const selection = history[i];
        if (!selection || !selection.photos || !Array.isArray(selection.photos)) continue;
        
        // 过滤每个选择记录中的照片
        const validPhotos = [];
        for (const photo of selection.photos) {
          let path = photo.path;
          if (path.indexOf('wxfile://') === 0) {
            path = path.replace('wxfile://', '');
          }
          
          try {
            fs.accessSync(path);
            validPhotos.push(photo);
          } catch (err) {
            // 文件不存在，跳过
            hasChanges = true;
            console.log('从历史记录中移除无效文件:', photo.path);
          }
        }
        
        // 更新这个记录中的照片
        history[i].photos = validPhotos;
      }
      
      // 过滤掉空的选择记录
      const filteredHistory = history.filter(selection => 
        selection && selection.photos && selection.photos.length > 0
      );
      
      // 如果有变化，更新存储
      if (hasChanges || filteredHistory.length !== history.length) {
        this.setData({ userSelectionHistory: filteredHistory });
        wx.setStorageSync('userSelectionHistory', filteredHistory);
        console.log('已清理历史记录中的无效文件路径');
      }
    } catch (error) {
      console.error('清理历史记录失败:', error);
    }
  },
  
  // 检查相册权限
  checkAlbumPermission: function() {
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.writePhotosAlbum']) {
          // 已授权访问相册
          this.setData({ hasAlbumAuth: true });
        } else {
          this.setData({ hasAlbumAuth: false });
        }
      }
    });
  },
  
  // 请求相册权限
  requestAlbumPermission: function() {
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.writePhotosAlbum']) {
          // 已授权访问相册
          this.setData({ hasAlbumAuth: true });
          this.choosePhotos();
        } else {
          // 未授权，尝试请求权限
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              this.setData({ hasAlbumAuth: true });
              this.choosePhotos();
            },
            fail: () => {
              this.setData({ hasAlbumAuth: false });
              wx.showModal({
                title: '提示',
                content: '需要您授权访问相册才能选择照片',
                confirmText: '去授权',
                success: (res) => {
                  if (res.confirm) {
                    wx.openSetting({
                      success: (settingRes) => {
                        if (settingRes.authSetting['scope.writePhotosAlbum']) {
                          this.setData({ hasAlbumAuth: true });
                          this.choosePhotos();
                        }
                      }
                    });
                  }
                }
              });
            }
          });
        }
      }
    });
  },
  
  // 切换分类
  switchCategory: function(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({ currentCategory: category });
    this.updateEmptyState();
  },
  
  // 更新空状态
  updateEmptyState: function() {
    const { currentCategory, recentPhotos, albumPhotos } = this.data;
    let isEmpty = false;
    const isRecentEmpty = recentPhotos.length === 0;
    
    if (currentCategory === 'recent') {
      isEmpty = isRecentEmpty;
    } else {
      isEmpty = albumPhotos.length === 0;
    }
    
    this.setData({ isEmpty, isRecentEmpty });
  },
  
  // 选择/取消选择图片
  toggleSelectImage: function(e) {
    const { index, source } = e.currentTarget.dataset;
    const photoList = source === 'recent' ? 'recentPhotos' : 'albumPhotos';
    const photos = [...this.data[photoList]];
    
    // 如果照片已选中，取消选择
    if (photos[index].selected) {
      // 获取当前索引
      const currentIndex = photos[index].index;
      
      // 取消选择并更新其他照片的索引
      photos[index].selected = false;
      photos[index].index = 0;
      
      // 更新selectedImages数组
      const selectedImages = this.data.selectedImages.filter(
        item => item.path !== photos[index].path
      );
      
      // 更新其他已选照片的索引
      selectedImages.forEach((item, i) => {
        if (item.index > currentIndex) {
          item.index -= 1;
        }
      });
      
      // 同时更新两个照片列表中相同照片的状态
      if (source === 'recent') {
        // 在albumPhotos中查找对应照片并更新
        const albumPhotos = [...this.data.albumPhotos];
        const albumIndex = albumPhotos.findIndex(item => item.path === photos[index].path);
        if (albumIndex !== -1) {
          albumPhotos[albumIndex].selected = false;
          albumPhotos[albumIndex].index = 0;
          this.setData({ albumPhotos });
        }
      } else {
        // 在recentPhotos中查找对应照片并更新
        const recentPhotos = [...this.data.recentPhotos];
        const recentIndex = recentPhotos.findIndex(item => item.path === photos[index].path);
        if (recentIndex !== -1) {
          recentPhotos[recentIndex].selected = false;
          recentPhotos[recentIndex].index = 0;
          this.setData({ recentPhotos });
        }
      }
      
      // 更新数据
      this.setData({
        [photoList]: photos,
        selectedImages
      });
    } else {
      // 检查是否已达到最大选择数量
      if (this.data.selectedImages.length >= this.data.maxSelect) {
        wx.showToast({
          title: `最多选择${this.data.maxSelect}张照片`,
          icon: 'none'
        });
        return;
      }
      
      // 添加到选中列表
      const selectedImages = [...this.data.selectedImages];
      const newIndex = selectedImages.length + 1;
      
      photos[index].selected = true;
      photos[index].index = newIndex;
      
      selectedImages.push({
        path: photos[index].path,
        index: newIndex,
        width: photos[index].width,
        height: photos[index].height
      });
      
      // 同时更新两个照片列表中相同照片的状态
      if (source === 'recent') {
        // 在albumPhotos中查找对应照片并更新
        const albumPhotos = [...this.data.albumPhotos];
        const albumIndex = albumPhotos.findIndex(item => item.path === photos[index].path);
        if (albumIndex !== -1) {
          albumPhotos[albumIndex].selected = true;
          albumPhotos[albumIndex].index = newIndex;
          this.setData({ albumPhotos });
        }
      } else {
        // 在recentPhotos中查找对应照片并更新
        const recentPhotos = [...this.data.recentPhotos];
        const recentIndex = recentPhotos.findIndex(item => item.path === photos[index].path);
        if (recentIndex !== -1) {
          recentPhotos[recentIndex].selected = true;
          recentPhotos[recentIndex].index = newIndex;
          this.setData({ recentPhotos });
        }
      }
      
      // 更新数据
      this.setData({
        [photoList]: photos,
        selectedImages
      });
    }
  },
  
  // 完成多选
  completeMultiSelect: function() {
    const { selectedImages } = this.data;
    
    if (selectedImages.length === 0) {
      wx.showToast({
        title: '请至少选择一张照片',
        icon: 'none'
      });
      return;
    }
    
    // 记录此次选择的图片到历史记录
    this.saveSelectionToHistory(selectedImages);
    
    // 检查和格式化图片路径
    const formattedImages = [];
    const fs = wx.getFileSystemManager();
    const invalidPaths = [];
    
    for (const img of selectedImages) {
      let path = img.path;
      
      // 确保临时文件路径格式正确
      if (path.indexOf('wxfile://') === 0) {
        path = path.replace('wxfile://', '');
      }
      
      // 增强路径验证和修复
      let isValid = false;
      let validPath = path;
      
      // 尝试多种临时文件路径格式
      const pathsToTry = [
        path, // 原始路径
        path.startsWith('/') ? path : '/' + path, // 确保以/开头
      ];
      
      // 如果路径中没有/tmp/但包含随机字符，可能是临时文件
      if (!path.includes('/tmp/') && 
          (path.includes('tmp_') || path.match(/[A-Za-z0-9]{6,}/))) {
        const fileName = path.split('/').pop();
        pathsToTry.push('/tmp/' + fileName);
      }
      
      // 检查所有可能的路径
      console.log('检查以下路径变种:', pathsToTry);
      
      for (const testPath of pathsToTry) {
        try {
          fs.accessSync(testPath);
          console.log('找到有效路径:', testPath);
          validPath = testPath;
          isValid = true;
          break;
        } catch (err) {
          console.log('路径无效:', testPath);
        }
      }
      
      if (isValid) {
        console.log('使用有效路径:', validPath);
        formattedImages.push(validPath);
      } else {
        console.error('文件不存在，无法修复路径:', path);
        invalidPaths.push(path);
      }
    }
    
    // 提示用户无效图片情况
    if (invalidPaths.length > 0) {
      console.error('以下图片无法访问:', invalidPaths);
      wx.showToast({
        title: `${invalidPaths.length}张图片无法访问`,
        icon: 'none',
        duration: 2000
      });
      
      // 如果所有图片都无效，则停止操作
      if (formattedImages.length === 0) {
        wx.showModal({
          title: '提示',
          content: '所选图片均无法访问，请重新选择',
          showCancel: false
        });
        return;
      }
    }
    
    // 根据选择的图片数量决定跳转到哪个编辑页面
    if (formattedImages.length === 1) {
      // 单图编辑，现在也使用multi-editor页面
      wx.navigateTo({
        url: '/pages/multi-editor/multi-editor',
        success: (res) => {
          res.eventChannel.emit('acceptDataFromOpenerPage', { 
            images: [formattedImages[0]]
          });
        }
      });
    } else {
      // 多图编辑
      wx.navigateTo({
        url: '/pages/multi-editor/multi-editor',
        success: (res) => {
          res.eventChannel.emit('acceptDataFromOpenerPage', { 
            images: formattedImages
          });
        }
      });
    }
  },
  
  // 保存选择记录到历史
  saveSelectionToHistory: function(selectedImages) {
    try {
      // 创建新的选择记录
      const newSelection = {
        timestamp: Date.now(),
        date: new Date().toISOString(),
        photos: selectedImages.map(img => ({
          path: img.path,
          width: img.width || 0,
          height: img.height || 0
        }))
      };
      
      // 获取现有历史记录
      let history = [...this.data.userSelectionHistory];
      
      // 将新记录添加到最前面
      history.unshift(newSelection);
      
      // 只保留最近3次选择记录
      if (history.length > 3) {
        history = history.slice(0, 3);
      }
      
      // 更新本地存储和数据
      wx.setStorageSync('userSelectionHistory', history);
      this.setData({ userSelectionHistory: history });
      
      // 更新最近照片显示
      this.updateRecentPhotosFromHistory();
    } catch (error) {
      console.error('保存选择记录失败:', error);
    }
  },
  
  // 重新选择照片
  reloadPhotos: function() {
    if (this.data.isLoading) return;
    
    this.setData({
      selectedImages: [],
      albumPhotos: []
    });
    
    this.choosePhotos();
  },
  
  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  }
});