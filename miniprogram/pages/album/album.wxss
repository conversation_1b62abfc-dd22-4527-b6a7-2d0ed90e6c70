/* pages/album/album.wxss */

/* 页面容器 */
.container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
}

/* 相册内容区域 */
.album-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 相册容器 */
.album-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 分类选择栏 */
.category-tabs {
  display: flex;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  overflow-x: auto;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
}

/* 选择照片按钮 */
.choose-photo-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FF7D2C;
  color: white;
  padding: 20rpx 0;
  margin: 20rpx 20rpx 0;
  border-radius: 10rpx;
  font-size: 28rpx;
  box-shadow: 0 4rpx 10rpx rgba(255, 125, 44, 0.2);
}

.choose-photo-btn .iconfont {
  margin-right: 10rpx;
  font-size: 32rpx;
}

/* 底部操作栏 */
.bottom-bar {
  width: 100%;
  height: 100rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  padding: 0 30rpx;
  box-sizing: border-box;
  padding-bottom: env(safe-area-inset-bottom);
}

.bottom-bar-button {
  height: 80rpx;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10rpx;
  border-radius: 40rpx;
  background-color: #f5f5f5;
  color: #666;
  font-size: 28rpx;
}

.bottom-bar-button.primary {
  background-color: #FF7D2C;
  color: #ffffff;
}

/* 无权限提示区域 */
.no-auth-tip {
  width: 100%;
  height: 70vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
  background-color: #ffffff;
}

.no-auth-icon {
  width: 220rpx;
  height: 220rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #f8f8f8, #e8e8e8);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.05);
}

.no-auth-icon .iconfont {
  font-size: 100rpx;
  color: #FF7D2C;
  text-shadow: 0 2rpx 5rpx rgba(255, 125, 44, 0.2);
}

.no-auth-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
  background: #f9f9f9;
  padding: 30rpx 40rpx;
  border-radius: 20rpx;
  width: 80%;
  line-height: 1.6;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.03);
}

.no-auth-button {
  padding: 20rpx 80rpx;
  background: linear-gradient(135deg, #FF9D5C, #FF7D2C);
  color: white;
  border-radius: 50rpx;
  font-size: 30rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 20rpx rgba(255, 125, 44, 0.3);
  transition: all 0.3s ease;
}

.no-auth-button:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 10rpx rgba(255, 125, 44, 0.2);
}

.category-tab {
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
  flex-shrink: 0;
}

.category-tab.active {
  color: #FF7D2C;
  font-weight: 500;
}

.category-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 30rpx;
  right: 30rpx;
  height: 4rpx;
  background-color: #FF7D2C;
  border-radius: 2rpx;
}

/* 多选提示 */
.multi-select-tip {
  padding: 20rpx 20rpx;
  background-color: #fff;
  font-size: 28rpx;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eee;
}

/* 最近照片区域 */
.recent-photos-section {
  background-color: #fff;
  padding-bottom: 20rpx;
  margin-bottom: 20rpx;
}

.section-title {
  padding: 20rpx;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-subtitle {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
}

.recent-photos-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 0 10rpx;
}

/* 相册网格 */
.album-grid {
  flex: 1;
  background-color: #fff;
  overflow-y: auto;
}

.photo-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 0 10rpx;
}

/* 照片项样式 */
.photo-item {
  position: relative;
  width: calc(33.333% - 10rpx);
  margin: 5rpx;
  aspect-ratio: 1;
  overflow: hidden;
  border-radius: 8rpx;
  background-color: #f0f0f0;
}

.photo-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.photo-selected-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
}

.photo-selected-mark {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background-color: #FF7D2C;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
}

/* 空状态样式 */
.empty-state {
  width: 100%;
  padding: 60rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.empty-state .iconfont {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.empty-button {
  padding: 15rpx 40rpx;
  background-color: #FF7D2C;
  color: white;
  border-radius: 50rpx;
  font-size: 28rpx;
}

/* 加载状态样式 */
.loading-state {
  width: 100%;
  padding: 60rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 125, 44, 0.2);
  border-top: 4rpx solid #FF7D2C;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
}