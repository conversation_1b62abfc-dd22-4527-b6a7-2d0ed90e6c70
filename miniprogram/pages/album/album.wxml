<!--pages/album/album.wxml-->
<view class="container">
  <!-- 使用navigation-bar组件 -->
  <navigation-bar 
    title="" 
    showBack="{{false}}" 
    backgroundColor="#ffffff"
    textColor="#000000"
    bindback="goBack">
  </navigation-bar>
  
  <!-- 相册内容区域 -->
  <view class="album-content">
    <!-- 相册权限提示 -->
    <view wx:if="{{!hasAlbumAuth}}" class="no-auth-tip">
      <view class="no-auth-icon">
        <text class="iconfont icon-picture"></text>
      </view>
      <view class="no-auth-text">需要访问您的相册才能选择和编辑照片，记录美好瞬间</view>
      <view class="no-auth-button" bindtap="requestAlbumPermission">授权访问相册</view>
    </view>
    
    <!-- 相册内容 -->
    <view wx:else class="album-container">
      <!-- 分类选择栏 -->
      <view class="category-tabs">
        <view class="category-tab {{currentCategory === 'all' ? 'active' : ''}}" 
              bindtap="switchCategory" data-category="all">
          <text>全部</text>
        </view>
        <view class="category-tab {{currentCategory === 'recent' ? 'active' : ''}}" 
              bindtap="switchCategory" data-category="recent">
          <text>最近</text>
        </view>
        <view class="category-tab {{currentCategory === 'food' ? 'active' : ''}}" 
              bindtap="switchCategory" data-category="food">
          <text>美食</text>
        </view>
        <view class="category-tab {{currentCategory === 'favorite' ? 'active' : ''}}" 
              bindtap="switchCategory" data-category="favorite">
          <text>收藏</text>
        </view>
      </view>
      
      <!-- 选择照片按钮 -->
      <view class="choose-photo-btn" bindtap="choosePhotos">
        <text class="iconfont icon-add"></text>
        <text>选择照片</text>
      </view>
      
      <!-- 多选提示 -->
      <view class="multi-select-tip">
        <text>已选择 {{selectedImages.length}}/{{maxSelect}} 张</text>
      </view>
      
      <!-- 最近照片区域 - 仅在全部或最近分类下显示 -->
      <view wx:if="{{currentCategory === 'all' || currentCategory === 'recent'}}" class="recent-photos-section">
        <view class="section-title">
          <text>最近选择的照片</text>
          <text class="section-subtitle">仅记录前3次选择</text>
        </view>
        <view class="recent-photos-grid">
          <view wx:for="{{recentPhotos}}" wx:key="path" class="photo-item"
                bindtap="toggleSelectImage" data-index="{{index}}" data-source="recent">
            <image src="{{item.path}}" mode="aspectFill"></image>
            <view wx:if="{{item.selected}}" class="photo-selected-mask"></view>
            <view wx:if="{{item.selected}}" class="photo-selected-mark">
              <text>{{item.index}}</text>
            </view>
          </view>
          
          <!-- 空状态提示 -->
          <view wx:if="{{isRecentEmpty && !isLoading}}" class="empty-state">
            <text class="iconfont icon-picture"></text>
            <text class="empty-text">暂无最近选择的照片</text>
          </view>
        </view>
      </view>
      
      <!-- 相册网格 -->
      <view class="album-grid">
        <view class="section-title">
          <text>{{currentCategory === 'all' ? '所有照片' : 
                 currentCategory === 'recent' ? '最近照片' : 
                 currentCategory === 'food' ? '美食照片' : '收藏照片'}}</text>
        </view>
        
        <!-- 照片网格 -->
        <view class="photo-grid">
          <view wx:for="{{currentCategory === 'recent' ? recentPhotos : albumPhotos}}" 
                wx:key="path" 
                class="photo-item"
                bindtap="toggleSelectImage" 
                data-index="{{index}}"
                data-source="{{currentCategory === 'recent' ? 'recent' : 'album'}}">
            <image src="{{item.path}}" mode="aspectFill"></image>
            <view wx:if="{{item.selected}}" class="photo-selected-mask"></view>
            <view wx:if="{{item.selected}}" class="photo-selected-mark">
              <text>{{item.index}}</text>
            </view>
          </view>
          
          <!-- 加载状态 -->
          <view wx:if="{{isLoading}}" class="loading-state">
            <view class="loading-spinner"></view>
            <text class="loading-text">加载中...</text>
          </view>
          
          <!-- 空状态提示 -->
          <view wx:if="{{isEmpty && !isLoading}}" class="empty-state">
            <text class="iconfont icon-picture"></text>
            <text class="empty-text">暂无照片</text>
            <view class="empty-button" bindtap="choosePhotos">选择照片</view>
          </view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 底部操作栏 -->
  <view wx:if="{{hasAlbumAuth}}" class="bottom-bar">
    <view class="bottom-bar-button" bindtap="choosePhotos">选择照片</view>
    <view class="bottom-bar-button primary" bindtap="completeMultiSelect">
      <text wx:if="{{selectedImages.length > 0}}">完成 ({{selectedImages.length}})</text>
      <text wx:else>完成</text>
    </view>
  </view>
</view>