/* 调试页面样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

/* 系统信息 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 按钮组 */
.button-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.test-btn, .clear-btn, .copy-btn {
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
}

.test-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.clear-btn {
  background: #ff6b6b;
  color: white;
}

.copy-btn {
  background: #4ecdc4;
  color: white;
}

/* 图片列表 */
.image-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.image-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
}

.preview-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
}

.image-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.image-size, .file-size {
  font-size: 24rpx;
  color: #666;
}

/* 测试结果 */
.results-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.result-item {
  padding: 24rpx;
  border-radius: 12rpx;
  border-left: 6rpx solid;
}

.result-item.success {
  background: #f0f9ff;
  border-left-color: #10b981;
}

.result-item.error {
  background: #fef2f2;
  border-left-color: #ef4444;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.result-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.result-status {
  font-size: 24rpx;
  font-weight: 500;
}

.result-item.success .result-status {
  color: #10b981;
}

.result-item.error .result-status {
  color: #ef4444;
}

.result-path, .result-error {
  margin-bottom: 16rpx;
}

.path-label, .error-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.path-value, .error-value {
  font-size: 24rpx;
  color: #333;
  word-break: break-all;
  font-family: monospace;
}

.result-details {
  border-top: 1rpx solid #e5e5e5;
  padding-top: 16rpx;
}

.detail-item {
  display: flex;
  margin-bottom: 8rpx;
}

.detail-label {
  font-size: 22rpx;
  color: #666;
  min-width: 160rpx;
}

.detail-value {
  font-size: 22rpx;
  color: #333;
  font-family: monospace;
  word-break: break-all;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  display: block;
}
