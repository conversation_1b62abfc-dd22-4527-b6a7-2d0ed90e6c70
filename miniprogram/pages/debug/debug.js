// 调试页面 - 用于诊断iOS真机环境问题
Page({
  data: {
    systemInfo: {},
    selectedImages: [],
    testResults: [],
    isLoading: false
  },

  onLoad: function() {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      systemInfo: systemInfo
    });
    
    console.log('调试页面加载，系统信息:', systemInfo);
  },

  // 选择图片进行测试
  selectImages: function() {
    this.setData({ isLoading: true });
    
    wx.chooseMedia({
      count: 3,
      mediaType: ['image'],
      sourceType: ['album'],
      success: (res) => {
        console.log('选择图片成功:', res);
        
        const images = res.tempFiles.map(file => ({
          path: file.tempFilePath,
          size: file.size,
          width: file.width,
          height: file.height
        }));
        
        this.setData({
          selectedImages: images,
          isLoading: false
        });
        
        // 开始测试
        this.runImageTests(images);
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        this.setData({ isLoading: false });
        
        wx.showModal({
          title: '选择图片失败',
          content: err.errMsg || '未知错误',
          showCancel: false
        });
      }
    });
  },

  // 运行图片测试
  runImageTests: function(images) {
    console.log('开始运行图片测试');
    
    const testResults = [];
    const testPromises = [];
    
    images.forEach((image, index) => {
      const testPromise = this.testSingleImage(image, index)
        .then(result => {
          testResults[index] = result;
          return result;
        })
        .catch(error => {
          const errorResult = {
            index: index,
            path: image.path,
            success: false,
            error: error.message || '测试失败',
            details: {}
          };
          testResults[index] = errorResult;
          return errorResult;
        });
      
      testPromises.push(testPromise);
    });
    
    Promise.all(testPromises)
      .then(results => {
        console.log('所有图片测试完成:', results);
        this.setData({
          testResults: testResults.filter(r => r)
        });
      })
      .catch(error => {
        console.error('图片测试过程出错:', error);
      });
  },

  // 测试单个图片
  testSingleImage: function(image, index) {
    return new Promise((resolve, reject) => {
      console.log(`测试图片 ${index}:`, image.path);
      
      const result = {
        index: index,
        path: image.path,
        success: false,
        error: '',
        details: {
          fileInfo: null,
          imageInfo: null,
          pathFixed: '',
          canLoad: false
        }
      };
      
      // 1. 测试文件信息
      wx.getFileInfo({
        filePath: image.path,
        success: (fileInfo) => {
          console.log(`图片 ${index} 文件信息:`, fileInfo);
          result.details.fileInfo = fileInfo;
          
          // 2. 测试图片信息
          wx.getImageInfo({
            src: image.path,
            success: (imageInfo) => {
              console.log(`图片 ${index} 图片信息:`, imageInfo);
              result.details.imageInfo = imageInfo;
              
              // 3. 测试路径修复
              const fixedPath = this.fixImagePath(image.path);
              result.details.pathFixed = fixedPath;
              
              // 4. 测试图片加载
              this.testImageLoad(fixedPath)
                .then(() => {
                  result.details.canLoad = true;
                  result.success = true;
                  resolve(result);
                })
                .catch(loadError => {
                  result.details.canLoad = false;
                  result.error = '图片加载失败: ' + loadError.message;
                  resolve(result);
                });
            },
            fail: (imageError) => {
              console.error(`图片 ${index} 图片信息获取失败:`, imageError);
              result.error = '获取图片信息失败: ' + imageError.errMsg;
              resolve(result);
            }
          });
        },
        fail: (fileError) => {
          console.error(`图片 ${index} 文件信息获取失败:`, fileError);
          result.error = '获取文件信息失败: ' + fileError.errMsg;
          resolve(result);
        }
      });
    });
  },

  // 修复图片路径（简化版）
  fixImagePath: function(imagePath) {
    if (!imagePath || typeof imagePath !== 'string') {
      return imagePath;
    }

    let fixedPath = imagePath;
    const systemInfo = wx.getSystemInfoSync();
    const isDevTool = systemInfo.platform === 'devtools';
    const isIOS = systemInfo.platform === 'ios';

    if (isIOS && !isDevTool) {
      if (fixedPath.startsWith('wxfile://')) {
        fixedPath = fixedPath.replace('wxfile://', '');
      }
      
      if (fixedPath.startsWith('http://tmp/')) {
        fixedPath = fixedPath.replace('http://tmp/', '/tmp/');
      }
      
      if (!fixedPath.startsWith('/') && !fixedPath.startsWith('http')) {
        fixedPath = '/' + fixedPath;
      }
    }

    return fixedPath;
  },

  // 测试图片加载
  testImageLoad: function(imagePath) {
    return new Promise((resolve, reject) => {
      const image = wx.createOffscreenCanvas().createImage();
      
      image.onload = () => {
        resolve(true);
      };
      
      image.onerror = (error) => {
        reject(new Error('图片加载失败'));
      };
      
      image.src = imagePath;
      
      // 设置超时
      setTimeout(() => {
        reject(new Error('图片加载超时'));
      }, 5000);
    });
  },

  // 清除测试结果
  clearResults: function() {
    this.setData({
      selectedImages: [],
      testResults: []
    });
  },

  // 复制测试结果
  copyResults: function() {
    const results = {
      systemInfo: this.data.systemInfo,
      testResults: this.data.testResults
    };
    
    wx.setClipboardData({
      data: JSON.stringify(results, null, 2),
      success: () => {
        wx.showToast({
          title: '测试结果已复制',
          icon: 'success'
        });
      }
    });
  }
});
