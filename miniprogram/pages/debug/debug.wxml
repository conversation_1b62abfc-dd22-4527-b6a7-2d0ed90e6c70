<!-- 调试页面 -->
<view class="container">
  <view class="header">
    <text class="title">图片导入调试工具</text>
    <text class="subtitle">用于诊断iOS真机环境问题</text>
  </view>

  <!-- 系统信息 -->
  <view class="section">
    <view class="section-title">系统信息</view>
    <view class="info-grid">
      <view class="info-item">
        <text class="info-label">平台:</text>
        <text class="info-value">{{systemInfo.platform}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">系统:</text>
        <text class="info-value">{{systemInfo.system}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">版本:</text>
        <text class="info-value">{{systemInfo.version}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">像素比:</text>
        <text class="info-value">{{systemInfo.pixelRatio}}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="section">
    <view class="button-group">
      <button class="test-btn" bindtap="selectImages" disabled="{{isLoading}}">
        {{isLoading ? '测试中...' : '选择图片测试'}}
      </button>
      <button class="clear-btn" bindtap="clearResults" wx:if="{{testResults.length > 0}}">
        清除结果
      </button>
      <button class="copy-btn" bindtap="copyResults" wx:if="{{testResults.length > 0}}">
        复制结果
      </button>
    </view>
  </view>

  <!-- 选中的图片 -->
  <view class="section" wx:if="{{selectedImages.length > 0}}">
    <view class="section-title">选中的图片 ({{selectedImages.length}}张)</view>
    <view class="image-list">
      <view class="image-item" wx:for="{{selectedImages}}" wx:key="index">
        <image src="{{item.path}}" mode="aspectFill" class="preview-image"></image>
        <view class="image-info">
          <text class="image-size">{{item.width}} × {{item.height}}</text>
          <text class="file-size">{{item.size}} bytes</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 测试结果 -->
  <view class="section" wx:if="{{testResults.length > 0}}">
    <view class="section-title">测试结果</view>
    <view class="results-list">
      <view class="result-item {{item.success ? 'success' : 'error'}}" wx:for="{{testResults}}" wx:key="index">
        <view class="result-header">
          <text class="result-title">图片 {{item.index + 1}}</text>
          <text class="result-status">{{item.success ? '✓ 成功' : '✗ 失败'}}</text>
        </view>
        
        <view class="result-path">
          <text class="path-label">路径:</text>
          <text class="path-value">{{item.path}}</text>
        </view>
        
        <view class="result-error" wx:if="{{!item.success}}">
          <text class="error-label">错误:</text>
          <text class="error-value">{{item.error}}</text>
        </view>
        
        <view class="result-details" wx:if="{{item.details}}">
          <view class="detail-item" wx:if="{{item.details.pathFixed}}">
            <text class="detail-label">修复后路径:</text>
            <text class="detail-value">{{item.details.pathFixed}}</text>
          </view>
          
          <view class="detail-item" wx:if="{{item.details.fileInfo}}">
            <text class="detail-label">文件大小:</text>
            <text class="detail-value">{{item.details.fileInfo.size}} bytes</text>
          </view>
          
          <view class="detail-item" wx:if="{{item.details.imageInfo}}">
            <text class="detail-label">图片尺寸:</text>
            <text class="detail-value">{{item.details.imageInfo.width}} × {{item.details.imageInfo.height}}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">可加载:</text>
            <text class="detail-value">{{item.details.canLoad ? '是' : '否'}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{selectedImages.length === 0 && testResults.length === 0}}">
    <text class="empty-text">点击"选择图片测试"开始诊断</text>
    <text class="empty-desc">此工具将帮助诊断iOS真机环境下的图片导入问题</text>
  </view>
</view>
