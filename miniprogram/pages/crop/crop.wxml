<!-- crop.wxml -->
<view class="container">
  
  <!-- 图片裁剪区域 -->
  <view class="crop-container">
    <view class="image-container" 
          style="width: {{displayWidth}}px; height: {{displayHeight}}px; position: relative; margin: 0 auto;">
      <image class="original-image" 
             src="{{imagePath}}" 
             mode="aspectFit" 
             style="width: 100%; height: 100%;"
             bindload="onImageLoad">
      </image>
      
      <!-- 九宫格裁剪框 -->
      <view class="crop-frame" 
            style="left: {{cropFrame.left}}px; top: {{cropFrame.top}}px; width: {{cropFrame.width}}px; height: {{cropFrame.height}}px;">
        <!-- 九宫格线 -->
        <view class="grid-lines horizontal" style="top: 33.3%;"></view>
        <view class="grid-lines horizontal" style="top: 66.6%;"></view>
        <view class="grid-lines vertical" style="left: 33.3%;"></view>
        <view class="grid-lines vertical" style="left: 66.6%;"></view>
        
        <!-- 边角控制点 -->
        <view class="corner-control top-left"
              bindtouchstart="onCornerTouchStart" 
              bindtouchmove="onCornerTopLeftMove"
              bindtouchend="onCornerTouchEnd"
              bindtouchcancel="onCornerTouchEnd"
              data-corner="topLeft">
        </view>
        <view class="corner-control top-right"
              bindtouchstart="onCornerTouchStart" 
              bindtouchmove="onCornerTopRightMove"
              bindtouchend="onCornerTouchEnd"
              bindtouchcancel="onCornerTouchEnd"
              data-corner="topRight">
        </view>
        <view class="corner-control bottom-left"
              bindtouchstart="onCornerTouchStart" 
              bindtouchmove="onCornerBottomLeftMove"
              bindtouchend="onCornerTouchEnd"
              bindtouchcancel="onCornerTouchEnd"
              data-corner="bottomLeft">
        </view>
        <view class="corner-control bottom-right"
              bindtouchstart="onCornerTouchStart" 
              bindtouchmove="onCornerBottomRightMove"
              bindtouchend="onCornerTouchEnd"
              bindtouchcancel="onCornerTouchEnd"
              data-corner="bottomRight">
        </view>
        
        <!-- 边框拖动控制点 -->
        <view class="edge-control top"
              bindtouchstart="onCornerTouchStart" 
              bindtouchmove="onEdgeTopMove"
              bindtouchend="onCornerTouchEnd"
              bindtouchcancel="onCornerTouchEnd"
              data-edge="top">
        </view>
        <view class="edge-control right"
              bindtouchstart="onCornerTouchStart" 
              bindtouchmove="onEdgeRightMove"
              bindtouchend="onCornerTouchEnd"
              bindtouchcancel="onCornerTouchEnd"
              data-edge="right">
        </view>
        <view class="edge-control bottom"
              bindtouchstart="onCornerTouchStart" 
              bindtouchmove="onEdgeBottomMove"
              bindtouchend="onCornerTouchEnd"
              bindtouchcancel="onCornerTouchEnd"
              data-edge="bottom">
        </view>
        <view class="edge-control left"
              bindtouchstart="onCornerTouchStart" 
              bindtouchmove="onEdgeLeftMove"
              bindtouchend="onCornerTouchEnd"
              bindtouchcancel="onCornerTouchEnd"
              data-edge="left">
        </view>
      </view>
    </view>
  </view>
  
  <!-- 裁剪尺寸显示 -->
  <view class="crop-size-info {{touchActive ? 'active' : ''}}">
    <view class="size-icon">⊞</view>
    <text class="size-text">{{cropSize.width || 0}} × {{cropSize.height || 0}} 裁剪区像素</text>
  </view>
  
  <!-- 操作栏 -->
  <view class="action-bar">
    <button class="cancel-btn" bindtap="cancelCrop">返回</button>
    <button class="confirm-btn" bindtap="confirmCrop">确定</button>
    <button class="reset-btn" bindtap="resetCrop">重置</button>
  </view>
  
  <!-- 底部裁剪比例选择 -->
  <view class="ratio-options">
    <view class="ratio-option {{currentRatio === 'free' ? 'active' : ''}}" bindtap="setRatio" data-ratio="free">
      <text>自由</text>
    </view>
    <view class="ratio-option {{currentRatio === '1:1' ? 'active' : ''}}" bindtap="setRatio" data-ratio="1:1">
      <text>1:1</text>
    </view>
    <view class="ratio-option {{currentRatio === '4:3' ? 'active' : ''}}" bindtap="setRatio" data-ratio="4:3">
      <text>4:3</text>
    </view>
    <view class="ratio-option {{currentRatio === '3:4' ? 'active' : ''}}" bindtap="setRatio" data-ratio="3:4">
      <text>3:4</text>
    </view>
    <view class="ratio-option {{currentRatio === '16:9' ? 'active' : ''}}" bindtap="setRatio" data-ratio="16:9">
      <text>16:9</text>
    </view>
    <view class="ratio-option {{currentRatio === '9:16' ? 'active' : ''}}" bindtap="setRatio" data-ratio="9:16">
      <text>9:16</text>
    </view>
  </view>
  
  <!-- 隐藏的canvas用于裁剪操作 -->
  <canvas type="2d" id="cropCanvas" style="position:fixed; left:-9999px; top:0; visibility:hidden;"></canvas>
</view>