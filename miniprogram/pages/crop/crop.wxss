/* crop.wxss */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #000000;
  padding-top: 10px; /* 为系统导航栏留出空间 */
}

/* 图片裁剪区域 */
.crop-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  padding: 15px;
}

.image-container {
  position: relative;
  border: 1px solid #333;
  background-color: #111;
  border-radius: 4px;
  overflow: visible;
  /* 不铺满全屏，设置最大尺寸 */
  max-width: 90%;
  max-height: 75%;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.original-image {
  display: block;
  object-fit: contain;
}

/* 裁剪框 */
.crop-frame {
  position: absolute;
  border: 2px solid #ffffff;
  box-sizing: border-box;
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.7);
  /* 增加外发光效果提高可见性 */
  filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.8));
  z-index: 5; /* 确保裁剪框在最上层 */
}

/* 九宫格线 */
.grid-lines {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.8);
}

.horizontal {
  width: 100%;
  height: 1px;
}

.vertical {
  height: 100%;
  width: 1px;
}

/* 边角控制点 - 优化拖动体验 */
.corner-control {
  position: absolute;
  width: 28px;
  height: 28px;
  background-color: rgba(255, 125, 44, 0.5);
  border: 2px solid #ffffff;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.7);
  transition: background-color 0.2s;
}

.corner-control:active {
  background-color: rgba(255, 125, 44, 0.8);
  transform: translate(-50%, -50%) scale(1.1);
}

.top-left {
  left: 0;
  top: 0;
  cursor: nwse-resize;
}

.top-right {
  right: 0;
  top: 0;
  transform: translate(50%, -50%);
  cursor: nesw-resize;
}

.top-right:active {
  transform: translate(50%, -50%) scale(1.1);
}

.bottom-left {
  left: 0;
  bottom: 0;
  transform: translate(-50%, 50%);
  cursor: nesw-resize;
}

.bottom-left:active {
  transform: translate(-50%, 50%) scale(1.1);
}

.bottom-right {
  right: 0;
  bottom: 0;
  transform: translate(50%, 50%);
  cursor: nwse-resize;
}

.bottom-right:active {
  transform: translate(50%, 50%) scale(1.1);
}

/* 边框拖动控制点 */
.edge-control {
  position: absolute;
  background-color: transparent;
}

.top {
  top: 0;
  left: 24px;
  right: 24px;
  height: 24px;
  transform: translateY(-50%);
  cursor: ns-resize;
}

.right {
  top: 24px;
  right: 0;
  bottom: 24px;
  width: 24px;
  transform: translateX(50%);
  cursor: ew-resize;
}

.bottom {
  bottom: 0;
  left: 24px;
  right: 24px;
  height: 24px;
  transform: translateY(50%);
  cursor: ns-resize;
}

.left {
  top: 24px;
  left: 0;
  bottom: 24px;
  width: 24px;
  transform: translateX(-50%);
  cursor: ew-resize;
}

/* 裁剪尺寸显示 */
.crop-size-info {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.8);
  border: 1px solid #FF7D2C;
  border-radius: 20px;
  margin: 10px 50px;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(255, 125, 44, 0.4);
  position: relative;
  animation: fadeInOut 2s infinite alternate;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.crop-size-info.active {
  animation: none;
  background-color: rgba(255, 125, 44, 0.7);
  border: 1px solid #FFFFFF;
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.6);
  transform: scale(1.05);
}

.size-icon {
  margin-right: 8px;
  color: #FFFFFF;
  font-size: 18px;
  font-weight: bold;
}

.size-text {
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

@keyframes fadeInOut {
  0% {
    opacity: 0.8;
    transform: scale(0.98);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 125, 44, 0.6);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(255, 125, 44, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 125, 44, 0);
  }
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: space-around;
  padding: 15px 20px;
  background-color: #000000;
}

.cancel-btn, .confirm-btn, .reset-btn {
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 15px;
  border: none;
  color: white;
}

.cancel-btn {
  background-color: #555555;
}

.confirm-btn {
  background: linear-gradient(to right, #FF7D2C, #FF9E2C);
  box-shadow: 0 2px 5px rgba(255, 125, 44, 0.3);
}

.reset-btn {
  background-color: #3F51B5;
}

/* 底部裁剪比例选择 */
.ratio-options {
  display: flex;
  justify-content: space-around;
  padding: 15px 10px;
  background-color: #000000;
  border-top: 1px solid #333333;
}

.ratio-option {
  padding: 8px 12px;
  border-radius: 15px;
  font-size: 14px;
  color: white;
  background-color: #333333;
  text-align: center;
  min-width: 40px;
}

.ratio-option.active {
  background: linear-gradient(to right, #FF7D2C, #FF9E2C);
  font-weight: bold;
}