const app = getApp();

Page({
  data: {
    imagePath: '',         // 图片路径
    imageWidth: 0,         // 图片实际宽度
    imageHeight: 0,        // 图片实际高度
    displayWidth: 0,       // 屏幕上显示的宽度
    displayHeight: 0,      // 屏幕上显示的高度
    cropFrame: {           // 裁剪框位置和大小
      left: 0,
      top: 0,
      width: 0,
      height: 0
    },
    originalCropFrame: {   // 原始裁剪框位置和大小(用于重置)
      left: 0,
      top: 0,
      width: 0,
      height: 0
    },
    lastTouchX: 0,         // 上次触摸位置X
    lastTouchY: 0,         // 上次触摸位置Y
    currentRatio: 'free',  // 当前选中的裁剪比例
    ratios: {              // 可选的裁剪比例
      'free': 0,
      '1:1': 1,
      '4:3': 4/3,
      '3:4': 3/4,
      '16:9': 16/9,
      '9:16': 9/16
    },
    touchActive: false,    // 是否处于触摸状态
    cropSize: {            // 裁剪尺寸显示
      width: 0,
      height: 0
    }
  },

  onLoad: function (options) {
    // 1. 首先尝试从options参数获取图片路径
    if (options && options.imagePath) {
      this.setData({
        imagePath: options.imagePath
      });
      console.log('从options获取图片路径:', options.imagePath);
      return;
    }
    
    // 2. 尝试从eventChannel获取图片路径
    try {
      const eventChannel = this.getOpenerEventChannel();
      if (eventChannel && typeof eventChannel.on === 'function') {
        eventChannel.on('acceptDataFromOpenerPage', (data) => {
          if (data && data.imagePath) {
            this.setData({
              imagePath: data.imagePath
            });
            console.log('从eventChannel获取图片路径:', data.imagePath);
          } else {
            console.error('eventChannel数据无效:', data);
            wx.showToast({
              title: '图片数据无效',
              icon: 'none'
            });
          }
        });
      } else {
        console.warn('eventChannel不可用或on方法不存在');
        this.showImagePathError();
      }
    } catch (error) {
      console.error('获取eventChannel失败:', error);
      this.showImagePathError();
    }

    // 获取屏幕尺寸
    const systemInfo = wx.getSystemInfoSync();
    this.windowWidth = systemInfo.windowWidth;
    this.windowHeight = systemInfo.windowHeight;
    this.pixelRatio = systemInfo.pixelRatio || 2;
    console.log('屏幕信息:', systemInfo);
  },

  // 显示图片路径错误提示
  showImagePathError: function() {
    wx.showToast({
      title: '无法获取图片路径',
      icon: 'none',
      duration: 2000
    });
    
    // 延迟返回上一页
    setTimeout(() => {
      wx.navigateBack({
        fail: function() {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }
      });
    }, 2000);
  },
  
  // 图片加载完成后设置初始裁剪框
  onImageLoad(e) {
    const { width: originalWidth, height: originalHeight } = e.detail;
    console.log('原始图片尺寸:', originalWidth, originalHeight);
    
    // 计算图片在屏幕上的显示尺寸
    let displayWidth, displayHeight;
    
    // 根据图片比例确定在屏幕上的显示尺寸
    const containerHeight = this.windowHeight - 200; // 减去顶部导航栏和底部按钮的高度
    const containerWidth = this.windowWidth * 0.9; // 限制为屏幕宽度的90%
    
    const imageRatio = originalWidth / originalHeight;
    const containerRatio = containerWidth / containerHeight;
    
    if (imageRatio > containerRatio) {
      // 图片比容器更宽，以宽度为基准
      displayWidth = containerWidth;
      displayHeight = containerWidth / imageRatio;
    } else {
      // 图片比容器更高，以高度为基准
      displayHeight = Math.min(containerHeight, this.windowHeight * 0.75); // 限制最大高度为屏幕高度的75%
      displayWidth = displayHeight * imageRatio;
    }
    
    // 控制点的大小，用于计算安全边距
    const controlPointSize = 28; // 控制点的尺寸为28px
    
    // 设置初始裁剪框大小，考虑安全边距
    // 对于小图片，使用图片尺寸的80%作为裁剪框大小
    const cropSizePercent = Math.min(displayWidth, displayHeight) * 0.8;
    
    // 确保裁剪框不超出图片边界
    const finalCropSize = Math.min(cropSizePercent, displayWidth, displayHeight);
    
    // 计算裁剪框的位置，确保完全居中
    // 修正：使用准确的居中计算方法
    const cropLeft = (displayWidth - finalCropSize) / 2-12;
    const cropTop = (displayHeight - finalCropSize) / 2;
    
    const cropFrame = {
      left: cropLeft,
      top: cropTop,
      width: finalCropSize,
      height: finalCropSize
    };
    
    console.log('裁剪框位置:', cropLeft, cropTop, '裁剪框大小:', finalCropSize);
    
    // 计算实际裁剪尺寸
    const scaleX = originalWidth / displayWidth;
    const scaleY = originalHeight / displayHeight;
    const realWidth = Math.round(cropFrame.width * scaleX);
    const realHeight = Math.round(cropFrame.height * scaleY);
    
    this.setData({
      imageWidth: originalWidth,
      imageHeight: originalHeight,
      displayWidth,
      displayHeight,
      cropFrame,
      originalCropFrame: { ...cropFrame },
      cropSize: {
        width: realWidth,
        height: realHeight
      }
    });
    
    // 保存缩放比例供后续使用
    this.scaleX = scaleX;
    this.scaleY = scaleY;
    
    console.log('设置初始裁剪框:', cropFrame);
    console.log('缩放比例:', scaleX, scaleY);
  },

  // 更新裁剪尺寸显示
  updateCropSize() {
    const { cropFrame } = this.data;
    
    // 计算实际裁剪尺寸
    const realWidth = Math.round(cropFrame.width * this.scaleX);
    const realHeight = Math.round(cropFrame.height * this.scaleY);
    
    this.setData({
      cropSize: {
        width: realWidth,
        height: realHeight
      }
    });
    
    console.log('更新裁剪尺寸:', realWidth, realHeight);
  },

  // 触摸开始时记录位置
  onCornerTouchStart(e) {
    const touch = e.touches[0];
    this.setData({
      lastTouchX: touch.clientX,
      lastTouchY: touch.clientY,
      touchActive: true
    });
    
    // 使用双指缓存提高性能
    this.lastTouchX = touch.clientX;
    this.lastTouchY = touch.clientY;
    
    // 缓存当前裁剪框状态
    this.currentCropFrame = { ...this.data.cropFrame };
  },
  
  // 触摸结束时更新状态
  onCornerTouchEnd() {
    // 更新最终裁剪尺寸
    this.updateCropSize();
    
    // 延迟关闭活动状态，使动画更流畅
    setTimeout(() => {
      this.setData({
        touchActive: false
      });
    }, 300);
  },

  // 左上角拖动 - 优化性能
  onCornerTopLeftMove(e) {
    if (!this.data.touchActive) return;
    
    const touch = e.touches[0];
    const { cropFrame } = this.data;
    
    // 计算移动距离 - 使用缓存值提高性能
    const deltaX = touch.clientX - this.lastTouchX;
    const deltaY = touch.clientY - this.lastTouchY;
    
    let newLeft = this.currentCropFrame.left + deltaX;
    let newTop = this.currentCropFrame.top + deltaY;
    let newWidth = this.currentCropFrame.width - deltaX;
    let newHeight = this.currentCropFrame.height - deltaY;
    
    // 确保不超出边界
    newLeft = Math.max(0, Math.min(newLeft, cropFrame.left + cropFrame.width - 50));
    newTop = Math.max(0, Math.min(newTop, cropFrame.top + cropFrame.height - 50));
    
    newWidth = cropFrame.left + cropFrame.width - newLeft;
    newHeight = cropFrame.top + cropFrame.height - newTop;
    
    // 处理裁剪比例
    if (this.data.currentRatio !== 'free' && this.data.ratios[this.data.currentRatio] > 0) {
      const ratio = this.data.ratios[this.data.currentRatio];
      if (newWidth / newHeight > ratio) {
        newWidth = newHeight * ratio;
      } else {
        newHeight = newWidth / ratio;
      }
      newLeft = cropFrame.left + cropFrame.width - newWidth;
      newTop = cropFrame.top + cropFrame.height - newHeight;
    }
    
    // 计算实际裁剪尺寸
    const realWidth = Math.round(newWidth * this.scaleX);
    const realHeight = Math.round(newHeight * this.scaleY);
    
    // 使用setData更新UI - 合并更新以提高性能
    this.setData({
      cropFrame: {
        left: newLeft,
        top: newTop,
        width: newWidth,
        height: newHeight
      },
      cropSize: {
        width: realWidth,
        height: realHeight
      }
    });
    
    // 更新缓存值
    this.lastTouchX = touch.clientX;
    this.lastTouchY = touch.clientY;
    this.currentCropFrame = {
      left: newLeft,
      top: newTop,
      width: newWidth,
      height: newHeight
    };
  },

  // 右上角拖动 - 优化性能
  onCornerTopRightMove(e) {
    if (!this.data.touchActive) return;
    
    const touch = e.touches[0];
    const { cropFrame } = this.data;
    
    // 计算移动距离 - 使用缓存值提高性能
    const deltaX = touch.clientX - this.lastTouchX;
    const deltaY = touch.clientY - this.lastTouchY;
    
    let newWidth = this.currentCropFrame.width + deltaX;
    let newTop = this.currentCropFrame.top + deltaY;
    let newHeight = this.currentCropFrame.height - deltaY;
    
    // 确保不超出边界
    newWidth = Math.max(50, Math.min(newWidth, this.data.displayWidth - cropFrame.left));
    newTop = Math.max(0, Math.min(newTop, cropFrame.top + cropFrame.height - 50));
    newHeight = cropFrame.top + cropFrame.height - newTop;
    
    // 处理裁剪比例
    if (this.data.currentRatio !== 'free' && this.data.ratios[this.data.currentRatio] > 0) {
      const ratio = this.data.ratios[this.data.currentRatio];
      if (newWidth / newHeight > ratio) {
        newWidth = newHeight * ratio;
      } else {
        newHeight = newWidth / ratio;
      }
      newTop = cropFrame.top + cropFrame.height - newHeight;
    }
    
    // 计算实际裁剪尺寸
    const { imageWidth, imageHeight, displayWidth, displayHeight } = this.data;
    const scaleX = imageWidth / displayWidth;
    const scaleY = imageHeight / displayHeight;
    const realWidth = Math.round(newWidth * scaleX);
    const realHeight = Math.round(newHeight * scaleY);
    
    // 更新裁剪框和触摸位置
    this.setData({
      cropFrame: {
        ...cropFrame,
        top: newTop,
        width: newWidth,
        height: newHeight
      },
      cropSize: {
        width: realWidth,
        height: realHeight
      }
    });
    
    // 更新缓存值
    this.lastTouchX = touch.clientX;
    this.lastTouchY = touch.clientY;
    this.currentCropFrame = {
      ...this.currentCropFrame,
      top: newTop,
      width: newWidth,
      height: newHeight
    };
  },

  // 左下角拖动 - 优化性能
  onCornerBottomLeftMove(e) {
    if (!this.data.touchActive) return;
    
    const touch = e.touches[0];
    const { cropFrame } = this.data;
    
    // 计算移动距离 - 使用缓存值提高性能
    const deltaX = touch.clientX - this.lastTouchX;
    const deltaY = touch.clientY - this.lastTouchY;
    
    let newLeft = this.currentCropFrame.left + deltaX;
    let newWidth = this.currentCropFrame.width - deltaX;
    let newHeight = this.currentCropFrame.height + deltaY;
    
    // 确保不超出边界
    newLeft = Math.max(0, Math.min(newLeft, cropFrame.left + cropFrame.width - 50));
    newWidth = cropFrame.left + cropFrame.width - newLeft;
    newHeight = Math.max(50, Math.min(newHeight, this.data.displayHeight - cropFrame.top));
    
    // 处理裁剪比例
    if (this.data.currentRatio !== 'free' && this.data.ratios[this.data.currentRatio] > 0) {
      const ratio = this.data.ratios[this.data.currentRatio];
      if (newWidth / newHeight > ratio) {
        newWidth = newHeight * ratio;
      } else {
        newHeight = newWidth / ratio;
      }
      newLeft = cropFrame.left + cropFrame.width - newWidth;
    }
    
    // 计算实际裁剪尺寸
    const { imageWidth, imageHeight, displayWidth, displayHeight } = this.data;
    const scaleX = imageWidth / displayWidth;
    const scaleY = imageHeight / displayHeight;
    const realWidth = Math.round(newWidth * scaleX);
    const realHeight = Math.round(newHeight * scaleY);
    
    // 更新裁剪框和触摸位置
    this.setData({
      cropFrame: {
        ...cropFrame,
        left: newLeft,
        width: newWidth,
        height: newHeight
      },
      cropSize: {
        width: realWidth,
        height: realHeight
      }
    });
    
    // 更新缓存值
    this.lastTouchX = touch.clientX;
    this.lastTouchY = touch.clientY;
    this.currentCropFrame = {
      ...this.currentCropFrame,
      left: newLeft,
      width: newWidth,
      height: newHeight
    };
  },

  // 右下角拖动 - 优化性能
  onCornerBottomRightMove(e) {
    if (!this.data.touchActive) return;
    
    const touch = e.touches[0];
    const { cropFrame, displayWidth, displayHeight } = this.data;
    
    // 计算移动距离 - 使用缓存值提高性能
    const deltaX = touch.clientX - this.lastTouchX;
    const deltaY = touch.clientY - this.lastTouchY;
    
    let newWidth = this.currentCropFrame.width + deltaX;
    let newHeight = this.currentCropFrame.height + deltaY;
    
    // 控制点大小
    const controlPointSize = 28; // 与CSS中保持一致
    const safeMargin = controlPointSize / 2;
    
    // 确保不超出边界，并且保留足够空间给控制点
    const maxWidth = displayWidth - cropFrame.left - safeMargin;
    const maxHeight = displayHeight - cropFrame.top - safeMargin;
    
    newWidth = Math.max(50, Math.min(newWidth, maxWidth));
    newHeight = Math.max(50, Math.min(newHeight, maxHeight));
    
    // 处理裁剪比例
    if (this.data.currentRatio !== 'free' && this.data.ratios[this.data.currentRatio] > 0) {
      const ratio = this.data.ratios[this.data.currentRatio];
      if (newWidth / newHeight > ratio) {
        newWidth = newHeight * ratio;
      } else {
        newHeight = newWidth / ratio;
      }
      
      // 再次确保不超出边界
      newWidth = Math.min(newWidth, maxWidth);
      newHeight = Math.min(newHeight, maxHeight);
    }
    
    // 计算实际裁剪尺寸
    const { imageWidth, imageHeight } = this.data;
    const scaleX = imageWidth / displayWidth;
    const scaleY = imageHeight / displayHeight;
    const realWidth = Math.round(newWidth * scaleX);
    const realHeight = Math.round(newHeight * scaleY);
    
    // 更新裁剪框和触摸位置
    this.setData({
      cropFrame: {
        ...cropFrame,
        width: newWidth,
        height: newHeight
      },
      cropSize: {
        width: realWidth,
        height: realHeight
      }
    });
    
    // 更新缓存值
    this.lastTouchX = touch.clientX;
    this.lastTouchY = touch.clientY;
    this.currentCropFrame = {
      ...this.currentCropFrame,
      width: newWidth,
      height: newHeight
    };
  },
  
  // 上边拖动 - 优化性能
  onEdgeTopMove(e) {
    if (!this.data.touchActive) return;
    
    const touch = e.touches[0];
    const { cropFrame } = this.data;
    
    // 计算移动距离 - 使用缓存值提高性能
    const deltaY = touch.clientY - this.lastTouchY;
    
    let newTop = this.currentCropFrame.top + deltaY;
    let newHeight = this.currentCropFrame.height - deltaY;
    
    // 确保不超出边界
    newTop = Math.max(0, Math.min(newTop, cropFrame.top + cropFrame.height - 50));
    newHeight = cropFrame.top + cropFrame.height - newTop;
    
    // 处理裁剪比例
    if (this.data.currentRatio !== 'free' && this.data.ratios[this.data.currentRatio] > 0) {
      const ratio = this.data.ratios[this.data.currentRatio];
      let newWidth = newHeight * ratio;
      
      // 确保宽度不超出边界
      if (newWidth > this.data.displayWidth || cropFrame.left + newWidth > this.data.displayWidth) {
        newWidth = this.data.displayWidth - cropFrame.left;
        newHeight = newWidth / ratio;
        newTop = cropFrame.top + cropFrame.height - newHeight;
      }
      
      // 计算实际裁剪尺寸
      const { imageWidth, imageHeight, displayWidth, displayHeight } = this.data;
      const scaleX = imageWidth / displayWidth;
      const scaleY = imageHeight / displayHeight;
      const realWidth = Math.round(newWidth * scaleX);
      const realHeight = Math.round(newHeight * scaleY);
      
      this.setData({
        cropFrame: {
          ...cropFrame,
          top: newTop,
          height: newHeight,
          width: newWidth
        },
        cropSize: {
          width: realWidth,
          height: realHeight
        }
      });
    } else {
      // 计算实际裁剪尺寸
      const { imageWidth, imageHeight, displayWidth, displayHeight } = this.data;
      const scaleX = imageWidth / displayWidth;
      const scaleY = imageHeight / displayHeight;
      const realWidth = Math.round(cropFrame.width * scaleX);
      const realHeight = Math.round(newHeight * scaleY);
      
      this.setData({
        cropFrame: {
          ...cropFrame,
          top: newTop,
          height: newHeight
        },
        cropSize: {
          width: realWidth,
          height: realHeight
        }
      });
    }
    
    // 更新缓存值
    this.lastTouchX = touch.clientX;
    this.lastTouchY = touch.clientY;
    this.currentCropFrame = {
      ...this.currentCropFrame,
      top: newTop,
      height: newHeight
    };
  },
  
  // 右边拖动 - 优化性能
  onEdgeRightMove(e) {
    if (!this.data.touchActive) return;
    
    const touch = e.touches[0];
    const { cropFrame } = this.data;
    
    // 计算移动距离 - 使用缓存值提高性能
    const deltaX = touch.clientX - this.lastTouchX;
    
    let newWidth = this.currentCropFrame.width + deltaX;
    
    // 确保不超出边界
    newWidth = Math.max(50, Math.min(newWidth, this.data.displayWidth - cropFrame.left));
    
    // 处理裁剪比例
    if (this.data.currentRatio !== 'free' && this.data.ratios[this.data.currentRatio] > 0) {
      const ratio = this.data.ratios[this.data.currentRatio];
      let newHeight = newWidth / ratio;
      
      // 确保高度不超出边界
      if (newHeight > this.data.displayHeight || cropFrame.top + newHeight > this.data.displayHeight) {
        newHeight = this.data.displayHeight - cropFrame.top;
        newWidth = newHeight * ratio;
      }
      
      // 计算实际裁剪尺寸
      const { imageWidth, imageHeight, displayWidth, displayHeight } = this.data;
      const scaleX = imageWidth / displayWidth;
      const scaleY = imageHeight / displayHeight;
      const realWidth = Math.round(newWidth * scaleX);
      const realHeight = Math.round(newHeight * scaleY);
      
      this.setData({
        cropFrame: {
          ...cropFrame,
          width: newWidth,
          height: newHeight
        },
        cropSize: {
          width: realWidth,
          height: realHeight
        }
      });
    } else {
      // 计算实际裁剪尺寸
      const { imageWidth, imageHeight, displayWidth, displayHeight } = this.data;
      const scaleX = imageWidth / displayWidth;
      const scaleY = imageHeight / displayHeight;
      const realWidth = Math.round(newWidth * scaleX);
      const realHeight = Math.round(cropFrame.height * scaleY);
      
      this.setData({
        cropFrame: {
          ...cropFrame,
          width: newWidth
        },
        cropSize: {
          width: realWidth,
          height: realHeight
        }
      });
    }
    
    // 更新缓存值
    this.lastTouchX = touch.clientX;
    this.lastTouchY = touch.clientY;
    this.currentCropFrame = {
      ...this.currentCropFrame,
      width: newWidth
    };
  },
  
  // 底边拖动 - 优化性能
  onEdgeBottomMove(e) {
    if (!this.data.touchActive) return;
    
    const touch = e.touches[0];
    const { cropFrame } = this.data;
    
    // 计算移动距离 - 使用缓存值提高性能
    const deltaY = touch.clientY - this.lastTouchY;
    
    let newHeight = this.currentCropFrame.height + deltaY;
    
    // 确保不超出边界
    newHeight = Math.max(50, Math.min(newHeight, this.data.displayHeight - cropFrame.top));
    
    // 处理裁剪比例
    if (this.data.currentRatio !== 'free' && this.data.ratios[this.data.currentRatio] > 0) {
      const ratio = this.data.ratios[this.data.currentRatio];
      let newWidth = newHeight * ratio;
      
      // 确保宽度不超出边界
      if (newWidth > this.data.displayWidth || cropFrame.left + newWidth > this.data.displayWidth) {
        newWidth = this.data.displayWidth - cropFrame.left;
        newHeight = newWidth / ratio;
      }
      
      // 计算实际裁剪尺寸
      const { imageWidth, imageHeight, displayWidth, displayHeight } = this.data;
      const scaleX = imageWidth / displayWidth;
      const scaleY = imageHeight / displayHeight;
      const realWidth = Math.round(newWidth * scaleX);
      const realHeight = Math.round(newHeight * scaleY);
      
      this.setData({
        cropFrame: {
          ...cropFrame,
          height: newHeight,
          width: newWidth
        },
        cropSize: {
          width: realWidth,
          height: realHeight
        }
      });
    } else {
      // 计算实际裁剪尺寸
      const { imageWidth, imageHeight, displayWidth, displayHeight } = this.data;
      const scaleX = imageWidth / displayWidth;
      const scaleY = imageHeight / displayHeight;
      const realWidth = Math.round(cropFrame.width * scaleX);
      const realHeight = Math.round(newHeight * scaleY);
      
      this.setData({
        cropFrame: {
          ...cropFrame,
          height: newHeight
        },
        cropSize: {
          width: realWidth,
          height: realHeight
        }
      });
    }
    
    // 更新缓存值
    this.lastTouchX = touch.clientX;
    this.lastTouchY = touch.clientY;
    this.currentCropFrame = {
      ...this.currentCropFrame,
      height: newHeight
    };
  },
  
  // 左边拖动 - 优化性能
  onEdgeLeftMove(e) {
    if (!this.data.touchActive) return;
    
    const touch = e.touches[0];
    const { cropFrame } = this.data;
    
    // 计算移动距离 - 使用缓存值提高性能
    const deltaX = touch.clientX - this.lastTouchX;
    
    let newLeft = this.currentCropFrame.left + deltaX;
    let newWidth = this.currentCropFrame.width - deltaX;
    
    // 确保不超出边界
    newLeft = Math.max(0, Math.min(newLeft, cropFrame.left + cropFrame.width - 50));
    newWidth = cropFrame.left + cropFrame.width - newLeft;
    
    // 处理裁剪比例
    if (this.data.currentRatio !== 'free' && this.data.ratios[this.data.currentRatio] > 0) {
      const ratio = this.data.ratios[this.data.currentRatio];
      let newHeight = newWidth / ratio;
      
      // 确保高度不超出边界
      if (newHeight > this.data.displayHeight || cropFrame.top + newHeight > this.data.displayHeight) {
        newHeight = this.data.displayHeight - cropFrame.top;
        newWidth = newHeight * ratio;
        newLeft = cropFrame.left + cropFrame.width - newWidth;
      }
      
      // 计算实际裁剪尺寸
      const { imageWidth, imageHeight, displayWidth, displayHeight } = this.data;
      const scaleX = imageWidth / displayWidth;
      const scaleY = imageHeight / displayHeight;
      const realWidth = Math.round(newWidth * scaleX);
      const realHeight = Math.round(newHeight * scaleY);
      
      this.setData({
        cropFrame: {
          ...cropFrame,
          left: newLeft,
          width: newWidth,
          height: newHeight
        },
        cropSize: {
          width: realWidth,
          height: realHeight
        }
      });
    } else {
      // 计算实际裁剪尺寸
      const { imageWidth, imageHeight, displayWidth, displayHeight } = this.data;
      const scaleX = imageWidth / displayWidth;
      const scaleY = imageHeight / displayHeight;
      const realWidth = Math.round(newWidth * scaleX);
      const realHeight = Math.round(cropFrame.height * scaleY);
      
      this.setData({
        cropFrame: {
          ...cropFrame,
          left: newLeft,
          width: newWidth
        },
        cropSize: {
          width: realWidth,
          height: realHeight
        }
      });
    }
    
    // 更新缓存值
    this.lastTouchX = touch.clientX;
    this.lastTouchY = touch.clientY;
    this.currentCropFrame = {
      ...this.currentCropFrame,
      left: newLeft,
      width: newWidth
    };
  },

  // 底部比例选择
  setRatio(e) {
    const ratio = e.currentTarget.dataset.ratio;
    if (ratio === this.data.currentRatio) return;
    
    this.setData({
      currentRatio: ratio
    });
    
    // 自由比例不调整大小
    if (ratio === 'free') return;
    
    // 调整裁剪框大小以符合所选比例
    const { displayWidth, displayHeight } = this.data;
    const currentRatio = this.data.ratios[ratio];
    
    if (currentRatio > 0) {
      // 计算图片中心点
      const centerX = displayWidth / 2;
      const centerY = displayHeight / 2;
      
      // 计算在保持比例的情况下可能的最大尺寸
      let maxWidth, maxHeight;
      
      if (displayWidth / displayHeight > currentRatio) {
        // 如果显示区域比例大于目标比例，则以高度为基准
        maxHeight = displayHeight * 0.8; // 使用80%的显示高度
        maxWidth = maxHeight * currentRatio;
      } else {
        // 否则以宽度为基准
        maxWidth = displayWidth * 0.8; // 使用80%的显示宽度
        maxHeight = maxWidth / currentRatio;
      }
      
      // 确保不超出图片边界
      maxWidth = Math.min(maxWidth, displayWidth * 0.9);
      maxHeight = Math.min(maxHeight, displayHeight * 0.9);
      
      // 根据中心点计算新的左上角位置
      let newLeft = centerX - maxWidth / 2;
      let newTop = centerY - maxHeight / 2;
      
      // 确保不超出边界
      newLeft = Math.max(0, Math.min(newLeft, displayWidth - maxWidth));
      newTop = Math.max(0, Math.min(newTop, displayHeight - maxHeight));
      
      // 计算实际裁剪尺寸
      const realWidth = Math.round(maxWidth * this.scaleX);
      const realHeight = Math.round(maxHeight * this.scaleY);
      
      // 更新UI
      this.setData({
        cropFrame: {
          left: newLeft,
          top: newTop,
          width: maxWidth,
          height: maxHeight
        },
        cropSize: {
          width: realWidth,
          height: realHeight
        }
      });
      
      console.log('设置新比例:', ratio, '新裁剪框尺寸:', maxWidth, maxHeight);
    }
  },
  
  // 重置裁剪框
  resetCrop() {
    this.setData({
      cropFrame: { ...this.data.originalCropFrame },
      currentRatio: 'free'
    });
    
    // 更新裁剪尺寸显示
    this.updateCropSize();
  },
  
  // 取消裁剪
  cancelCrop() {
    wx.navigateBack();
  },
  
  // 确认裁剪
  confirmCrop() {
    const { imagePath, cropFrame, imageWidth, imageHeight, displayWidth, displayHeight } = this.data;
    
    // 计算实际图片上的裁剪区域
    const scaleX = imageWidth / displayWidth;
    const scaleY = imageHeight / displayHeight;
    
    const realCropFrame = {
      x: Math.floor(cropFrame.left * scaleX),
      y: Math.floor(cropFrame.top * scaleY),
      width: Math.floor(cropFrame.width * scaleX),
      height: Math.floor(cropFrame.height * scaleY)
    };
    
    // 确保裁剪区域有效
    if (realCropFrame.width <= 0 || realCropFrame.height <= 0) {
      wx.showToast({
        title: '裁剪区域无效',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({ title: '处理中...' });
    
    // 创建离屏Canvas进行裁剪
    const createCanvas = () => {
      return new Promise((resolve, reject) => {
        wx.createSelectorQuery()
          .select('#cropCanvas')
          .fields({ node: true, size: true })
          .exec((res) => {
            if (!res || !res[0] || !res[0].node) {
              reject(new Error('Canvas节点获取失败'));
              return;
            }
            
            const canvas = res[0].node;
            const ctx = canvas.getContext('2d');
            
            // 设置canvas大小为裁剪后的尺寸
            canvas.width = realCropFrame.width;
            canvas.height = realCropFrame.height;
            
            // 创建图片对象
            const image = canvas.createImage();
            
            image.onload = () => {
              // 先清空canvas
              ctx.clearRect(0, 0, canvas.width, canvas.height);
              
              // 不再填充白色背景，保留透明区域
              // ctx.fillStyle = '#FFFFFF';
              // ctx.fillRect(0, 0, canvas.width, canvas.height);
              
              // 绘制裁剪区域
              ctx.drawImage(
                image,
                realCropFrame.x, realCropFrame.y, realCropFrame.width, realCropFrame.height,
                0, 0, realCropFrame.width, realCropFrame.height
              );
              
              // 将canvas转为临时文件，使用png格式保留透明度
              wx.canvasToTempFilePath({
                canvas: canvas,
                fileType: 'png', // 使用png格式，保留透明度
                quality: 0.95,   // 设置较高的图片质量
                success: (res) => {
                  // 修复路径格式问题
                  let fixedPath = res.tempFilePath;
                  if (fixedPath && fixedPath.startsWith('http://tmp/')) {
                    fixedPath = fixedPath.replace('http://tmp/', '/tmp/');
                    console.log('修正Canvas生成的图片路径:', fixedPath);
                  }
                  resolve(fixedPath);
                },
                fail: (err) => {
                  console.error('Canvas转图片失败:', err);
                  reject(err);
                }
              });
            };
            
            image.onerror = (err) => {
              console.error('加载图片失败:', err);
              reject(err);
            };
            
            image.src = imagePath;
          });
      });
    };
    
    createCanvas()
      .then((croppedImagePath) => {
        wx.hideLoading();
        
        // 保存图片路径到当前页面，以便裁剪预览页面可以通过页面栈获取
        this.setData({
          tempCroppedPath: croppedImagePath,
          tempOriginalPath: imagePath
        });
        
        // 跳转到预览页面，不通过URL参数传递图片路径，避免路径编码问题
        wx.navigateTo({
          url: '/pages/crop-preview/crop-preview',
          success: (res) => {
            // 通过eventChannel传递数据
            if (res.eventChannel && typeof res.eventChannel.emit === 'function') {
              res.eventChannel.emit('acceptDataFromOpenerPage', { 
                croppedImagePath: croppedImagePath,
                originalImagePath: imagePath
              });
              console.log('成功通过eventChannel传递裁剪后图片到预览页:', croppedImagePath);
            } else {
              console.warn('eventChannel不可用，已通过URL参数传递数据');
            }
          },
          fail: (err) => {
            console.error('跳转到预览页面失败:', err);
            wx.showToast({
              title: '跳转失败',
              icon: 'none'
            });
          }
        });
      })
      .catch((err) => {
        wx.hideLoading();
        wx.showToast({
          title: '裁剪失败',
          icon: 'none'
        });
        console.error('裁剪失败:', err);
      });
  }
});