// 使用指南页面
const app = getApp();

Page({
  data: {
    navBarHeight: app.globalData.navBarHeight || 44,
    statusBarHeight: 20,
    menuButtonHeight: 32,
    // 使用指南内容
    guideContent: [
      {
        title: '基础操作',
        sections: [
          {
            subtitle: '拍照与选图',
            content: '首页点击"拍照"按钮可直接调用相机；点击"相册"可从手机相册中选择图片。',
            icon: 'camera'
          },
          {
            subtitle: '历史记录',
            content: '首页点击"历史"可查看您之前编辑过的所有图片，点击任意图片可重新编辑。',
            icon: 'history'
          }
        ]
      },
      {
        title: '图片编辑',
        sections: [
          {
            subtitle: '基础编辑',
            content: '在编辑页面，您可以对图片进行剪裁、旋转、翻转、调整亮度和对比度等基础操作。',
            icon: 'edit'
          },
          {
            subtitle: '滤镜应用',
            content: '"增鲜"滤镜专为食物照片优化，可让食物看起来更加鲜艳美味。',
            icon: 'filter'
          },
          {
            subtitle: '文字与贴纸',
            content: '您可以添加日期、餐次等文字信息，也可以添加学校徽标、食品营养图标等贴纸。',
            icon: 'text'
          },
          {
            subtitle: '手势操作',
            content: '单指拖动可移动图片，双指捏合可缩放图片，双指旋转可旋转图片，双击可重置图片。',
            icon: 'gesture'
          }
        ]
      },
      {
        title: '高级功能',
        sections: [
          {
            subtitle: '批量处理',
            content: '首页点击"批量处理"可同时编辑多张图片，提高工作效率。',
            icon: 'batch'
          },
          {
            subtitle: '模板应用',
            content: '您可以将常用的编辑风格保存为模板，下次编辑时直接应用，节省时间。',
            icon: 'template'
          },
          {
            subtitle: '快速模式',
            content: '编辑页面右上角的"快速模式"可一键优化照片并添加日期和餐次信息。',
            icon: 'quick'
          },
          {
            subtitle: '食堂环境预设',
            content: '编辑页面左上角的"食堂环境预设"可自动调整适合食堂光线条件的参数。',
            icon: 'preset'
          }
        ]
      },
      {
        title: '保存与分享',
        sections: [
          {
            subtitle: '保存图片',
            content: '编辑完成后点击底部"保存"按钮，图片将保存到手机相册中。',
            icon: 'save'
          },
          {
            subtitle: '分享图片',
            content: '您可以直接将编辑好的图片分享到微信群或好友，方便快捷地分享食堂饭菜情况。',
            icon: 'share'
          }
        ]
      }
    ]
  },
  
  onLoad: function() {
    // 设置导航栏高度
    this.setNavBarHeight();
  },
  
  // 设置导航栏高度
  setNavBarHeight: function() {
    const systemInfo = {
      ...wx.getDeviceInfo(),
      ...wx.getSystemSetting(),
      ...wx.getAppBaseInfo(),
      ...wx.getWindowInfo()
    };
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
    
    const statusBarHeight = systemInfo.statusBarHeight;
    const menuButtonHeight = menuButtonInfo.height;
    const menuButtonTop = menuButtonInfo.top;
    
    // 计算导航栏高度 = 状态栏高度 + 菜单按钮上下边距 * 2 + 菜单按钮高度
    const navBarHeight = statusBarHeight + (menuButtonTop - statusBarHeight) * 2 + menuButtonHeight;
    
    this.setData({
      navBarHeight,
      statusBarHeight,
      menuButtonHeight
    });
  },
  
  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  }
}); 