<view class="guide-container">
  <!-- 自定义导航栏 -->
  <view class="custom-nav" style="height:{{navBarHeight}}px;padding-top:{{statusBarHeight}}px;">
    <view class="nav-back" bindtap="goBack">
      <text class="back-icon">←</text>
    </view>
    <view class="nav-title">使用指南</view>
    <view class="nav-placeholder"></view>
  </view>
  
  <!-- 指南内容 -->
  <scroll-view scroll-y="true" class="guide-content" style="height:calc(100vh - {{navBarHeight}}px);">
    <block wx:for="{{guideContent}}" wx:key="title">
      <view class="guide-section">
        <view class="section-title">{{item.title}}</view>
        
        <view class="section-items">
          <block wx:for="{{item.sections}}" wx:for-item="section" wx:key="subtitle">
            <view class="guide-item">
              <view class="item-icon {{section.icon}}-icon"></view>
              <view class="item-content">
                <view class="item-subtitle">{{section.subtitle}}</view>
                <view class="item-text">{{section.content}}</view>
              </view>
            </view>
          </block>
        </view>
      </view>
    </block>
    
    <!-- 底部版本信息 -->
    <view class="guide-footer">
      <text>校园食堂图片管理助手 v1.0</text>
    </view>
  </scroll-view>
</view> 