/* 使用指南页面样式 */
.guide-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}

/* 导航栏样式 */
.custom-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  border-bottom: 1px solid #eee;
  padding: 0 15px;
  position: relative;
  z-index: 100;
}

.nav-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 20px;
  color: #333;
}

.nav-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.nav-placeholder {
  width: 40px;
}

/* 指南内容样式 */
.guide-content {
  flex: 1;
  padding: 20px 15px;
}

.guide-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 4px;
  height: 18px;
  width: 4px;
  background-color: #07c160;
  border-radius: 2px;
}

.section-items {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.guide-item {
  display: flex;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.guide-item:last-child {
  border-bottom: none;
}

.item-icon {
  width: 36px;
  height: 36px;
  margin-right: 12px;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  flex-shrink: 0;
}

.item-content {
  flex: 1;
}

.item-subtitle {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.item-text {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* 底部版本信息 */
.guide-footer {
  text-align: center;
  padding: 20px 0;
  color: #999;
  font-size: 12px;
}

/* 图标样式 */
.camera-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="%2307c160" d="M9,3L7.17,5H4C2.9,5,2,5.9,2,7v12c0,1.1,0.9,2,2,2h16c1.1,0,2-0.9,2-2V7c0-1.1-0.9-2-2-2h-3.17L15,3H9z M12,18c-2.76,0-5-2.24-5-5s2.24-5,5-5s5,2.24,5,5S14.76,18,12,18z"/><circle fill="%2307c160" cx="12" cy="13" r="3.2"/></svg>');
}

.history-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="%2307c160" d="M13,3c-4.97,0-9,4.03-9,9H1l3.89,3.89L8.78,12H5c0-4.42,3.58-8,8-8s8,3.58,8,8s-3.58,8-8,8c-3.07,0-5.75-1.74-7.1-4.29l-1.42,1.42C6.37,20.21,9.48,22,13,22c5.52,0,10-4.48,10-10S18.52,3,13,3z M12.5,8v4.25l3.5,2.08l-0.72,1.21L11,13V8H12.5z"/></svg>');
}

.edit-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="%2307c160" d="M3,17.25V21h3.75L17.81,9.94l-3.75-3.75L3,17.25z M20.71,7.04c0.39-0.39,0.39-1.02,0-1.41l-2.34-2.34c-0.39-0.39-1.02-0.39-1.41,0l-1.83,1.83l3.75,3.75L20.71,7.04z"/></svg>');
}

.filter-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="%2307c160" d="M19,3H5C3.9,3,3,3.9,3,5v14c0,1.1,0.9,2,2,2h14c1.1,0,2-0.9,2-2V5C21,3.9,20.1,3,19,3z M19,19H5V5h14V19z"/><path fill="%2307c160" d="M13.5,12l-1.5,0l0,1.5l1.5,0z"/><path fill="%2307c160" d="M13.5,9l-1.5,0l0,1.5l1.5,0z"/><path fill="%2307c160" d="M13.5,15l-1.5,0l0,1.5l1.5,0z"/></svg>');
}

.text-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="%2307c160" d="M5,17v2h14v-2H5z M9.5,12.8h5l0.9,2.2h2.1L12.75,4h-1.5L6.5,15h2.1L9.5,12.8z M12,6.9l1.75,4.1h-3.5L12,6.9z"/></svg>');
}

.gesture-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="%2307c160" d="M4.59,6.89c0.7-0.71,1.4-1.35,1.71-1.22c0.5,0.2,0,1.03-0.3,1.52c-0.25,0.42-2.86,3.89-2.86,6.31c0,1.28,0.48,2.34,1.34,2.98 c0.75,0.56,1.74,0.73,2.64,0.46c1.07-0.31,1.95-1.4,3.06-2.77c1.21-1.49,2.83-3.44,4.08-3.44c1.63,0,1.65,1.01,1.76,1.79 c-3.78,0.64-5.38,3.67-5.38,5.37c0,1.7,1.44,3.09,3.21,3.09c1.63,0,4.29-1.33,4.69-6.1H21v-2.5h-2.47c-0.15-1.65-1.09-4.2-4.03-4.2 c-2.25,0-4.18,1.91-4.94,2.84c-0.58,0.73-2.06,2.48-2.29,2.72c-0.25,0.3-0.68,0.84-1.11,0.84c-0.45,0-0.72-0.83-0.36-1.92 c0.35-1.09,1.4-2.86,1.85-3.52c0.78-1.14,1.3-1.92,1.3-3.28C8.95,3.69,7.31,3,6.44,3C5.12,3,3.97,4,3.72,4.25 c-0.36,0.36-0.66,0.66-0.88,0.93l1.75,1.71L4.59,6.89z M13.88,18.55c-0.31,0-0.74-0.26-0.74-0.72c0-0.6,0.73-2.2,2.87-2.76 C15.84,17.38,14.58,18.55,13.88,18.55z"/></svg>');
}

.batch-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="%2307c160" d="M3,5H1v16c0,1.1,0.9,2,2,2h16v-2H3V5z"/><path fill="%2307c160" d="M21,1H7C5.9,1,5,1.9,5,3v14c0,1.1,0.9,2,2,2h14c1.1,0,2-0.9,2-2V3C23,1.9,22.1,1,21,1z M21,17H7V3h14V17z"/><path fill="%2307c160" d="M15,5h2v2h-2V5z M11,5h2v2h-2V5z M15,9h2v2h-2V9z M11,9h2v2h-2V9z M15,13h2v2h-2V13z M11,13h2v2h-2V13z"/></svg>');
}

.template-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="%2307c160" d="M19,3H5C3.9,3,3,3.9,3,5v14c0,1.1,0.9,2,2,2h14c1.1,0,2-0.9,2-2V5C21,3.9,20.1,3,19,3z M5,5h6v14H5V5z M19,19h-6V5h6V19z"/><path fill="%2307c160" d="M13.5,12l-1.5,0l0,1.5l1.5,0z"/><path fill="%2307c160" d="M13.5,9l-1.5,0l0,1.5l1.5,0z"/><path fill="%2307c160" d="M13.5,15l-1.5,0l0,1.5l1.5,0z"/></svg>');
}

.quick-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="%2307c160" d="M13,3c-4.97,0-9,4.03-9,9H1l3.89,3.89L8.78,12H5c0-4.42,3.58-8,8-8s8,3.58,8,8s-3.58,8-8,8c-3.07,0-5.75-1.74-7.1-4.29l-1.42,1.42C6.37,20.21,9.48,22,13,22c5.52,0,10-4.48,10-10S18.52,3,13,3z M12.5,8v4.25l3.5,2.08l-0.72,1.21L11,13V8H12.5z"/></svg>');
}

.preset-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="%2307c160" d="M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36c-0.98,1.37-2.58,2.26-4.4,2.26 c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z"/></svg>');
}

.save-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="%2307c160" d="M17,3H5C3.89,3,3,3.9,3,5v14c0,1.1,0.89,2,2,2h14c1.1,0,2-0.9,2-2V7L17,3z M19,19H5V5h11.17L19,7.83V19z M12,12 c-1.66,0-3,1.34-3,3s1.34,3,3,3s3-1.34,3-3S13.66,12,12,12z M6,6h9v4H6V6z"/></svg>');
}

.share-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="%2307c160" d="M18,16.08c-0.76,0-1.44,0.3-1.96,0.77L8.91,12.7C8.96,12.47,9,12.24,9,12s-0.04-0.47-0.09-0.7l7.05-4.11 c0.54,0.5,1.25,0.81,2.04,0.81c1.66,0,3-1.34,3-3s-1.34-3-3-3s-3,1.34-3,3c0,0.24,0.04,0.47,0.09,0.7L8.04,9.81 C7.5,9.31,6.79,9,6,9c-1.66,0-3,1.34-3,3s1.34,3,3,3c0.79,0,1.5-0.31,2.04-0.81l7.12,4.16c-0.05,0.21-0.08,0.43-0.08,0.65 c0,1.61,1.31,2.92,2.92,2.92s2.92-1.31,2.92-2.92C20.92,17.39,19.61,16.08,18,16.08z"/></svg>');
} 