/* 多图编辑页面样式 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 0;
  box-sizing: border-box;
  background-color: #f8f8f8;
}

/* 预览区域 */
.preview-area {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
  background-color: #fff;
}

.canvas-container {
  position: relative;
  width: 90%;
  height: 400rpx;
  background-color: #f0f0f0;
  border-radius: 10rpx;
  overflow: hidden;
}

.preview-canvas {
  width: 100%;
  height: 100%;
}

.loading-text {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #666666;
  font-size: 30rpx;
}

/* 工具栏 */
.toolbar {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
  background-color: #fff;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 40rpx;
  padding: 10rpx;
  border-radius: 10rpx;
}

.tool-item.active {
  background-color: #f1f1f1;
}

.tool-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 10rpx;
}

.tool-text {
  font-size: 24rpx;
  color: #666;
}

/* 贴纸面板 */
.sticker-panel {
  height: 200rpx;
  background-color: #fff;
  padding: 20rpx;
}

.sticker-grid {
  display: flex;
  flex-wrap: wrap;
}

.sticker-item {
  width: 100rpx;
  height: 100rpx;
  margin: 10rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  border: 1px solid #eee;
}

.sticker-image {
  width: 80%;
  height: 80%;
}

/* 模板区域 */
.templates-area {
  padding: 20rpx;
  background-color: #fff;
  margin-top: 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
  display: block;
}

.template-scroll {
  white-space: nowrap;
  width: 100%;
}

.template-item {
  display: inline-block;
  width: 200rpx;
  height: 160rpx;
  margin-right: 20rpx;
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;
  background-color: #f0f0f0;
}

.template-item.selected {
  border: 2px solid #1aad19;
}

.template-image {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-size: 26rpx;
  color: #666;
}

/* 底部按钮 */
.btn-group {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 40rpx;
  margin-top: auto;
  background-color: #fff;
  border-top: 1px solid #eee;
}

.btn {
  width: 300rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 32rpx;
}

.btn-cancel {
  color: #666;
  background-color: #f1f1f1;
}

.btn-primary {
  color: #fff;
  background-color: #07c160;
}

/* 处理中遮罩 */
.processing-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.processing-content {
  width: 80%;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 40rpx;
  text-align: center;
}

.processing-content text {
  display: block;
  margin-bottom: 30rpx;
}

.preview-area {
  position: relative;
  flex: 1;
  margin: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.template-canvas {
  width: 100% !important;
  height: 100% !important;
  display: block;
}

/* Canvas在Skyline模式下的样式调整 */
.skyline-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
}

.canvas-fallback {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #999;
  font-size: 28rpx;
  z-index: 10;
}

/* 编辑工具区 */
.edit-tools {
  background: #fff;
  padding: 20rpx;
  border-radius: 24rpx 24rpx 0 0;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 模板选择 */
.template-scroll {
  white-space: nowrap;
  margin-bottom: 20rpx;
  height: 150rpx;
}

.template-item {
  display: inline-block;
  width: 120rpx;
  height: 120rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  border: 2rpx solid #eee;
  background: #f5f5f5;
}

.template-item.active {
  border-color: #07c160;
}

.template-thumb {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-color: #eee;
}

.template-name {
  font-size: 20rpx;
  color: #333;
  display: block;
  text-align: center;
  margin-top: 8rpx;
}

/* 装饰工具栏 */
.decoration-tools {
  display: flex;
  justify-content: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.tool-item {
  width: 80rpx;
  height: 80rpx;
  margin: 0 20rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

.tool-item.active {
  background: #e6f7e9;
}

.tool-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 贴纸选择区 */
.sticker-scroll {
  padding: 20rpx 0;
  height: 160rpx;
}

.sticker-group {
  display: flex;
  align-items: center;
}

.sticker-item {
  width: 100rpx;
  height: 100rpx;
  margin-right: 20rpx;
  padding: 10rpx;
  background: #fff;
  border-radius: 12rpx;
  border: 1rpx solid #eee;
}

/* 底部按钮 */
.bottom-buttons {
  display: flex;
  padding: 20rpx;
  background: #fff;
}

.cancel-btn, .save-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  margin: 0 10rpx;
  font-size: 32rpx;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.save-btn {
  background: #07c160;
  color: #fff;
}

/* 图片模板预览区域 */
.template-preview-area {
  position: relative;
  height: 40vh;
  background-color: #000;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.template-preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 图片索引指示器 */
.image-indicator {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
}

/* 模板选择区 */
.template-selection {
  padding: 15px;
  background-color: #fff;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #333;
}

.template-item {
  display: inline-block;
  width: 80px;
  margin-right: 10px;
  text-align: center;
}

.template-thumbnail {
  width: 70px;
  height: 70px;
  border-radius: 6px;
  border: 2px solid #eee;
  background-color: #f5f5f5;
  margin-bottom: 5px;
}

.template-item.active .template-thumbnail {
  border-color: #07c160;
}

.template-name {
  font-size: 12px;
  color: #666;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.template-item.active .template-name {
  color: #07c160;
}

/* 编辑工具区域 */
.editing-tools {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.tool-section {
  background-color: #fff;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* 滤镜样式 */
.filter-scroll {
  white-space: nowrap;
  height: 90px;
}

.filter-item {
  display: inline-block;
  width: 70px;
  margin-right: 15px;
  text-align: center;
  vertical-align: top;
}

.filter-preview {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  margin-bottom: 5px;
  border: 2px solid #eee;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-image: url('linear-gradient(45deg, #f0f0f0, #e0e0e0)');
}

.filter-item.active .filter-preview {
  border-color: #07c160;
}

.filter-item text {
  font-size: 12px;
  color: #333;
}

.filter-item.active text {
  color: #07c160;
}

/* 滤镜预览样式 */
.filter-preview.none {
  filter: none;
}

.filter-preview.food {
  filter: saturate(1.4) contrast(1.1) brightness(1.1);
}

.filter-preview.warm {
  filter: sepia(0.3) saturate(1.3) contrast(0.9) brightness(1.1);
}

.filter-preview.fresh {
  filter: saturate(1.2) hue-rotate(-10deg) contrast(1.1) brightness(1.05);
}

/* 调整项 */
.adjust-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.adjust-item .label {
  width: 70px;
  font-size: 14px;
  color: #666;
}

.adjust-item slider {
  flex: 1;
  margin: 0 10px;
}

.setting-value {
  width: 50px;
  text-align: right;
  font-size: 14px;
  color: #333;
}

/* 颜色选择器 */
.color-picker {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}

.color-item {
  width: 30px;
  height: 30px;
  border-radius: 4px;
  margin-right: 10px;
  margin-bottom: 10px;
  border: 2px solid transparent;
}

.color-item.active {
  border-color: #07c160;
}

/* 快捷功能按钮 */
.quick-buttons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.quick-button {
  flex: 1;
  margin: 0 5px;
  height: 40px;
  font-size: 14px;
  color: #fff;
  background-color: #07c160;
  border-radius: 6px;
}

/* 底部工具栏 */
.bottom-toolbar {
  display: flex;
  height: 60px;
  background-color: #fff;
  border-top: 1px solid #eee;
}

.toolbar-button {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.toolbar-button.cancel {
  color: #999;
}

.toolbar-button.save {
  color: #07c160;
}

/* 加载中蒙层 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 20px 30px;
  border-radius: 10px;
  color: #fff;
}

.loading-icon {
  width: 40px;
  height: 40px;
  margin-bottom: 10px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 低电量模式 */
.low-power-mode .filter-container,
.low-power-mode .preset-container,
.low-power-mode .beautify-container {
  opacity: 0.6;
}

/* 模板面板样式 */
.template-panel {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.panel-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.panel-close {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  color: #666;
}

/* 分类标签页 */
.category-tabs {
  margin-bottom: 15px;
}

.tabs-scroll {
  white-space: nowrap;
  height: 40px;
}

.tab-item {
  display: inline-block;
  padding: 0 15px;
  height: 36px;
  line-height: 36px;
  margin-right: 10px;
  border-radius: 18px;
  background-color: #f5f5f5;
  font-size: 14px;
  color: #666;
}

.tab-item.active {
  background-color: #07c160;
  color: #fff;
}

/* 模板预览 */
.template-preview-container {
  height: 180px;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.template-empty {
  height: 180px;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
  border-radius: 10px;
  font-size: 14px;
  color: #999;
}

/* 模板列表 */
.template-list {
  margin-bottom: 15px;
}

/* 模板设置 */
.template-settings {
  background-color: #f5f5f5;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
}

.setting-item {
  margin-bottom: 15px;
}

/* 应用模板按钮 */
.apply-template {
  margin-top: 20px;
}

.apply-template-btn {
  height: 40px;
  font-size: 16px;
  color: #fff;
  background-color: #07c160;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 食堂风格模板样式 */
.cafeteria-template {
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.cafeteria-title {
  background-color: #ff6b6b;
  color: white;
  text-align: center;
  padding: 10px;
  font-weight: bold;
  font-size: 16px;
  position: relative;
  border-radius: 10px;
  margin: 5px auto;
  width: 60%;
}

.cafeteria-title-decoration {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 15px;
  height: 15px;
  background-color: #fffc00;
  border-radius: 50%;
}

.decoration-left {
  left: -10px;
}

.decoration-right {
  right: -10px;
}

.cafeteria-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 10px;
  padding: 10px;
  background-color: #f5f5f5;
}

.cafeteria-item {
  position: relative;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  padding: 5px;
}

.cafeteria-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cafeteria-label {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 5px 0;
  background-color: #ff7675;
  color: white;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
}

.cafeteria-label.round {
  width: 70%;
  left: 15%;
  border-radius: 999px;
}

.cafeteria-rating {
  position: absolute;
  top: 5px;
  left: 5px;
  width: 24px;
  height: 24px;
  background-color: #fffc00;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cafeteria-rating-icon {
  font-size: 14px;
  color: #333;
}

/* 修改模板缩略图样式，适应食堂风格 */
.template-item .template-thumbnail {
  background-size: cover;
  background-position: center;
  border-radius: 6px;
}

.template-item[data-id="cafeteria_1"] .template-thumbnail {
} 