const app = getApp();

// 多图编辑器页面
Page({
  data: {
    imagePaths: [],           // 图片路径数组
    isProcessing: false,      // 是否正在处理中
    
    // 编辑模式相关
    editorMode: 'imageTitle', // 当前编辑模式: imageTitle, previewTitle
    showToolButtons: true,    // 是否显示工具按钮区域
    
    // 标题相关
    imageTitles: [],         // 所有图片的标题数组
    previewTitle: '',        // 预览区标题
    themeTitle: '',          // 主题标题
    selectedImageIndex: -1,  // 当前选中的图片索引，-1表示未选中
    isEditingPreviewTitle: false, // 是否正在编辑预览区标题
    hasImageTitles: false,   // 是否有图片标题
    
    // 美化相关
    isBeautified: false,     // 是否已美化
    decorations: [],         // 装饰元素数组
    foodQuote: '',           // 美食名言
    
    // 提示相关
    showTitleSuccess: false,  // 是否显示添加标题成功提示
    
    // 编辑相关已移至微信原生模态框
    
    // Canvas相关
    canvasWidth: 750,        // 画布宽度
    canvasHeight: 1200,      // 画布高度（会根据内容动态调整）
    canvasInitialized: false, // Canvas是否已初始化
    
    // 分享相关
    composedImageWidth: 0,    // 合成图片的宽度
    composedImageHeight: 0,   // 合成图片的高度
    lastSharedImage: '',      // 最后一次分享的图片路径

    // 调试信息
    debugInfo: {
      show: false,
      platform: '',
      version: '',
      system: '',
      pixelRatio: 1,
      pathCount: 0,
      paths: []
    }
  },
  
  // 页面加载时
  onLoad: function(options) {
    console.log('多图编辑页面加载，参数:', options);

    // 初始化调试信息
    this.initDebugInfo();

    // 初始化编辑模式和标题相关数据 - 强制使用图片标题模式
    this.setData({
      editorMode: 'imageTitle',  // 强制为图片标题模式
      showToolButtons: true,     // 初始默认显示工具按钮
      imageTitles: [],          // 所有图片的标题数组
      previewTitle: '',         // 预览区标题始终为空
      themeTitle: '',           // 初始化时不显示主题，只有点击添加主题按钮后才显示
      selectedImageIndex: -1,    // 当前选中的图片索引，-1表示未选中
      showTitleSuccess: false    // 初始不显示成功提示
    });
    
    // 获取传递的图片路径数组
    if (options && options.imagePaths) {
      try {
        console.log('尝试从URL参数解析图片路径');
        let imagePaths;
        
        // 尝试解析JSON格式的图片路径
        try {
          imagePaths = JSON.parse(options.imagePaths);
          console.log('成功解析JSON格式图片路径:', imagePaths);
        } catch (jsonErr) {
          console.warn('JSON解析失败，尝试作为单个路径处理:', options.imagePaths);
          // 如果不是JSON格式，可能是单个路径字符串
          imagePaths = [options.imagePaths];
        }
        
        // 处理图片路径
        this.processImagePaths(imagePaths);
      } catch (e) {
        console.error('处理URL参数图片路径失败:', e);
        wx.showToast({
          title: '加载图片失败',
          icon: 'none'
        });
      }
    } else {
      // 使用EventChannel获取图片路径数组
      try {
        const eventChannel = this.getOpenerEventChannel();
        if (eventChannel && eventChannel.on) {
          console.log('尝试从EventChannel获取图片路径');
          
          eventChannel.on('acceptDataFromOpenerPage', (data) => {
            console.log('接收到的图片数据:', data);
            
            if (!data) {
              console.error('接收到的数据为空');
              return;
            }
            
            // 添加临时文件格式处理
            let imagePaths = [];
            
            // 检查是否有images字段（从album页面传递）
            if (data.images && Array.isArray(data.images)) {
              console.log('从images字段获取图片路径');
              imagePaths = data.images;
            }
            // 向后兼容旧版本代码的imagePaths字段
            else if (data.imagePaths && Array.isArray(data.imagePaths)) {
              console.log('从imagePaths字段获取图片路径');
              imagePaths = data.imagePaths;
            }
            // 检查是否只有一个图片路径
            else if (data.imagePath && typeof data.imagePath === 'string') {
              console.log('从imagePath字段获取单个图片路径');
              imagePaths = [data.imagePath];
            }
            // 检查data本身是否为数组
            else if (Array.isArray(data)) {
              console.log('数据本身是图片路径数组');
              imagePaths = data;
            }
            // 检查data本身是否为字符串（单个图片路径）
            else if (typeof data === 'string') {
              console.log('数据本身是单个图片路径字符串');
              imagePaths = [data];
            }
            
            // 处理图片路径
            this.processImagePaths(imagePaths);
          });
        } else {
          console.warn('无法获取EventChannel');
        }
      } catch (channelErr) {
        console.error('获取EventChannel失败:', channelErr);
      }
    }
  },

  // 初始化调试信息
  initDebugInfo: function() {
    const systemInfo = wx.getSystemInfoSync();
    const isDevTool = systemInfo.platform === 'devtools';

    // 在iOS真机环境下显示调试信息
    const showDebug = systemInfo.platform === 'ios' && !isDevTool;

    this.setData({
      debugInfo: {
        show: showDebug,
        platform: systemInfo.platform,
        version: systemInfo.version,
        system: systemInfo.system,
        pixelRatio: systemInfo.pixelRatio,
        pathCount: 0,
        paths: []
      }
    });

    console.log('调试信息初始化:', this.data.debugInfo);
  },

  // 更新调试信息中的路径信息
  updateDebugPaths: function(paths) {
    if (this.data.debugInfo.show) {
      this.setData({
        'debugInfo.pathCount': paths ? paths.length : 0,
        'debugInfo.paths': paths || []
      });
    }
  },

  // 页面显示时初始化Canvas
  onReady: function() {
    // 延迟初始化Canvas，确保页面已完全渲染
    setTimeout(() => {
      this.initCanvas();
    }, 300);
    
    // 显示分享菜单，包括分享到朋友圈
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },
  
  // 初始化Canvas
  initCanvas: function() {
    return new Promise((resolve, reject) => {
      console.log('开始初始化Canvas');

      const query = wx.createSelectorQuery();
      query.select('#compositeCanvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          console.log('Canvas查询结果:', res);

          if (!res || !res[0]) {
            console.error('Canvas查询结果为空');
            reject(new Error('Canvas查询结果为空'));
            return;
          }

          if (!res[0].node) {
            console.error('Canvas节点不存在');
            reject(new Error('Canvas节点不存在'));
            return;
          }

          try {
            const canvas = res[0].node;
            console.log('获取到Canvas节点:', canvas);

            if (!canvas) {
              throw new Error('Canvas节点为null');
            }

            const ctx = canvas.getContext('2d');
            if (!ctx) {
              throw new Error('无法获取Canvas 2D上下文');
            }

            // 设置画布大小
            const info = wx.getWindowInfo();
            const dpr = info.pixelRatio || 2;

            // 确保Canvas有合理的尺寸
            const canvasWidth = this.data.canvasWidth || 750;
            const canvasHeight = this.data.canvasHeight || 800;

            console.log('设置Canvas尺寸:', canvasWidth, 'x', canvasHeight, 'DPR:', dpr);

            // 安全地设置Canvas尺寸
            try {
              // 检查canvas对象是否有width和height属性
              if (typeof canvas.width === 'undefined' || typeof canvas.height === 'undefined') {
                console.error('Canvas对象不支持width/height属性');
                throw new Error('Canvas对象不支持width/height属性');
              }

              const physicalWidth = canvasWidth * dpr;
              const physicalHeight = canvasHeight * dpr;

              console.log('准备设置Canvas物理尺寸:', physicalWidth, 'x', physicalHeight);

              canvas.width = physicalWidth;
              canvas.height = physicalHeight;

              console.log('Canvas物理尺寸设置成功:', canvas.width, 'x', canvas.height);
            } catch (sizeError) {
              console.error('设置Canvas尺寸失败:', sizeError);
              console.error('Canvas对象:', canvas);
              throw new Error('设置Canvas尺寸失败: ' + sizeError.message);
            }

            // 设置Canvas样式尺寸（如果支持）
            if (canvas.style) {
              canvas.style.width = canvasWidth + 'px';
              canvas.style.height = canvasHeight + 'px';
            }

            // 缩放上下文以匹配设备像素比
            ctx.scale(dpr, dpr);

            // 设置高质量渲染
            if (ctx.imageSmoothingEnabled !== undefined) {
              ctx.imageSmoothingEnabled = true;
            }
            if (ctx.imageSmoothingQuality !== undefined) {
              ctx.imageSmoothingQuality = 'high';
            }

            // 保存Canvas引用
            this.canvas = canvas;
            this.ctx = ctx;
            this.dpr = dpr;

            this.setData({
              canvasInitialized: true,
              canvasWidth: canvasWidth,
              canvasHeight: canvasHeight
            });

            console.log('Canvas初始化成功');
            resolve();

          } catch (error) {
            console.error('Canvas初始化过程中出错:', error);
            reject(error);
          }
        });
    });
  },

  // 修复图片路径格式，确保跨平台兼容性
  fixImagePath: function(imagePath) {
    if (!imagePath || typeof imagePath !== 'string') {
      return imagePath;
    }

    let fixedPath = imagePath;
    const systemInfo = wx.getSystemInfoSync();
    const isDevTool = systemInfo.platform === 'devtools';
    const isIOS = systemInfo.platform === 'ios';

    console.log('修复图片路径 - 原始路径:', fixedPath);
    console.log('系统信息:', { platform: systemInfo.platform, isDevTool, isIOS });

    // iOS真机环境特殊处理
    if (isIOS && !isDevTool) {
      console.log('iOS真机环境，进行特殊路径处理');

      // 移除可能的前缀
      if (fixedPath.startsWith('wxfile://')) {
        fixedPath = fixedPath.replace('wxfile://', '');
        console.log('移除wxfile前缀:', fixedPath);
      }

      // iOS真机环境下，确保临时文件路径格式正确
      if (fixedPath.startsWith('http://tmp/')) {
        fixedPath = fixedPath.replace('http://tmp/', '/tmp/');
        console.log('iOS真机修正HTTP临时文件路径:', fixedPath);
      }

      // 确保路径以/开头
      if (!fixedPath.startsWith('/') && !fixedPath.startsWith('http')) {
        fixedPath = '/' + fixedPath;
        console.log('iOS真机添加路径前缀:', fixedPath);
      }
    } else if (!isDevTool && fixedPath.startsWith('http://tmp/')) {
      // 其他真机环境
      fixedPath = fixedPath.replace('http://tmp/', '/tmp/');
      console.log('真机环境修正HTTP临时文件路径:', fixedPath);
    } else if (fixedPath.startsWith('http://tmp/')) {
      console.log('开发工具环境保持HTTP临时文件路径:', fixedPath);
    }

    // 处理URL编码问题
    if (fixedPath.indexOf('%') >= 0) {
      try {
        fixedPath = decodeURIComponent(fixedPath);
        console.log('解码路径:', fixedPath);
      } catch (e) {
        console.warn('路径解码失败:', e);
      }
    }

    // 最终路径验证和修正
    console.log('最终修正后的路径:', fixedPath);
    return fixedPath;
  },

  // 处理图片路径
  processImagePaths: function(imagePaths) {
    console.log('处理图片路径数组:', imagePaths);

    // 更新调试信息
    this.updateDebugPaths(imagePaths);

    if (!imagePaths) {
      console.error('图片路径数组为空');
      this.showImageLoadError('未接收到图片数据');
      return;
    }

    if (!Array.isArray(imagePaths)) {
      console.log('图片路径不是数组，转换为数组');
      // 单个路径转为数组
      imagePaths = [imagePaths];
      // 更新调试信息
      this.updateDebugPaths(imagePaths);
    }

    // 修复并验证图片路径
    this.validateAndFixImagePaths(imagePaths);
  },

  // 验证和修复图片路径
  validateAndFixImagePaths: function(imagePaths) {
    console.log('开始验证和修复图片路径:', imagePaths);

    const validatedPaths = [];
    const validationPromises = [];

    // 对每个路径进行验证
    imagePaths.forEach((path, index) => {
      if (!path || typeof path !== 'string' || path.length === 0) {
        console.warn(`图片路径 ${index} 无效:`, path);
        return;
      }

      const fixedPath = this.fixImagePath(path);
      console.log(`图片路径 ${index} 修复结果:`, { original: path, fixed: fixedPath });

      // 创建验证Promise
      const validationPromise = this.validateImagePath(fixedPath, index)
        .then(validPath => {
          if (validPath) {
            validatedPaths[index] = validPath;
            console.log(`图片路径 ${index} 验证成功:`, validPath);
          }
          return validPath;
        })
        .catch(error => {
          console.error(`图片路径 ${index} 验证失败:`, error);
          return null;
        });

      validationPromises.push(validationPromise);
    });

    // 等待所有验证完成
    Promise.all(validationPromises)
      .then(results => {
        const finalValidPaths = validatedPaths.filter(path => path);
        console.log('最终有效图片路径:', finalValidPaths);

        if (finalValidPaths.length === 0) {
          this.showImageLoadError('所有图片路径都无效，请重新选择图片');
          return;
        }

        // 设置有效的图片路径
        this.setValidImagePaths(finalValidPaths);
      })
      .catch(error => {
        console.error('图片路径验证过程出错:', error);
        this.showImageLoadError('图片加载验证失败');
      });
  },

  // 验证单个图片路径
  validateImagePath: function(imagePath, index) {
    return new Promise((resolve, reject) => {
      console.log(`验证图片路径 ${index}:`, imagePath);

      // 首先检查文件是否存在
      wx.getFileInfo({
        filePath: imagePath,
        success: (res) => {
          console.log(`图片文件 ${index} 信息:`, res);

          // 检查文件大小是否合理（不能为0，不能超过50MB）
          if (res.size === 0) {
            reject(new Error('图片文件大小为0'));
            return;
          }

          if (res.size > 50 * 1024 * 1024) {
            reject(new Error('图片文件过大（超过50MB）'));
            return;
          }

          // 尝试获取图片信息以验证是否为有效图片
          wx.getImageInfo({
            src: imagePath,
            success: (imgInfo) => {
              console.log(`图片 ${index} 信息验证成功:`, imgInfo);

              // 检查图片尺寸是否合理
              if (imgInfo.width === 0 || imgInfo.height === 0) {
                reject(new Error('图片尺寸无效'));
                return;
              }

              resolve(imagePath);
            },
            fail: (imgError) => {
              console.error(`图片 ${index} 信息获取失败:`, imgError);
              reject(new Error('无法获取图片信息: ' + imgError.errMsg));
            }
          });
        },
        fail: (fileError) => {
          console.error(`图片文件 ${index} 不存在或无法访问:`, fileError);
          reject(new Error('图片文件不存在或无法访问: ' + fileError.errMsg));
        }
      });
    });
  },

  // 设置有效的图片路径
  setValidImagePaths: function(validPaths) {
    console.log('设置有效图片路径:', validPaths);

    // 准备标题数组
    const newImageTitles = this.data.imageTitles && this.data.imageTitles.length > 0 ?
      // 确保标题数组长度与图片数量一致
      this.data.imageTitles.length === validPaths.length ?
        this.data.imageTitles : // 长度一致，保留原有标题
        validPaths.map((_, i) => this.data.imageTitles[i] || '') : // 长度不一致，尽可能保留原有标题
      new Array(validPaths.length).fill(''); // 没有原有标题，初始化为空字符串

    // 检查是否有标题（至少有一个图片有标题）
    const hasAnyTitle = newImageTitles.some(title => title && title.trim() !== '');

    // 更新图片路径和标题
    this.setData({
      imagePaths: validPaths,
      imageTitles: newImageTitles,
      hasImageTitles: hasAnyTitle // 初始化hasImageTitles属性
    });

    // 显示成功提示
    wx.showToast({
      title: `成功加载${validPaths.length}张图片`,
      icon: 'success',
      duration: 2000
    });
  },

  // 显示图片加载错误
  showImageLoadError: function(message) {
    console.error('图片加载错误:', message);

    wx.showModal({
      title: '图片加载失败',
      content: message || '无法加载图片，请重新选择',
      showCancel: true,
      cancelText: '返回',
      confirmText: '重新选择',
      success: (res) => {
        if (res.confirm) {
          // 重新选择图片
          this.chooseImages();
        } else {
          // 返回上一页
          wx.navigateBack({
            fail: () => {
              wx.switchTab({
                url: '/pages/index/index'
              });
            }
          });
        }
      }
    });
  },

  // 重新选择图片
  chooseImages: function() {
    wx.chooseMedia({
      count: 9,
      mediaType: ['image'],
      sourceType: ['album'],
      success: (res) => {
        const tempFiles = res.tempFiles;
        const images = tempFiles.map(file => file.tempFilePath);

        if (images.length > 0) {
          this.processImagePaths(images);
        }
      },
      fail: (err) => {
        console.error('重新选择图片失败:', err);
        if (err.errMsg !== "chooseMedia:fail cancel") {
          wx.showToast({
            title: '选择图片失败',
            icon: 'none'
          });
        }
      }
    });
  },
    
    // 准备标题数组
    const newImageTitles = this.data.imageTitles && this.data.imageTitles.length > 0 ?
      // 确保标题数组长度与图片数量一致
      this.data.imageTitles.length === validPaths.length ?
        this.data.imageTitles : // 长度一致，保留原有标题
        validPaths.map((_, i) => this.data.imageTitles[i] || '') : // 长度不一致，尽可能保留原有标题
      new Array(validPaths.length).fill(''); // 没有原有标题，初始化为空字符串
    
    // 检查是否有标题（至少有一个图片有标题）
    const hasAnyTitle = newImageTitles.some(title => title && title.trim() !== '');
    
    // 更新图片路径和标题
    this.setData({
      imagePaths: validPaths,
      imageTitles: newImageTitles,
      hasImageTitles: hasAnyTitle // 初始化hasImageTitles属性
    });
  },
  
  // 切换编辑模式 - 已限制只能使用图片标题模式
  changeEditorMode: function(e) {
    // 强制设置为图片标题模式，忽略传入的mode
    // 切换工具按钮的显示状态
    this.setData({
      editorMode: 'imageTitle',
      showToolButtons: !this.data.showToolButtons
    });
  },
  
  // 选择图片并编辑标题
  selectImage: function(e) {
    const index = e.currentTarget.dataset.index;
    
    // 设置选中状态
    this.setData({ 
      selectedImageIndex: index
    });
    
    console.log('选择图片，索引:', index);
    
    // 自动切换到图片标题模式
    if (this.data.editorMode !== 'imageTitle') {
      this.setData({ editorMode: 'imageTitle' });
    }
    
    // 显示编辑对话框
    wx.showModal({
      title: '编辑图片标题',
      content: this.data.imageTitles[index] || '',
      editable: true,
      placeholderText: `请输入图片标题${index + 1}`,
      value: this.data.imageTitles[index] || '',
      success: (res) => {
        if (res.confirm) {
          // 更新特定图片的标题
          const newImageTitles = [...this.data.imageTitles];
          newImageTitles[index] = res.content.trim();
          
          this.setData({ 
            imageTitles: newImageTitles,
            selectedImageIndex: -1 // 编辑完成后清除选中状态
          });
          
          wx.showToast({
            title: '标题已更新',
            icon: 'success'
          });
        } else {
          // 用户取消编辑，清除选中状态
          this.setData({ 
            selectedImageIndex: -1
          });
        }
      }
    });
  },
  
  // 编辑图片标题（点击标题时触发）
  editImageTitle: function(e) {
    const index = e.currentTarget.dataset.index;
    
    // 设置选中状态
    this.setData({ 
      selectedImageIndex: index
    });
    
    console.log('编辑图片标题，索引:', index);
    
    // 自动切换到图片标题模式
    if (this.data.editorMode !== 'imageTitle') {
      this.setData({ editorMode: 'imageTitle' });
    }
    
    // 使用微信原生模态框编辑图片标题
    const currentTitle = this.data.imageTitles[index] || '';
    wx.showModal({
      title: '编辑图片标题',
      content: currentTitle,
      editable: true,
      placeholderText: `请输入图片标题${index + 1}`,
      success: (res) => {
        if (res.confirm) {
          // 用户点击确定，更新图片标题
          const newImageTitles = [...this.data.imageTitles];
          newImageTitles[index] = res.content.trim();
          
          // 检查是否有标题（至少有一个图片有标题）
          const hasAnyTitle = newImageTitles.some(title => title && title.trim() !== '');
          
          this.setData({ 
            imageTitles: newImageTitles,
            hasImageTitles: hasAnyTitle, // 更新hasImageTitles状态
            selectedImageIndex: -1 // 编辑完成后清除选中状态
          });
          
          wx.showToast({
            title: '标题已更新',
            icon: 'success'
          });
        } else {
          // 用户点击取消，清除选中状态
          this.setData({ 
            selectedImageIndex: -1
          });
        }
      }
    });
  },
  
  // 图片标题模态框确认按钮事件已移除，使用微信原生模态框
  
  // 图片标题模态框取消按钮事件已移除，使用微信原生模态框
  
  // 编辑预览区标题
  editPreviewTitle: function() {
    // 设置预览区标题为编辑状态
    this.setData({ 
      isEditingPreviewTitle: true,
      editorMode: 'previewTitle' // 自动切换到预览标题模式
    });
    
    // 显示编辑对话框
    wx.showModal({
      title: '编辑预览区标题',
      content: '',
      editable: true,
      placeholderText: '请输入预览区标题',
      value: this.data.previewTitle || '',
      success: (res) => {
        if (res.confirm) {
          // 更新预览区标题
          this.setData({ 
            previewTitle: res.content.trim() || '默认标题',
            isEditingPreviewTitle: false // 编辑完成后清除编辑状态
          });
          
          wx.showToast({
            title: '标题已更新',
            icon: 'success'
          });
        } else {
          // 用户取消编辑，清除编辑状态
          this.setData({ 
            isEditingPreviewTitle: false
          });
        }
      }
    });
  },
  
  // 添加图片标题 - 为所有图片添加默认标题
  addImageTitle: function() {
    const { imagePaths, imageTitles } = this.data;
    
    // 创建带序号的默认标题数组，保留已有的标题
    const newImageTitles = imagePaths.map((_, index) => {
      // 如果已经有标题，则保留原标题，否则使用默认标题
      return imageTitles[index] || `图片标题${index + 1}`;
    });
    
    this.setData({ 
      imageTitles: newImageTitles,
      showTitleSuccess: true  // 显示添加标题成功提示
    });
    
    // 3秒后隐藏成功提示
    setTimeout(() => {
      this.setData({
        showTitleSuccess: false
      });
    }, 2000);
  },
  
  // 添加/清除图片标题 - 合并添加和清除功能
  toggleImageTitle: function() {
    const { imagePaths, imageTitles } = this.data;
    
    // 检查是否已有标题（至少有一个图片有标题）
    const hasAnyTitle = imageTitles.some(title => title && title.trim() !== '');
    
    if (hasAnyTitle) {
      // 如果已有标题，则清除所有标题
      const newImageTitles = imagePaths.map(() => '');
      
      this.setData({ 
        imageTitles: newImageTitles,
        hasImageTitles: false // 更新hasImageTitles状态
      });
    } else {
      // 如果没有标题，则添加默认标题
      const newImageTitles = imagePaths.map((_, index) => `图片标题${index + 1}`);
      
      this.setData({ 
        imageTitles: newImageTitles,
        hasImageTitles: true, // 更新hasImageTitles状态
        showTitleSuccess: true  // 显示添加标题成功提示
      });
      
      // 3秒后隐藏成功提示
      setTimeout(() => {
        this.setData({
          showTitleSuccess: false
        });
      }, 2000);
    }
  },
  
  // 添加预览标题 - 已禁用
  addPreviewTitle: function() {
    // 预览标题功能已禁用
    wx.showToast({
      title: '预览标题功能已禁用',
      icon: 'none',
      duration: 2000
    });
  },
  
  // 一键美化/取消美化功能
  // 一键美化 - 修改为只添加/清除美食名句
  beautifyImage: function() {
    // 调用切换美食名句的函数
    this.toggleFoodQuote();
  },
  
  // 切换美食名句 - 添加或清除美食名句
  toggleFoodQuote: function() {
    // 如果已经有美食名句，则清除
    if (this.data.foodQuote) {
      this.setData({
        foodQuote: ''
      });
      
      // 重新渲染Canvas以移除美食名句
      this.setData({
        isProcessing: true // 显示处理中遮罩
      }, () => {
        this.renderToCanvas().then((imagePath) => {
          // 隐藏处理中遮罩
          this.setData({
            isProcessing: false,
            composedImagePath: imagePath
          });
          
          wx.showToast({
            title: '已清除美食名句',
            icon: 'success'
          });
        }).catch(err => {
          console.error('清除美食名句失败:', err);
          this.setData({
            isProcessing: false
          });
          
          wx.showToast({
            title: '清除美食名句失败',
            icon: 'none'
          });
        });
      });
      
      return;
    }
    
    // 显示加载中提示
    wx.showLoading({
      title: '正在添加美食名句...',
      mask: true
    });
    
    // 准备美食名言
    const foodQuote = this.generateFoodQuote();
    
    // 立即更新数据，添加美食名句
    this.setData({
      foodQuote: foodQuote,
      isProcessing: true // 立即显示处理中遮罩
    });
    
    // 强制页面重新渲染，确保美食名句显示
    // 使用短暂延迟确保UI先更新，再进行Canvas渲染
    setTimeout(() => {
      // 重新渲染Canvas
      this.renderToCanvas().then((imagePath) => {
          // 检查是否成功获取到图片路径
          if (!imagePath) {
            console.error('添加美食名句失败: 未能获取合成图片路径');
            // 隐藏加载提示和处理中遮罩
            wx.hideLoading();
            this.setData({
              isProcessing: false // 隐藏处理中遮罩
            });
            wx.showToast({
              title: '添加美食名句失败',
              icon: 'none'
            });
            return;
          }
          // 隐藏加载提示
          wx.hideLoading();
          
          // 更新界面显示添加美食名句后的效果并隐藏处理中遮罩
          this.setData({
            composedImagePath: imagePath,
            isProcessing: false // 直接隐藏处理中遮罩
          });
          
          // 显示添加成功提示
          wx.showToast({
            title: '添加美食名句成功',
            icon: 'success'
          });
        }).catch(err => {
          console.error('添加美食名句失败:', err);
          // 隐藏加载提示和处理中遮罩
          wx.hideLoading();
          this.setData({
            isProcessing: false // 隐藏处理中遮罩
          });
          wx.showToast({
            title: '添加美食名句失败',
            icon: 'none'
          });
        });
    }, 100); // 短暂延迟确保UI更新
  },
  
  // 生成装饰元素
  generateDecorations: function() {
    // 可爱动物表情符号 - 用于外层大标题区域
    const animals = ['🐱', '🐶', '🐰', '🦊', '🐨', '🦁', '🐯', '🐹'];
    // 星星和其他装饰 - 用于其他区域
    const stars = ['✨', '⭐', '🌟', '💫', '🌈', '🎀', '🎵', '🎶', '💕', '💖'];
    
    const decorations = [];
    
    // 为外层大标题区域生成3-5个动物装饰元素
    const topAnimalsCount = Math.floor(Math.random() * 3) + 3;
    for (let i = 0; i < topAnimalsCount; i++) {
      const symbol = animals[Math.floor(Math.random() * animals.length)];
      
      // 位置设置在外层大标题区域
      const position = {
        x: Math.random(),  // 0-1之间的相对位置
        y: Math.random() * 0.6 + 0.2,  // 0.2-0.8之间的相对位置，使装饰元素分布在标题区域
        size: Math.floor(Math.random() * 10) + 25,  // 25-35px
        rotation: Math.random() * 30 - 15,  // -15到15度旋转
        area: 'title'  // 标记为标题区域
      };
      
      decorations.push({
        type: 'animal',
        symbol: symbol,
        position: position
      });
    }
    
    // 生成5-8个随机装饰元素用于其他区域
    const otherCount = Math.floor(Math.random() * 4) + 5;
    for (let i = 0; i < otherCount; i++) {
      // 随机选择装饰类型
      const type = Math.random() < 0.3 ? 'animal' : 'star';
      const symbols = type === 'animal' ? animals : stars;
      const symbol = symbols[Math.floor(Math.random() * symbols.length)];
      
      // 随机位置（确保在图片边框外）
      // 注意：具体位置会在渲染时根据画布尺寸计算
      const position = {
        x: Math.random(),  // 0-1之间的相对位置
        y: Math.random(),  // 0-1之间的相对位置
        size: Math.floor(Math.random() * 20) + 20,  // 20-40px
        rotation: Math.random() * 360,  // 0-360度旋转
        area: 'other'  // 标记为其他区域
      };
      
      decorations.push({
        type: type,
        symbol: symbol,
        position: position
      });
    }
    
    return decorations;
  },
  
  // 生成美食名言
  generateFoodQuote: function() {
    const quotes = [
      '人生如同火锅，不是所有的料都辣，但一定都很香！',
      '世界上最遥远的距离，不是生与死，而是我在减肥，你在吃小龙虾。',
      '饭前不要看美食视频，否则你会吃下两个人的饭量。',
      '我的减肥计划：第一天，看着美食流口水；第二天，流着口水吃美食。',
      '美食是世界上最好的安慰剂，没有之一。',
      '吃货的世界你不懂，我们不是胖，只是装满了幸福。',
      '生活不只有眼前的苟且，还有诗和远方的烤串。',
      '没有什么事情是一顿火锅解决不了的，如果有，那就两顿！',
      '我不是胖，我只是比别人更有福气。看，连肉都长得比别人多！',
      '人生就像一道菜，酸甜苦辣咸，样样都不能少。',
      '我对美食的热爱，就像对生活的热爱一样，永不言弃！',
      '与其担心未来，不如好好享受眼前的这盘红烧肉。',
      '美食当前，不食何待？人生苦短，及时行乐！',
      '我的字典里没有"饱"这个字，只有"再来一碗"。',
      '吃货的最高境界：看到美食时，眼睛比胃口还大。'
    ];
    
    return quotes[Math.floor(Math.random() * quotes.length)];
  },
  
  // 渲染到Canvas
  renderToCanvas: async function() {
    // 调用合成图片方法
    return await this.composeAndSaveImage();
  },
  
  // 清除所有图片标题
  clearImageTitle: function() {
    const { imagePaths } = this.data;
    
    // 清空所有标题
    const newImageTitles = imagePaths.map(() => '');
    
    this.setData({ 
      imageTitles: newImageTitles
    });
  },
  
  // 清除预览区标题 - 已禁用
  clearPreviewTitle: function() {
    // 预览标题功能已禁用
    wx.showToast({
      title: '预览标题功能已禁用',
      icon: 'none',
      duration: 2000
    });
  },
  
  // 设置默认主题
  setDefaultTheme: function() {
    // 只在需要时设置默认主题，不在初始化时自动设置
    this.setData({
      themeTitle: this.getDefaultTheme()
    });
  },
  
  // 添加/删除主题
  addTheme: function() {
    // 如果已有主题，则删除主题
    if (this.data.themeTitle) {
      this.setData({
        themeTitle: ''
      });
      
      wx.showToast({
        title: '已删除主题',
        icon: 'success'
      });
      return;
    }
    
    // 如果没有主题，直接设置默认主题
    this.setData({
      themeTitle: this.getDefaultTheme()
    });
    
    wx.showToast({
      title: '已添加主题',
      icon: 'success'
    });
    return;
    
    // 以下代码不再执行，保留以备将来需要
    /*
    // 准备预设主题选项
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    const day = now.getDate();
    const dateStr = `${year}.${month}.${day}`;
    
    const presetThemes = [
      `${dateStr} 早餐时光`,
      `${dateStr} 午餐记录`,
      `${dateStr} 下午茶`,
      `${dateStr} 晚餐分享`,
      `${dateStr} 美食记录`,
      `${dateStr} 今日美食`,
      `${dateStr} 美食日记`,
      `${dateStr} 食堂日常`
    ];
    
    // 先显示预设主题选择
    wx.showActionSheet({
      itemList: [...presetThemes, '自定义主题'],
      success: (res) => {
        if (res.tapIndex < presetThemes.length) {
          // 选择了预设主题
          this.setData({
            themeTitle: presetThemes[res.tapIndex]
          });
          
          wx.showToast({
            title: '主题已更新',
            icon: 'success'
          });
        } else {
          // 选择了自定义主题，显示自定义模态框
          this.setData({
            showThemeModal: true,
            themeTitle: this.data.themeTitle || this.getDefaultTheme()
          });
        }
      },
      fail: () => {
        // 操作取消，保持原主题
      }
    });
    */
  },
  
  // 主题模态框确认按钮事件已移除，使用微信原生模态框
  
  // 主题模态框取消按钮事件已移除，使用微信原生模态框
  
  // 编辑主题 - 点击主题标题时触发
  editTheme: function() {
    // 使用微信原生模态框编辑主题
    const currentTheme = this.data.themeTitle || this.getDefaultTheme();
    wx.showModal({
      title: '自定义主题',
      content: currentTheme,
      editable: true,
      success: (res) => {
        if (res.confirm) {
          // 用户点击确定，更新主题
          const newTheme = res.content.trim() || this.getDefaultTheme();
          this.setData({
            themeTitle: newTheme
          });
          
          wx.showToast({
            title: '主题已更新',
            icon: 'success'
          });
        }
        // 用户点击取消，不做任何操作
      }
    });
  },
  
  // 获取默认主题 - 更加个性化的主题
  getDefaultTheme: function() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    const day = now.getDate();
    const hour = now.getHours();
    
    // 根据时间段生成不同的主题
    let mealType = '套餐';
    // if (hour >= 5 && hour < 10) {
    //   mealType = '早餐时光';
    // } else if (hour >= 10 && hour < 14) {
    //   mealType = '午餐记录';
    // } else if (hour >= 14 && hour < 17) {
    //   mealType = '下午茶';
    // } else if (hour >= 17 && hour < 21) {
    //   mealType = '晚餐分享';
    // } else {
    //   mealType = '美食记录';
    // }
    
    return `${year}.${month}.${day} ${mealType}`;
  },
  
  // 图片加载错误处理
  onImageError: function(e) {
    const index = e.currentTarget.dataset.index;
    console.error(`图片${index}加载失败`);
    
    wx.showToast({
      title: `图片${index + 1}加载失败`,
      icon: 'none'
    });
  },
  
  // 合成并保存图片
  composeAndSaveImage: async function() {
    console.log('开始合成图片，图片数量:', this.data.imagePaths.length);

    // 确保Canvas已初始化
    if (!this.ctx || !this.canvas || !this.data.canvasInitialized) {
      console.log('Canvas未初始化，开始初始化');
      try {
        await this.initCanvas();
        // 额外等待确保Canvas完全准备好
        await new Promise(resolve => setTimeout(resolve, 300));
      } catch (error) {
        console.error('Canvas初始化失败:', error);
        wx.showToast({
          title: '画布初始化失败',
          icon: 'none'
        });
        return null;
      }

      if (!this.ctx || !this.canvas) {
        console.error('Canvas初始化后仍然无效');
        wx.showToast({
          title: '画布初始化失败',
          icon: 'none'
        });
        return null;
      }
    }

    const { imagePaths, imageTitles, themeTitle } = this.data;

    // 验证图片路径
    if (!imagePaths || imagePaths.length === 0) {
      console.error('没有图片路径');
      wx.showToast({
        title: '没有图片可合成',
        icon: 'none'
      });
      return null;
    }

    const ctx = this.ctx;
    const canvas = this.canvas;
    
    try {
      // 清空画布
      ctx.clearRect(0, 0, this.data.canvasWidth, this.data.canvasHeight);
      
      // 设置背景色
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, this.data.canvasWidth, this.data.canvasHeight);
      
      // 计算布局
      const padding = 20;
      const titleHeight = themeTitle ? 120 : 0; // 增加标题高度，避免压住文字
      const imageWidth = (this.data.canvasWidth - padding * 3) / 2; // 两列布局
      const imageHeight = imageWidth; // 保持1:1的宽高比，与CSS中的aspect-ratio: 1 / 1一致
      const imageSpacing = 20;
      const imageBorderRadius = 4; // 与CSS中的border-radius保持一致
      
      let currentY = padding;
      
      // 绘制主题标题
      if (themeTitle) {
        // 首先绘制外层蓝色渐变背景 - 与CSS中的background: linear-gradient(135deg, #3a7bd5, #00d2ff)保持一致
        const outerGradient = ctx.createLinearGradient(0, 0, this.data.canvasWidth, titleHeight);
        outerGradient.addColorStop(0, '#3a7bd5');
        outerGradient.addColorStop(1, '#00d2ff');
        ctx.fillStyle = outerGradient;
        ctx.fillRect(0, 0, this.data.canvasWidth, titleHeight);
        
        // 设置字体以便正确测量文本宽度
        ctx.font = 'bold 22px sans-serif';
        
        // 动态计算标题背景宽度，使其与标题文字长度一致
        const textWidth = ctx.measureText(themeTitle).width;
        // 使用文字宽度加上左右内边距
        const titlePadding = 40; // 文字两侧的内边距
        const titleBackgroundWidth = textWidth + titlePadding * 2;
        const titleBackgroundX = (this.data.canvasWidth - titleBackgroundWidth) / 2; // 水平居中
        
        // 动态计算标题背景高度，使其适配文字高度
        const titleFontSize = 22; // 标题字体大小
        const titleVerticalPadding = 15; // 文字上下的内边距
        const titleBackgroundHeight = titleFontSize + titleVerticalPadding * 2; // 根据文字大小和内边距计算背景高度
        const titleBackgroundY = currentY + (titleHeight - titleBackgroundHeight) / 2; // 垂直居中
        
        // 绘制内层标题背景 - 使用半透明白色背景，与CSS中的background-color: rgba(255, 255, 255, 0.2)保持一致
        // 调整内层背景的位置和大小，使其在外层背景中居中且有足够空间
        ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
        this.drawRoundedRect(ctx, titleBackgroundX, titleBackgroundY, titleBackgroundWidth, titleBackgroundHeight, 8);
        ctx.fill();
        
        // 绘制标题边框 - 使用更深的颜色
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.lineWidth = 1;
        this.drawRoundedRect(ctx, titleBackgroundX, titleBackgroundY, titleBackgroundWidth, titleBackgroundHeight, 8);
        ctx.stroke();
        
        // 绘制标题文字 - 使用白色文字增加对比度
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 22px sans-serif';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(themeTitle, this.data.canvasWidth / 2, currentY + titleHeight / 2); // 文字垂直居中
        
        // 添加装饰元素 - 左右两侧的小圆点
        const dotRadius = 3;
        const dotDistance = 10;
        
        // 左侧装饰
        ctx.fillStyle = '#ff7e5f';
        ctx.beginPath();
        ctx.arc(titleBackgroundX + titleBackgroundWidth/2 - ctx.measureText(themeTitle).width/2 - dotDistance, 
               currentY + titleHeight / 2, dotRadius, 0, Math.PI * 2); // 保持与文字同一高度
        ctx.fill();
        
        // 右侧装饰
        ctx.beginPath();
        ctx.arc(titleBackgroundX + titleBackgroundWidth/2 + ctx.measureText(themeTitle).width/2 + dotDistance, 
               currentY + titleHeight / 2, dotRadius, 0, Math.PI * 2); // 保持与文字同一高度
        ctx.fill();
        
        // 更新当前Y坐标
        currentY += titleHeight;
      }
      
      // 计算总高度
      const totalRows = Math.ceil(imagePaths.length / 2);
      // 使用currentY作为起始点，因为它已经包含了主题标题的高度
      const totalHeight = currentY + totalRows * imageHeight + (totalRows - 1) * imageSpacing + padding;
      
      // 调整Canvas高度
      const newHeight = Math.max(totalHeight, 800); // 确保最小高度
      if (canvas.height / this.dpr < newHeight) {
        canvas.height = newHeight * this.dpr;
        // 重新设置缩放以适应新的高度
        ctx.scale(this.dpr, this.dpr);
        console.log('调整Canvas高度为:', newHeight, '物理像素:', canvas.height);
      }
      
      // 更新数据中的画布高度
      this.setData({
        canvasHeight: newHeight
      });
      
      // 如果启用了美化功能，绘制一些装饰元素在背景上
      if (this.data.isBeautified && this.data.decorations && this.data.decorations.length > 0) {
        console.log('绘制装饰元素，数量:', this.data.decorations.length);
        
        // 绘制装饰元素（确保在图片边框外）
        this.data.decorations.forEach(decoration => {
          const { type, symbol, position } = decoration;
          
          // 计算实际位置
          let posX, posY;
          
          if (position.area === 'title' && themeTitle) {
            // 外层大标题区域 - 专门用于动物装饰
            if (type === 'animal') {
              // 计算动物在外层大标题区域的位置
              const titleHeight = 120; // 标题区域总高度
              const titleFontSize = 22; // 标题字体大小
              const titleVerticalPadding = 15; // 文字上下的内边距
              const titleBackgroundHeight = titleFontSize + titleVerticalPadding * 2; // 标题背景高度
              const titleBackgroundY = (titleHeight - titleBackgroundHeight) / 2; // 标题背景Y坐标
              const textWidth = ctx.measureText(themeTitle).width;
              const titlePadding = 40; // 文字两侧的内边距
              const titleBackgroundWidth = textWidth + titlePadding * 2;
              const titleBackgroundX = (this.data.canvasWidth - titleBackgroundWidth) / 2; // 标题背景X坐标
              
              // 在外层大标题区域随机分布动物，但避开内层标题背景区域
              // 使用与图片区域相同的宽度，并考虑padding
              let posX = padding + position.x * (this.data.canvasWidth - padding * 2); // 在整个标题宽度范围内随机分布
              let posY = position.y * titleHeight; // 在标题高度范围内随机分布
              
              // 检查是否与内层标题背景区域重叠
              const animalSize = position.size / 2; // 动物装饰元素的半径（近似值）
              const isOverlapping = 
                posX >= titleBackgroundX - animalSize && 
                posX <= titleBackgroundX + titleBackgroundWidth + animalSize &&
                posY >= titleBackgroundY - animalSize && 
                posY <= titleBackgroundY + titleBackgroundHeight + animalSize;
              
              // 如果重叠，则调整位置
              if (isOverlapping) {
                // 随机选择一个不重叠的区域（上、下、左、右）
                const area = Math.floor(Math.random() * 4);
                
                switch(area) {
                  case 0: // 上方区域
                    posY = Math.max(animalSize, titleBackgroundY - animalSize - 5);
                    break;
                  case 1: // 右侧区域
                    posX = titleBackgroundX + titleBackgroundWidth + animalSize + 5;
                    break;
                  case 2: // 下方区域
                    posY = titleBackgroundY + titleBackgroundHeight + animalSize + 5;
                    break;
                  case 3: // 左侧区域
                    posX = Math.max(padding, titleBackgroundX - animalSize - 5);
                    break;
                }
              }
            }
          } else {
            // 其他区域的装饰元素
            // 随机选择位置区域（上、下、左、右边框外）
            const area = Math.floor(position.x * 4); // 0-3分别代表上、右、下、左四个区域
            
            switch(area) {
              case 0: // 上方区域
                posX = padding + position.x * (this.data.canvasWidth - padding * 2);
                posY = padding * 4 + position.y * padding * 2; // 避开上方区域
                break;
              case 1: // 右侧区域
                posX = this.data.canvasWidth - padding * (0.5 + position.y * 0.5); // 右侧边距区域
                posY = currentY + position.y * (this.data.canvasHeight - currentY - padding);
                break;
              case 2: // 下方区域
                posX = padding + position.x * (this.data.canvasWidth - padding * 2);
                posY = this.data.canvasHeight - padding * (0.5 + position.y * 0.5); // 下方边距区域
                break;
              case 3: // 左侧区域
                posX = padding * (0.5 + position.y * 0.5); // 左侧边距区域
                posY = currentY + position.y * (this.data.canvasHeight - currentY - padding);
                break;
            }
          }
          
          // 绘制装饰元素
          ctx.save();
          ctx.translate(posX, posY);
          ctx.rotate(position.rotation * Math.PI / 180);
          
          ctx.font = `${position.size}px sans-serif`;
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText(symbol, 0, 0);
          
          ctx.restore();
        });
      }
      
      // 加载并绘制图片 - 增强错误处理
      console.log('开始绘制图片，总数:', imagePaths.length);

      for (let i = 0; i < imagePaths.length; i++) {
        console.log(`处理第${i + 1}张图片:`, imagePaths[i]);

        const row = Math.floor(i / 2);
        const col = i % 2;

        const x = padding + col * (imageWidth + padding);
        const y = currentY + row * (imageHeight + imageSpacing);

        try {
          // 验证图片路径
          if (!imagePaths[i] || typeof imagePaths[i] !== 'string') {
            throw new Error(`图片路径无效: ${imagePaths[i]}`);
          }

          // 加载图片，增加重试机制
          let image = null;
          let retryCount = 0;
          const maxRetries = 3;

          while (retryCount < maxRetries && !image) {
            try {
              image = await this.loadImage(canvas, imagePaths[i]);
              console.log(`图片${i + 1}加载成功，尺寸:`, image.width, 'x', image.height);
              break;
            } catch (loadError) {
              retryCount++;
              console.warn(`图片${i + 1}加载失败，重试${retryCount}/${maxRetries}:`, loadError);
              if (retryCount < maxRetries) {
                await new Promise(resolve => setTimeout(resolve, 500)); // 等待500ms后重试
              }
            }
          }

          if (!image) {
            throw new Error(`图片${i + 1}加载失败，已重试${maxRetries}次`);
          }

          // 计算填充模式的绘制尺寸和位置
          const drawSize = this.calculateAspectFillSize(
            image.width,
            image.height,
            imageWidth,
            imageHeight
          );

          // 居中绘制图片（填充模式需要计算裁剪位置）
          const centerX = x + (imageWidth - drawSize.width) / 2;
          const centerY = y + (imageHeight - drawSize.height) / 2;

          // 绘制图片边框和背景（带圆角）
          ctx.fillStyle = '#f8f8f8';
          this.drawRoundedRect(ctx, x, y, imageWidth, imageHeight, imageBorderRadius);
          ctx.fill();

          // 绘制图片（带圆角裁剪）
          ctx.save();
          this.drawRoundedRect(ctx, x, y, imageWidth, imageHeight, imageBorderRadius);
          ctx.clip();

          // 绘制图片（填充模式）
          console.log(`绘制图片${i + 1}，位置:`, centerX, centerY, '尺寸:', drawSize.width, drawSize.height);
          ctx.drawImage(
            image,
            centerX,
            centerY,
            drawSize.width,
            drawSize.height
          );

          ctx.restore();
          
          // 绘制图片标题
          if (imageTitles[i]) {
            ctx.fillStyle = '#ff6b81';
            ctx.font = '16px sans-serif';
            const titleWidth = Math.min(ctx.measureText(imageTitles[i]).width + 20, imageWidth * 0.8);
            ctx.fillRect(x, y, titleWidth, 30);
            
            ctx.fillStyle = '#ffffff';
            ctx.font = '16px sans-serif';
            ctx.textAlign = 'left';
            ctx.textBaseline = 'middle';
            ctx.fillText(imageTitles[i], x + 10, y + 15);
          }
          
          // 绘制图片序号
          ctx.fillStyle = 'rgba(0, 0, 0, 0.6)';
          ctx.beginPath();
          ctx.arc(x + imageWidth - 20, y + imageHeight - 20, 15, 0, Math.PI * 2);
          ctx.fill();
          
          ctx.fillStyle = '#ffffff';
          ctx.font = '14px sans-serif';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText((i + 1).toString(), x + imageWidth - 20, y + imageHeight - 20);
        } catch (error) {
          console.error(`加载或绘制图片${i + 1}失败:`, error);

          // 绘制错误占位符，确保不影响整体布局
          ctx.fillStyle = '#f5f5f5';
          this.drawRoundedRect(ctx, x, y, imageWidth, imageHeight, imageBorderRadius);
          ctx.fill();

          // 绘制边框
          ctx.strokeStyle = '#ddd';
          ctx.lineWidth = 1;
          this.drawRoundedRect(ctx, x, y, imageWidth, imageHeight, imageBorderRadius);
          ctx.stroke();

          // 绘制错误图标和文字
          ctx.fillStyle = '#999';
          ctx.font = '14px sans-serif';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText('图片加载失败', x + imageWidth / 2, y + imageHeight / 2 - 10);
          ctx.fillText(`(${i + 1}/${imagePaths.length})`, x + imageWidth / 2, y + imageHeight / 2 + 10);

          // 继续处理下一张图片，不中断整个流程
        }
      }
      
      // 如果有美食名言，绘制在最后一张图片下方
      if (this.data.foodQuote) {
        console.log('绘制美食名言:', this.data.foodQuote);
        
        // 计算美食名言的位置（在所有图片下方）
        const lastRow = Math.floor((imagePaths.length - 1) / 2);
        const quoteY = currentY + (lastRow + 1) * (imageHeight + imageSpacing) + padding;
        
        // 绘制美食名言背景
        const quoteWidth = this.data.canvasWidth - padding * 2;
        const quoteHeight = 120; // 增加高度使其更加大方
        const quoteX = padding;
        const borderRadius = 25; // 增大圆角半径
        
        // 创建更有趣的渐变背景 - 使用更鲜艳的颜色
        const gradient = ctx.createLinearGradient(quoteX, quoteY, quoteX + quoteWidth, quoteY + quoteHeight);
        gradient.addColorStop(0, 'rgba(255, 183, 77, 0.2)'); // 橙黄色
        gradient.addColorStop(0.5, 'rgba(255, 218, 121, 0.25)'); // 淡黄色
        gradient.addColorStop(1, 'rgba(255, 152, 0, 0.2)'); // 橙色
        
        // 绘制美食名言背景
        ctx.fillStyle = gradient;
        this.drawRoundedRect(ctx, quoteX, quoteY, quoteWidth, quoteHeight, borderRadius);
        ctx.fill();
        
        // 绘制有趣的波浪边框
        ctx.save();
        ctx.strokeStyle = '#ff9800';
        ctx.lineWidth = 3;
        ctx.setLineDash([8, 4]); // 虚线边框
        this.drawRoundedRect(ctx, quoteX, quoteY, quoteWidth, quoteHeight, borderRadius);
        ctx.stroke();
        ctx.restore();
        
        // 添加装饰元素 - 食物表情符号
        const foodEmojis = ['🍔', '🍕', '🍦', '🍰', '🍜', '🍣', '🍩', '🍗', '🥗', '🍓'];
        const emoji1 = foodEmojis[Math.floor(Math.random() * foodEmojis.length)];
        const emoji2 = foodEmojis[Math.floor(Math.random() * foodEmojis.length)];
        
        // 绘制食物表情符号
        ctx.font = '32px sans-serif';
        ctx.textAlign = 'left';
        ctx.textBaseline = 'top';
        ctx.fillText(emoji1, quoteX + 20, quoteY + 15);
        
        ctx.textAlign = 'right';
        ctx.textBaseline = 'bottom';
        ctx.fillText(emoji2, quoteX + quoteWidth - 20, quoteY + quoteHeight - 15);
        
        // 绘制引号装饰
        ctx.fillStyle = '#ff7e5f';
        ctx.font = '36px sans-serif'; // 增大引号字体
        ctx.textAlign = 'left';
        ctx.textBaseline = 'top';
        ctx.fillText('❝', quoteX + 60, quoteY + 15);
        
        ctx.textAlign = 'right';
        ctx.textBaseline = 'bottom';
        ctx.fillText('❞', quoteX + quoteWidth - 60, quoteY + quoteHeight - 15);
        
        // 绘制美食名言文本 - 使用更有趣的颜色
        // 创建文字渐变色
        const textGradient = ctx.createLinearGradient(quoteX, quoteY + quoteHeight/2, quoteX + quoteWidth, quoteY + quoteHeight/2);
        textGradient.addColorStop(0, '#d84315'); // 深橙色
        textGradient.addColorStop(0.5, '#bf360c'); // 红橙色
        textGradient.addColorStop(1, '#d84315'); // 深橙色
        
        ctx.fillStyle = textGradient;
        ctx.font = 'bold 22px sans-serif'; // 增大字号
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(this.data.foodQuote, quoteX + quoteWidth / 2, quoteY + quoteHeight / 2);
        
        // 更新Canvas高度以包含美食名言
        this.setData({
          canvasHeight: quoteY + quoteHeight + padding
        });
      }
      
      // 导出图片
      return await new Promise((resolve, reject) => {
        console.log('准备导出Canvas图片，尺寸:', this.data.canvasWidth, 'x', this.data.canvasHeight, '设备像素比:', this.dpr);
        
        // 确保Canvas尺寸正确
        if (canvas.width !== this.data.canvasWidth * this.dpr || canvas.height !== this.data.canvasHeight * this.dpr) {
          console.log('调整Canvas物理尺寸:', this.data.canvasWidth * this.dpr, 'x', this.data.canvasHeight * this.dpr);
          canvas.width = this.data.canvasWidth * this.dpr;
          canvas.height = this.data.canvasHeight * this.dpr;
          ctx.scale(this.dpr, this.dpr);
        }
        
        wx.canvasToTempFilePath({
          canvas: canvas,
          width: this.data.canvasWidth,
          height: this.data.canvasHeight,
          destWidth: this.data.canvasWidth * this.dpr,
          destHeight: this.data.canvasHeight * this.dpr,
          fileType: 'jpg',
          quality: 0.9,
          success: (res) => {
            console.log('合成图片成功:', res.tempFilePath);
            // 保存图片尺寸信息，用于分享预览
            this.setData({
              composedImagePath: res.tempFilePath,
              composedImageWidth: this.data.canvasWidth,
              composedImageHeight: this.data.canvasHeight
            });
            resolve(res.tempFilePath);
          },
          fail: (err) => {
            console.error('合成图片失败:', err);
            reject(err);
          }
        });
      });
      
    } catch (error) {
      console.error('绘制图片失败:', error);
      return null;
    }
  },
  
  // 计算保持宽高比的尺寸 (aspectFit模式)
  calculateAspectFitSize: function(srcWidth, srcHeight, maxWidth, maxHeight) {
    const srcRatio = srcWidth / srcHeight;
    const maxRatio = maxWidth / maxHeight;
    
    let width, height;
    
    if (srcRatio > maxRatio) {
      // 图片比容器更宽，以宽度为准
      width = maxWidth;
      height = width / srcRatio;
    } else {
      // 图片比容器更高，以高度为准
      height = maxHeight;
      width = height * srcRatio;
    }
    
    return { width, height };
  },
  
  // 计算填充模式的尺寸 (aspectFill模式)
  calculateAspectFillSize: function(srcWidth, srcHeight, maxWidth, maxHeight) {
    console.log('计算填充模式尺寸，源尺寸:', srcWidth, 'x', srcHeight, '目标尺寸:', maxWidth, 'x', maxHeight);
    const srcRatio = srcWidth / srcHeight;
    const maxRatio = maxWidth / maxHeight;
    
    let width, height;
    
    if (srcRatio > maxRatio) {
      // 图片比容器更宽，以高度为准
      height = maxHeight;
      width = height * srcRatio;
    } else {
      // 图片比容器更高，以宽度为准
      width = maxWidth;
      height = width / srcRatio;
    }
    
    console.log('计算结果:', width, 'x', height);
    return { width, height };
  },
  
  // 绘制圆角矩形的辅助方法
  drawRoundedRect: function(ctx, x, y, width, height, radius) {
    if (width < 2 * radius) radius = width / 2;
    if (height < 2 * radius) radius = height / 2;
    
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.arcTo(x + width, y, x + width, y + height, radius);
    ctx.arcTo(x + width, y + height, x, y + height, radius);
    ctx.arcTo(x, y + height, x, y, radius);
    ctx.arcTo(x, y, x + width, y, radius);
    ctx.closePath();
  },
  
  // 加载单张图片的辅助方法
  loadImage: function(canvas, src) {
    return new Promise((resolve, reject) => {
      try {
        console.log('开始加载图片:', src.substring(0, 50) + '...');
        
        // 检查图片路径是否有效
        if (!src || typeof src !== 'string' || src.trim() === '') {
          console.error('图片路径无效');
          reject(new Error('图片路径无效'));
          return;
        }
        
        const image = canvas.createImage();
        
        // 设置超时处理
        let isResolved = false;
        const timeout = setTimeout(() => {
          if (!isResolved) {
            console.error('图片加载超时');
            reject(new Error('图片加载超时'));
          }
        }, 10000); // 10秒超时
        
        image.onload = () => {
          clearTimeout(timeout);
          isResolved = true;
          console.log('图片加载成功，尺寸:', image.width, 'x', image.height);
          resolve(image);
        };
        
        image.onerror = (err) => {
          clearTimeout(timeout);
          isResolved = true;
          console.error('图片加载失败:', err);
          reject(new Error('图片加载失败'));
        };
        
        // 处理不同类型的图片路径
        if (src.startsWith('http://tmp/') || src.startsWith('https://tmp/')) {
          // 开发工具中的临时文件，直接使用原始路径
          console.log('检测到开发工具临时文件路径，直接加载:', src);
          image.src = src;
        } else if (src.startsWith('http://') || src.startsWith('https://')) {
          // 真正的网络图片，需要下载到本地
          console.log('检测到网络图片，开始下载:', src);
          wx.downloadFile({
            url: src,
            success: (res) => {
              if (res.statusCode === 200) {
                console.log('网络图片下载成功:', res.tempFilePath);
                image.src = res.tempFilePath;
              } else {
                console.error('下载图片失败，状态码:', res.statusCode);
                reject(new Error('下载图片失败，状态码: ' + res.statusCode));
              }
            },
            fail: (err) => {
              console.error('下载图片失败:', err);
              reject(new Error('下载图片失败: ' + (err.errMsg || '未知错误')));
            }
          });
        } else {
          // 本地图片直接加载
          console.log('检测到本地图片路径，直接加载:', src);
          image.src = src;
        }
      } catch (error) {
        console.error('创建图片对象失败:', error);
        reject(error);
      }
    });
  },
  
  // 保存编辑结果
  saveMultiEdit: async function() {
    // 显示处理中状态
    this.setData({
      isProcessing: true
    });
    
    try {
      // 确保Canvas已初始化
      if (!this.data.canvasInitialized) {
        await new Promise(resolve => {
          this.initCanvas();
          setTimeout(resolve, 500); // 等待Canvas初始化
        });
      }
      
      // 合成图片
      const composedImagePath = await this.composeAndSaveImage();
      
      if (!composedImagePath) {
        throw new Error('图片合成失败');
      }
      
      // 保存编辑后的图片到相册（注意：原图已在拍照时保存，这里保存的是编辑后的合成图片）
      await new Promise((resolve, reject) => {
        wx.saveImageToPhotosAlbum({
          filePath: composedImagePath,
          success: (res) => {
            console.log('编辑后的图片保存成功:', res);
            resolve(res);
          },
          fail: (err) => {
            console.error('编辑后的图片保存失败:', err);
            
            // 如果是因为权限问题失败，则引导用户授权
            if (err.errMsg.indexOf('auth deny') >= 0 || err.errMsg.indexOf('authorize') >= 0) {
              wx.showModal({
                title: '提示',
                content: '需要您授权保存编辑后的图片到相册',
                confirmText: '去授权',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    wx.openSetting({
                      success: (settingRes) => {
                        if (settingRes.authSetting['scope.writePhotosAlbum']) {
                          wx.showToast({
                            title: '授权成功，请重新保存',
                            icon: 'none'
                          });
                        }
                      }
                    });
                  }
                }
              });
            }
            
            reject(err);
          }
        });
      });
      
      // 返回编辑后的图片路径数组、图片标题数组和主题标题
      const eventChannel = this.getOpenerEventChannel();
      if (eventChannel && eventChannel.emit) {
        eventChannel.emit('acceptMultiEditResult', {
          composedImagePath: composedImagePath,
          paths: this.data.imagePaths,
          imageTitles: this.data.imageTitles,
          previewTitle: this.data.previewTitle,
          themeTitle: this.data.themeTitle
        });
      }
      
      // 显示成功提示
      wx.showToast({
        title: '编辑后的图片已保存',
        icon: 'success',
        duration: 1500,
        success: () => {
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      });
      
    } catch (error) {
      console.error('保存失败:', error);
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    } finally {
      // 隐藏处理中状态
      this.setData({
        isProcessing: false
      });
    }
  },
  
  // 取消编辑
  cancelEdit: function() {
    wx.showModal({
      title: '提示',
      content: '确定要放弃当前编辑吗？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },
  
  // 分享到微信好友和朋友圈
  shareToTimeline: async function() {
    console.log('开始分享流程，图片数量:', this.data.imagePaths.length);

    try {
      // 显示处理中状态
      this.setData({
        isProcessing: true
      });

      wx.showLoading({ title: '正在生成分享图片...' });

      // 验证图片数据
      if (!this.data.imagePaths || this.data.imagePaths.length === 0) {
        throw new Error('没有图片可分享');
      }

      // 确保Canvas已初始化
      if (!this.data.canvasInitialized || !this.ctx || !this.canvas) {
        console.log('Canvas未初始化，开始初始化');
        try {
          await this.initCanvas();
          // 额外等待确保Canvas完全准备好
          await new Promise(resolve => setTimeout(resolve, 500));
        } catch (initError) {
          throw new Error('Canvas初始化失败: ' + initError.message);
        }
      }

      // 合成图片
      console.log('开始合成图片');
      const composedImagePath = await this.composeAndSaveImage();

      if (!composedImagePath) {
        throw new Error('图片合成失败');
      }

      console.log('图片合成成功，路径:', composedImagePath);

      // 验证合成的图片是否有效
      try {
        const fileInfo = await new Promise((resolve, reject) => {
          wx.getFileInfo({
            filePath: composedImagePath,
            success: resolve,
            fail: reject
          });
        });

        if (fileInfo.size <= 0) {
          throw new Error('合成的图片文件无效');
        }

        console.log('合成图片文件大小:', Math.round(fileInfo.size / 1024), 'KB');
      } catch (fileError) {
        throw new Error('合成图片验证失败: ' + fileError.message);
      }

      wx.hideLoading();

      // 调起分享
      wx.showShareImageMenu({
        path: composedImagePath,
        success: () => {
          wx.showToast({ title: '已调起分享', icon: 'success' });
          // 记录分享成功状态
          this.setData({
            lastSharedImage: composedImagePath
          });
          console.log('分享成功');
        },
        fail: (err) => {
          console.error('分享失败:', err);
          wx.showToast({ title: '分享失败: ' + (err.errMsg || '未知错误'), icon: 'none' });
        }
      });

    } catch (error) {
      console.error('分享流程失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: error.message || '分享准备失败',
        icon: 'none',
        duration: 3000
      });

      // 清除相关状态
      this.setData({
        lastSharedImage: ''
      });
    } finally {
      // 隐藏处理中状态
      this.setData({
        isProcessing: false
      });
    }
  },
  
  // 支持右上角转发到好友
  onShareAppMessage: function() {
    // 获取全局用户信息
    const app = getApp();
    const nickName = (app.globalData.userInfo && app.globalData.userInfo.nickName) || '用户';
    
    // 尝试获取已合成的图片路径
    const imagePath = this.data.lastSharedImage || (this.data.imagePaths && this.data.imagePaths.length > 0 ? this.data.imagePaths[0] : '');
    
    return {
      title: `${nickName}的图片作品`,
      path: '/pages/index/index',
      imageUrl: imagePath
    };
  },
  
  // 支持右上角转发到朋友圈
  onShareTimeline: function() {
    // 尝试获取已合成的图片路径
    const imagePath = this.data.lastSharedImage || (this.data.imagePaths && this.data.imagePaths.length > 0 ? this.data.imagePaths[0] : '');
    
    return {
      title: '图片助手-分享我的图片',
      imageUrl: imagePath
    };
  }
})