<!-- 多图编辑页面 -->
<view class="container">
  <!-- 顶部导航栏已隐藏 -->
  
  <!-- 预览区域 -->
  <view class="preview-container">
    <!-- 主题标题 -->
    <view class="theme-title-container" wx:if="{{themeTitle}}" bindtap="editTheme">
      <view class="theme-title">{{themeTitle}}</view>
    </view>
    
    <!-- 图片加载失败提示 -->
    <view class="empty-state" wx:if="{{imagePaths.length === 0}}">
      <text class="empty-text">图片加载失败</text>
      <text class="empty-desc">请返回重新选择图片</text>

      <!-- 调试信息（仅在开发环境显示） -->
      <view class="debug-info" wx:if="{{debugInfo.show}}">
        <text class="debug-title">调试信息:</text>
        <text class="debug-item">平台: {{debugInfo.platform}}</text>
        <text class="debug-item">版本: {{debugInfo.version}}</text>
        <text class="debug-item">系统: {{debugInfo.system}}</text>
        <text class="debug-item">像素比: {{debugInfo.pixelRatio}}</text>
        <text class="debug-item">接收到的路径数量: {{debugInfo.pathCount}}</text>
        <view class="debug-paths" wx:if="{{debugInfo.paths.length > 0}}">
          <text class="debug-subtitle">路径详情:</text>
          <text class="debug-path" wx:for="{{debugInfo.paths}}" wx:key="index" wx:for-item="path">
            {{index + 1}}. {{path}}
          </text>
        </view>
      </view>
    </view>

    <!-- 图片网格 - 使用两列布局 -->
    <scroll-view class="image-grid-container" scroll-y="true" enhanced="true" show-scrollbar="true" bounces="true" fast-deceleration="true" wx:if="{{imagePaths.length > 0}}">
      <view class="image-grid">
        <view wx:for="{{imagePaths}}" wx:key="index" class="image-item">
          <!-- 图片 -->
          <image src="{{item}}" mode="aspectFill" class="grid-image" 
                 data-index="{{index}}" 
                 bindtap="selectImage" 
                 binderror="onImageError"
                 lazy-load="true"></image>
          
          <!-- 图片标题 - 只在有标题时显示 -->
          <view class="image-title {{selectedImageIndex === index ? 'editing' : ''}}" 
                wx:if="{{imageTitles[index]}}" 
                bindtap="editImageTitle" 
                data-index="{{index}}">
            {{imageTitles[index]}}
          </view>
          
          <!-- 图片序号 -->
          <view class="image-number">{{index + 1}}</view>
        </view>
      </view>
      
      <!-- 美食名句 - 只在有美食名句时显示 -->
      <view class="food-quote-container" wx:if="{{foodQuote}}">
        <view class="food-quote">{{foodQuote}}</view>
      </view>
    </scroll-view>
    
    <!-- 添加标题成功提示 -->
    <view class="title-success-toast" wx:if="{{showTitleSuccess}}">
      <view class="success-icon">✓</view>
      <view class="success-text">已添加默认标题</view>
    </view>
  </view>
  
  <!-- 底部工具区域 - 简洁设计 -->
  <view class="bottom-tools-new">
    <!-- 编辑模式选择器 - 仅保留图片标题 -->
    <view class="edit-mode-tabs">
      <view class="tab-item {{showToolButtons ? 'active' : ''}}" 
            data-mode="imageTitle" 
            bindtap="changeEditorMode">
        <view class="tab-icon image-title-icon"></view>
        <text class="tab-text">工具箱</text>
      </view>
    </view>
    
    <!-- 操作按钮区域 - 只在showToolButtons为true时显示 -->
    <view class="action-area" wx:if="{{showToolButtons}}">
      <!-- 操作按钮 - 简洁设计 -->
      <view class="tool-buttons">
        <view class="tool-btn" bindtap="addTheme">
          <view class="tool-icon theme-icon"></view>
          <text class="tool-text">{{themeTitle ? '删除主题' : '添加主题'}}</text>
        </view>
        <view class="tool-btn" bindtap="toggleImageTitle">
          <view class="tool-icon {{hasImageTitles ? 'clear-icon' : 'add-icon'}}"></view>
          <text class="tool-text">{{hasImageTitles ? '清除图片标题' : '添加图片标题'}}</text>
        </view>
        <view class="tool-btn" bindtap="beautifyImage">
          <view class="tool-icon {{foodQuote ? 'cancel-beautify-icon' : 'beautify-icon'}}"></view>
          <text class="tool-text">{{foodQuote ? '清除美食名句' : '添加美食名句'}}</text>
        </view>
      </view>
    </view>
      
    <!-- 分隔线 -->
    <view class="divider"></view>
      
    <!-- 底部按钮组 -->
    <view class="bottom-buttons">
      <button class="share-btn" bindtap="shareToTimeline">分享到朋友圈</button>
      <button class="save-btn" bindtap="saveMultiEdit">保存</button>
    </view>
  </view>
  
  <!-- 处理中遮罩 -->
  <view class="processing-mask" wx:if="{{isProcessing}}">
    <view class="processing-content">
      <view class="loading-spinner"></view>
      <text class="processing-text">处理中...</text>
    </view>
  </view>
  
  <!-- 用于合成图片的Canvas，设置为不可见但确保有尺寸 -->
  <canvas type="2d" id="compositeCanvas" style="width:{{canvasWidth}}px; height:{{canvasHeight}}px; position:fixed; left:-9999px; top:0; visibility:hidden;"></canvas>
</view>