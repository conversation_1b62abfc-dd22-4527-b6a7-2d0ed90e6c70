/* 多图编辑页面样式 */

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

/* 调试信息样式 */
.debug-info {
  background: #f5f5f5;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-top: 40rpx;
  text-align: left;
  max-width: 600rpx;
  border: 2rpx solid #e0e0e0;
}

.debug-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.debug-item {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
  font-family: monospace;
}

.debug-subtitle {
  font-size: 26rpx;
  font-weight: bold;
  color: #444;
  margin: 20rpx 0 10rpx 0;
  display: block;
}

.debug-paths {
  margin-top: 20rpx;
}

.debug-path {
  font-size: 22rpx;
  color: #555;
  margin-bottom: 8rpx;
  display: block;
  font-family: monospace;
  word-break: break-all;
  background: #fff;
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
  border: 1rpx solid #ddd;
}

/* 容器样式 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}

/* 顶部导航栏样式 - 已隐藏 */
.nav-bar {
  display: none;
}

/* 预览容器样式 */
.preview-container {
  flex: 1;
  overflow: hidden;
  position: relative;
  background-color: white;
  margin: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

/* 主题标题容器 */
.theme-title-container {
  display: flex;
  justify-content: center;
  width: 100%;
  padding: 15px 0;
  background: linear-gradient(135deg, #3a7bd5, #00d2ff);
  border-bottom: 1px solid #e0e0e0;
}

/* 主题标题样式 */
.theme-title {
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
  padding: 10px 20px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  /* 修改为自适应宽度 */
  width: auto;
  min-width: 30%;
  max-width: 90%;
  display: inline-block;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  letter-spacing: 1px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 图片网格容器 */
.image-grid-container {
  height: 100%;
  width: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}

/* 图片网格布局 */
.image-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 10px;
}

/* 图片项目样式 */
.image-item {
  width: calc(50% - 10px);
  margin: 5px;
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  background-color: #f0f0f0;
  display: flex;
  flex-direction: column;
}

/* 网格中的图片样式 */
.grid-image {
  width: 100%;
  object-fit: cover;
  background-color: #f0f0f0;
  border-radius: 4px;
  transition: opacity 0.3s ease;
  aspect-ratio: 1 / 1; /* 固定宽高比为1:1，使图片显示为正方形 */
}

/* 图片标题样式 */
.image-title {
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(255, 126, 95, 0.7);
  color: white;
  padding: 4px 8px;
  font-size: 12px;
  max-width: 90%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  border: 1px solid rgba(255, 255, 255, 0.5);
}

/* 图片标题编辑状态 */
.image-title.editing {
  border: 1px dashed white;
}

/* 图片序号样式 */
.image-number {
  position: absolute;
  bottom: 5px;
  right: 5px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
}

/* 美食名句容器样式 */
.food-quote-container {
  width: calc(100% - 40px);
  margin: 10px 20px;
  padding: 15px;
  background: linear-gradient(135deg, rgba(255, 183, 77, 0.2), rgba(255, 152, 0, 0.2));
  border-radius: 15px;
  border: 2px dashed #ff9800;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

/* 美食名句样式 */
.food-quote {
  font-size: 16px;
  color: #d84315;
  text-align: center;
  font-weight: bold;
  padding: 10px;
  position: relative;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  max-width: 100%;
}

/* 美食名句前后引号装饰 */
.food-quote::before,
.food-quote::after {
  content: '❝';
  font-size: 24px;
  color: #ff7e5f;
  position: absolute;
  opacity: 0.6;
}

.food-quote::before {
  top: -10px;
  left: 10px;
}

.food-quote::after {
  content: '❞';
  bottom: -10px;
  right: 10px;
}

/* 底部工具区域 */
.bottom-tools-new {
  background: white;
  border-top: 1px solid #e0e0e0;
  padding: 10px 15px 20px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

/* 编辑模式选项卡 */
.edit-mode-tabs {
  display: flex;
  margin-bottom: 15px;
  background-color: #f0f0f0;
  border-radius: 8px;
  padding: 3px;
}

/* 选项卡项目 */
.tab-item {
  flex: 1;
  text-align: center;
  padding: 8px 0;
  border-radius: 6px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #666;
  transition: all 0.3s;
  position: relative;
}

/* 激活的选项卡 */
.tab-item.active {
  background-color: #fff;
  color: #ff7e5f;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 添加一个小箭头指示展开/收起状态 */
.tab-item::after {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-right: 2px solid #666;
  border-bottom: 2px solid #666;
  transform: rotate(45deg);
  margin-left: 8px;
  transition: transform 0.3s;
}

.tab-item.active::after {
  border-color: white;
  transform: rotate(-135deg);
}

/* 选项卡图标 */
.tab-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 工具箱图标 */
.image-title-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23ff7e5f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='2' y='7' width='20' height='14' rx='2' ry='2'/%3E%3Cpath d='M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16'/%3E%3C/svg%3E");
}

/* 预览标题图标 */
.preview-title-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M4 6h16'/%3E%3Cpath d='M4 12h16'/%3E%3Cpath d='M4 18h12'/%3E%3C/svg%3E");
}

/* 选项卡文本 */
.tab-text {
  font-size: 13px;
}

/* 操作区域 */
.action-area {
  padding-top: 5px;
  animation: slideDown 0.3s ease-out;
  transform-origin: top center;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: scaleY(0);
  }
  to {
    opacity: 1;
    transform: scaleY(1);
  }
}

/* 工具按钮 - 小巧设计 */
.tool-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 15px;
}

.tool-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 15px;
  background-color: #f8f8f8;
  border-radius: 20px;
  border: 1px solid #e0e0e0;
  transition: all 0.2s;
}

.tool-btn:active {
  background-color: #f0f0f0;
  transform: scale(0.98);
}

.tool-icon {
  width: 20px;
  height: 20px;
  margin-bottom: 4px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.tool-text {
  font-size: 12px;
  color: #333;
}

/* 主题图标 */
.theme-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24' fill='none' stroke='%23ff7e5f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M4 3h16a2 2 0 0 1 2 2v6a10 10 0 0 1-10 10A10 10 0 0 1 2 11V5a2 2 0 0 1 2-2z'%3E%3C/path%3E%3Cpath d='M8 10h.01'%3E%3C/path%3E%3Cpath d='M12 10h.01'%3E%3C/path%3E%3Cpath d='M16 10h.01'%3E%3C/path%3E%3C/svg%3E");
}

/* 添加按钮 */
.add-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24' fill='none' stroke='%23ff7e5f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='12' y1='5' x2='12' y2='19'%3E%3C/line%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
}

/* 清除按钮 */
.clear-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='3 6 5 6 21 6'%3E%3C/polyline%3E%3Cpath d='M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2'%3E%3C/path%3E%3C/svg%3E");
}

/* 美化按钮 */
.beautify-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24' fill='none' stroke='%23ff7e5f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolygon points='12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2'%3E%3C/polygon%3E%3C/svg%3E");
}

/* 取消美化按钮 */
.cancel-beautify-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
}

/* 分隔线 */
.divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 10px 0;
}

/* 底部按钮组 */
.bottom-buttons {
  display: flex;
  justify-content: space-between;
  gap: 15px;
}

/* 分享按钮 */
.share-btn {
  flex: 1;
  border-radius: 20px;
  font-size: 15px;
  padding: 8px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #4caf50;
  color: white;
  border: none;
  box-shadow: 0 2px 5px rgba(76, 175, 80, 0.3);
}

/* 保存按钮 */
.save-btn {
  flex: 1;
  border-radius: 20px;
  font-size: 15px;
  padding: 8px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to right, #ff7e5f, #feb47b);
  color: white;
  border: none;
  box-shadow: 0 2px 5px rgba(255, 126, 95, 0.3);
}

/* 处理中遮罩 */
.processing-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* 处理中内容 */
.processing-content {
  background-color: white;
  padding: 20px 30px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 加载动画 */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #ff7e5f;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 处理中文本 */
.processing-text {
  font-size: 16px;
  color: #333;
}

/* 标题成功提示 */
.title-success-toast {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 15px 20px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: fadeInOut 1.5s ease-in-out forwards;
}

.success-icon {
  font-size: 24px;
  margin-bottom: 5px;
  color: #4cd964;
}

.success-text {
  font-size: 14px;
}

@keyframes fadeInOut {
  0% { opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { opacity: 0; }
}
