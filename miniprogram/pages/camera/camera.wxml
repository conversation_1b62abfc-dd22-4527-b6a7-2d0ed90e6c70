<!-- camera页面不需要导航栏，因为它是tabBar页面 -->

<!-- 添加导航栏 -->
<navigation-bar 
  title="拍摄美食" 
  showBack="{{false}}" 
  backgroundColor="#ffffff"
  textColor="#000000"
  bindback="goBack">
</navigation-bar>

<!-- 相机组件 - 只在获得授权且相机激活时显示 -->
<camera wx:if="{{hasPermission && cameraActive}}" device-position="back" flash="auto" style="width: 100%; height: calc(100vh - {{statusBarHeight + navBarHeight}}px);">
  <cover-view class="controls">
    <cover-view class="take-photo" bindtap="takePhoto">
      <cover-view class="camera-icon"></cover-view>
    </cover-view>
  </cover-view>
</camera>

<!-- 未授权时的占位界面 -->
<view wx:else class="no-permission" style="padding-top: {{statusBarHeight + navBarHeight}}px;">
  <view class="permission-icon">
    <view class="camera-placeholder"></view>
  </view>
  
  <view class="permission-title">需要使用相机权限</view>
  
  <view class="permission-desc">
    <text>我们需要获取您的相机权限，以便您能够：</text>
    <text>• 拍摄美食照片</text>
    <text>• 记录生活中的美妙瞬间</text>
    <text>• 分享美好回忆</text>
  </view>
  
  <view class="permission-info">
    <text>我们尊重您的隐私，照片仅保存在本地设备</text>
  </view>
  
  <button class="permission-button" bindtap="checkCameraPermission">授权使用相机</button>
  
  <view class="cancel-button" bindtap="goBack">暂不使用，返回</view>
</view>