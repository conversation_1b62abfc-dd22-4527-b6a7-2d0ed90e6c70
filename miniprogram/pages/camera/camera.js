// pages/camera/camera.js
Page({
  data: {
    cameraContext: null,
    hasPermission: false,
    cameraActive: false, // 控制相机是否激活
    statusBarHeight: 20,
    navBarHeight: 44
  },

  onLoad() {
    // 不再自动检查相机权限，改为手动触发
    // 只获取系统信息
    this.getSystemInfo()
  },

  // 获取系统信息
  getSystemInfo() {
    try {
      const app = getApp()
      if (app && app.globalData) {
        this.setData({
          statusBarHeight: app.globalData.systemInfo?.statusBarHeight || 20,
          navBarHeight: app.globalData.navBarHeight || 44
        })
      } else {
        // 如果全局数据不可用，尝试直接获取
        let systemInfo = {};
        
        // 尝试使用新的API，如果不支持则回退到旧API
        try {
          systemInfo = {
            ...wx.getDeviceInfo(),
            ...wx.getSystemSetting(),
            ...wx.getAppBaseInfo(),
            ...wx.getWindowInfo()
          }
        } catch (apiError) {
          console.log('新API不可用，使用旧API替代');
          // 回退到旧API
          systemInfo = wx.getSystemInfoSync();
        }
        
        this.setData({
          statusBarHeight: systemInfo.statusBarHeight || 20
        })
      }
    } catch (e) {
      console.error('获取系统信息失败', e)
    }
  },

  // 页面显示时如果已授权则激活相机
  onShow() {
    // 检查是否已经获得权限
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.camera']) {
          // 用户已授权，激活相机
          this.setData({ 
            hasPermission: true,
            cameraActive: true
          })
          this.initCamera()
        }
        // 未授权则显示授权界面，等待用户手动触发授权
      }
    })
  },

  // 页面隐藏时停止相机
  onHide() {
    this.stopCamera()
  },

  // 页面卸载时停止相机
  onUnload() {
    this.stopCamera()
  },

  // 停止相机
  stopCamera() {
    // 先设置相机为非激活状态，这会关闭camera组件
    this.setData({
      cameraActive: false,
      cameraContext: null
    })
  },

  // 检查相机权限
  checkCameraPermission() {
    // 先检查用户是否已经授权过相机权限
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.camera']) {
          // 用户已授权，直接启动相机
          this.setData({ 
            hasPermission: true,
            cameraActive: true
          })
          this.initCamera()
        } else {
          // 用户未授权，显示对话框询问是否授权
          wx.showModal({
            title: '相机权限',
            content: '我们需要使用您的相机进行拍照，是否授权？',
            confirmText: '确认授权',
            cancelText: '暂不授权',
            success: (modalRes) => {
              if (modalRes.confirm) {
                // 用户同意，申请权限
                wx.authorize({
                  scope: 'scope.camera',
                  success: () => {
                    // 授权成功
                    this.setData({ 
                      hasPermission: true,
                      cameraActive: true
                    })
                    this.initCamera()
                  },
                  fail: () => {
                    // 授权失败，引导用户去设置页面开启权限
                    wx.showModal({
                      title: '授权失败',
                      content: '请在设置页面中手动开启相机权限',
                      confirmText: '去设置',
                      success: (settingModalRes) => {
                        if (settingModalRes.confirm) {
                          wx.openSetting({
                            success: (settingRes) => {
                              if (settingRes.authSetting['scope.camera']) {
                                this.setData({ 
                                  hasPermission: true,
                                  cameraActive: true
                                })
                                this.initCamera()
                              } else {
                                // 用户在设置页面仍未授权
                                wx.showToast({
                                  title: '未获得相机授权',
                                  icon: 'none'
                                })
                                this.goBack() // 未获得授权则返回上一页
                              }
                            }
                          })
                        } else {
                          // 用户取消去设置
                          this.goBack() // 返回上一页
                        }
                      }
                    })
                  }
                })
              } else {
                // 用户拒绝授权
                wx.showToast({
                  title: '未获得相机授权',
                  icon: 'none'
                })
                this.goBack() // 返回上一页
              }
            }
          })
        }
      },
      fail: (err) => {
        console.error('获取权限设置失败:', err)
        wx.showToast({
          title: '获取权限失败',
          icon: 'none'
        })
        this.goBack() // 获取权限设置失败则返回上一页
      }
    })
  },

  // 初始化相机
  initCamera() {
    if (this.data.hasPermission) {
      const cameraContext = wx.createCameraContext()
      this.setData({ cameraContext })
    }
  },

  // 拍照
  takePhoto() {
    if (!this.data.cameraContext) return
    
    this.data.cameraContext.takePhoto({
      quality: 'high',
      success: (res) => {
        // 先保存原图到相册
        this.saveImageToAlbum(res.tempImagePath)
        
        // 然后进入多图编辑页面（编辑后的图片只在编辑页面保存）
        this.navigateToMultiEditor([res.tempImagePath])
      },
      fail: (error) => {
        console.error('拍照失败:', error)
        wx.showToast({
          title: '拍照失败',
          icon: 'none'
        })
      }
    })
  },

  // 保存原图到相册
  saveImageToAlbum(tempFilePath) {
    wx.authorize({
      scope: 'scope.writePhotosAlbum',
      success: () => {
        this.doSaveImage(tempFilePath)
      },
      fail: () => {
        wx.showModal({
          title: '提示',
          content: '需要保存原图到您的相册，是否授权？',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting({
                success: (settingRes) => {
                  if (settingRes.authSetting['scope.writePhotosAlbum']) {
                    this.doSaveImage(tempFilePath)
                  }
                }
              })
            }
          }
        })
      }
    })
  },

  // 执行保存原图操作
  doSaveImage(tempFilePath) {
    wx.saveImageToPhotosAlbum({
      filePath: tempFilePath,
      success: () => {
        wx.showToast({
          title: '原图已保存',
          icon: 'success'
        })
      },
      fail: (error) => {
        console.error('原图保存失败:', error)
        wx.showToast({
          title: '原图保存失败',
          icon: 'none'
        })
      }
    })
  },
  
  // 导航到多图编辑页面
  navigateToMultiEditor(imagePaths) {
    // 确保传入的是数组
    if (!Array.isArray(imagePaths)) {
      imagePaths = [imagePaths];
    }
    
    // 无论是1张还是多张图片，都进入多图编辑页面
    wx.navigateTo({
      url: '/pages/multi-editor/multi-editor',
      success: (res) => {
        // 传递图片路径数组
        res.eventChannel.emit('acceptDataFromOpenerPage', { 
          images: imagePaths
        });
      },
      fail: (error) => {
        console.error('导航到多图编辑页面失败:', error);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 从相册选择图片
  chooseFromAlbum() {
    wx.chooseMedia({
      count: 9, // 最多可以选择9张图片
      mediaType: ['image'],
      sourceType: ['album'],
      success: (res) => {
        if (res.tempFiles && res.tempFiles.length > 0) {
          // 提取所有选中图片的临时路径
          const imagePaths = res.tempFiles.map(file => file.tempFilePath);
          
          // 导航到多图编辑页面
          this.navigateToMultiEditor(imagePaths);
        }
      },
      fail: (error) => {
        console.error('选择图片失败:', error);
        if (error.errMsg !== "chooseMedia:fail cancel") {
          wx.showToast({
            title: '选择图片失败',
            icon: 'none'
          });
        }
      }
    });
  },

  // 返回上一页
  goBack() {
    wx.navigateBack({
      fail: () => {
        // 如果返回失败（可能是没有上一页），则导航到首页
        wx.switchTab({
          url: '/pages/index/index'
        })
      }
    })
  },

  // 支持右上角转发到好友
  onShareAppMessage: function() {
    // 获取全局用户信息
    const app = getApp();
    const nickName = (app.globalData.userInfo && app.globalData.userInfo.nickName) || '用户';
    
    return {
      title: `${nickName}邀请您使用图片助手`,
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.png' // 使用一个默认的分享封面图
    };
  },
  
  // 支持右上角转发到朋友圈
  onShareTimeline: function() {
    return {
      title: '图片助手-轻松拍照编辑',
      imageUrl: '/images/share-cover.png' // 使用一个默认的分享封面图
    };
  }
})