.controls {
  position: fixed;
  bottom: 50rpx;
  left: 0;
  width: 100%;
  height: 150rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.take-photo {
  width: 140rpx;
  height: 140rpx;
  border-radius: 70rpx;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  border: 6rpx solid #ffffff;
  transition: all 0.2s ease;
}

.take-photo:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
}

.camera-icon {
  width: 70rpx;
  height: 70rpx;
  position: relative;
  background: #FF7D2C; /* 使用应用主题色 */
  border-radius: 35rpx;
  box-shadow: inset 0 0 10rpx rgba(0, 0, 0, 0.1);
}

.camera-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 45rpx;
  height: 35rpx;
  border: 4rpx solid #fff;
  border-radius: 8rpx;
}

.camera-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 22rpx;
  height: 22rpx;
  border: 3rpx solid #fff;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
}

/* 未授权时的占位界面样式 */
.no-permission {
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 40rpx;
  background: #ffffff;
  box-sizing: border-box;
}

.permission-icon {
  margin-top: 80rpx;
  width: 220rpx;
  height: 220rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #f8f8f8, #e8e8e8);
  border-radius: 50%;
  margin-bottom: 50rpx;
  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.05);
}

.camera-placeholder {
  width: 110rpx;
  height: 110rpx;
  position: relative;
  background: linear-gradient(135deg, #ffffff, #f0f0f0);
  border-radius: 15rpx;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.1);
}

.camera-placeholder::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 75rpx;
  height: 55rpx;
  border: 5rpx solid #FF7D2C;
  border-radius: 8rpx;
}

.camera-placeholder::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid #FF7D2C;
  border-radius: 50%;
}

.permission-title {
  font-size: 38rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
  text-align: center;
}

.permission-desc {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 40rpx;
  background: #f9f9f9;
  padding: 30rpx;
  border-radius: 20rpx;
}

.permission-desc text {
  font-size: 28rpx;
  color: #555;
  line-height: 2;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
}

.permission-desc text:first-child {
  font-weight: 500;
  margin-bottom: 20rpx;
}

.permission-info {
  font-size: 26rpx;
  color: #888;
  margin-bottom: 60rpx;
  text-align: center;
  background: rgba(255, 125, 44, 0.1);
  padding: 20rpx 30rpx;
  border-radius: 30rpx;
}

.permission-button {
  width: 90%;
  height: 90rpx;
  line-height: 80rpx;
  padding-top: 10rpx;
  background: linear-gradient(135deg, #FF9D5C, #FF7D2C);
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 45rpx;
  margin: 0 auto 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(255, 125, 44, 0.3);
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}

.permission-button:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 10rpx rgba(255, 125, 44, 0.2);
}

.cancel-button {
  font-size: 28rpx;
  color: #666;
  padding: 20rpx 40rpx;
  border-radius: 30rpx;
  background: #f0f0f0;
  margin: 0 auto;
  text-align: center;
  width: fit-content;
  display: block;
}