<view class="save-container">
  <!-- 顶部工具栏 -->
  <view class="toolbar">
    <view class="toolbar-btn" bindtap="backToEditor">返回</view>
    <view class="toolbar-title">保存图片</view>
    <view class="toolbar-btn"></view> <!-- 占位 -->
  </view>
  
  <!-- 图片预览 -->
  <view class="preview-container">
    <image class="preview-image" src="{{imagePath}}" mode="aspectFit"></image>
  </view>
  
  <!-- 保存选项 -->
  <view class="options-container">
    <view class="options-title">选择保存质量:</view>
    <view class="options-list">
      <view class="option-item {{selectedOption === option.value ? 'active' : ''}}" 
            wx:for="{{savingOptions}}" 
            wx:for-item="option" 
            wx:key="value"
            data-option="{{option.value}}" 
            bindtap="changeSaveOption">
        {{option.name}}
      </view>
    </view>
  </view>
  
  <!-- 保存按钮 -->
  <view class="action-buttons">
    <button class="action-btn primary" bindtap="saveToAlbum" disabled="{{isSaving}}">保存到相册</button>
    <button class="action-btn" bindtap="shareToWechat">分享到微信</button>
    <button class="action-btn" bindtap="createCollage">制作拼图</button>
  </view>
  
  <!-- 底部导航 -->
  <view class="bottom-nav">
    <view class="nav-item" bindtap="goToHome">返回首页</view>
  </view>
  
  <!-- 保存中遮罩 -->
  <view class="saving-mask" wx:if="{{isSaving}}">
    <view class="saving-content">
      <view class="progress-bar-container">
        <view class="progress-bar" style="width: {{savingProgress}}%;"></view>
      </view>
      <text class="progress-text">正在保存 {{savingProgress}}%</text>
    </view>
  </view>
  
  <!-- 保存成功弹窗 -->
  <view class="success-dialog" wx:if="{{saveSuccess}}">
    <view class="success-content">
      <view class="success-icon"></view>
      <text class="success-text">保存成功</text>
      <text class="success-path">图片已保存到相册</text>
      <button class="close-btn" bindtap="goToHome">返回首页</button>
    </view>
  </view>
</view> 