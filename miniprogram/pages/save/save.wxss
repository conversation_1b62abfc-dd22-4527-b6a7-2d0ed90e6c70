/* 保存页面容器 */
.save-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}

/* 顶部工具栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #ffffff;
  border-bottom: 1px solid #eeeeee;
  height: 44px;
}

.toolbar-btn {
  font-size: 16px;
  color: #333;
  padding: 5px 10px;
}

.toolbar-title {
  font-size: 18px;
  font-weight: bold;
}

/* 图片预览 */
.preview-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f0f0f0;
  padding: 20px;
  max-height: 40vh;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
}

/* 选项容器 */
.options-container {
  background-color: white;
  padding: 15px;
  margin: 10px 0;
}

.options-title {
  font-size: 16px;
  margin-bottom: 10px;
  color: #333;
}

.options-list {
  display: flex;
  flex-wrap: wrap;
}

.option-item {
  padding: 8px 15px;
  border: 1px solid #ddd;
  border-radius: 20px;
  margin-right: 10px;
  margin-bottom: 10px;
  color: #333;
}

.option-item.active {
  background-color: #07c160;
  color: white;
  border-color: #07c160;
}

/* 操作按钮 */
.action-buttons {
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.action-btn {
  margin-bottom: 10px;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
  color: #333;
  background-color: #f8f8f8;
  border: 1px solid #ddd;
  border-radius: 22px;
}

.action-btn.primary {
  background-color: #07c160;
  color: white;
  border: none;
}

.action-btn[disabled] {
  background-color: #ccc;
  color: #fff;
}

/* 底部导航 */
.bottom-nav {
  padding: 15px;
  text-align: center;
  border-top: 1px solid #eee;
  background-color: white;
}

.nav-item {
  color: #07c160;
  font-size: 16px;
}

/* 保存中遮罩 */
.saving-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.saving-content {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  width: 80%;
  max-width: 300px;
}

.progress-bar-container {
  height: 8px;
  background-color: #eee;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-bar {
  height: 100%;
  background-color: #07c160;
  transition: width 0.3s;
}

.progress-text {
  display: block;
  text-align: center;
  color: #333;
  font-size: 14px;
}

/* 成功弹窗 */
.success-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.success-content {
  background-color: white;
  padding: 30px 20px;
  border-radius: 10px;
  width: 80%;
  max-width: 300px;
  text-align: center;
}

.success-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 20px;
  background-color: #07c160;
  border-radius: 50%;
  position: relative;
}

.success-icon:after {
  content: '';
  position: absolute;
  width: 30px;
  height: 15px;
  border-left: 3px solid white;
  border-bottom: 3px solid white;
  transform: rotate(-45deg);
  left: 15px;
  top: 20px;
}

.success-text {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.success-path {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
}

.close-btn {
  background-color: #07c160;
  color: white;
  border-radius: 22px;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
} 