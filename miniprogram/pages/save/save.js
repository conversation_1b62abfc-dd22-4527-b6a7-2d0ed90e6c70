// 图片保存页面
Page({
  data: {
    imagePath: '', // 已编辑图片路径
    savingOptions: [
      { name: '原始分辨率', value: 'original' },
      { name: '高清 (1920x1080)', value: 'hd' },
      { name: '压缩版 (1280x720)', value: 'compressed' }
    ],
    selectedOption: 'original',
    isSaving: false,
    savingProgress: 0,
    saveSuccess: false,
    savePath: '' // 保存后的图片路径
  },
  
  onLoad: function(options) {
    if (options.imagePath) {
      this.setData({
        imagePath: options.imagePath
      });
    }
  },
  
  // 选择保存选项
  changeSaveOption: function(e) {
    const option = e.currentTarget.dataset.option;
    this.setData({
      selectedOption: option
    });
  },
  
  // 根据选中选项调整图片分辨率和压缩
  processImageByOption: function() {
    return new Promise((resolve, reject) => {
      // 如果选择原始分辨率，则直接返回原图
      if (this.data.selectedOption === 'original') {
        resolve(this.data.imagePath);
        return;
      }
      
      // 创建离屏canvas处理分辨率
      const img = wx.createImage();
      
      img.onload = () => {
        let targetWidth, targetHeight, quality;
        
        // 根据选项设置目标分辨率和质量
        switch (this.data.selectedOption) {
          case 'hd':
            targetWidth = 1920;
            targetHeight = 1080;
            quality = 0.9;
            break;
          case 'compressed':
            targetWidth = 1280;
            targetHeight = 720;
            quality = 0.75;
            break;
          default:
            // 默认情况，维持原始尺寸
            resolve(this.data.imagePath);
            return;
        }
        
        // 保持宽高比
        const aspectRatio = img.width / img.height;
        if (aspectRatio > targetWidth / targetHeight) {
          // 图片更宽，以宽度为准
          targetHeight = Math.round(targetWidth / aspectRatio);
        } else {
          // 图片更高，以高度为准
          targetWidth = Math.round(targetHeight * aspectRatio);
        }
        
        // 创建canvas
        const canvas = wx.createOffscreenCanvas({
          width: targetWidth,
          height: targetHeight
        });
        const ctx = canvas.getContext('2d');
        
        // 应用优化处理
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        
        // 绘制调整大小后的图片
        ctx.drawImage(img, 0, 0, targetWidth, targetHeight);
        
        // 导出为临时文件
        wx.canvasToTempFilePath({
          canvas: canvas,
          width: targetWidth,
          height: targetHeight,
          destWidth: targetWidth,
          destHeight: targetHeight,
          fileType: 'jpg',
          quality: quality,
          success: (res) => {
            // 获取原始图片和压缩后图片的大小信息
            Promise.all([
              this.getFileInfo(this.data.imagePath),
              this.getFileInfo(res.tempFilePath)
            ]).then(([originalInfo, compressedInfo]) => {
              const compressionRatio = Math.round((1 - compressedInfo.size / originalInfo.size) * 100);
              
              // 显示压缩信息
              wx.showToast({
                title: `压缩率: ${compressionRatio}%`,
                icon: 'none',
                duration: 1500
              });
              
              resolve(res.tempFilePath);
            }).catch(() => {
              // 如果获取文件信息失败，仍然返回处理后的图片
            resolve(res.tempFilePath);
            });
          },
          fail: (error) => {
            console.error('调整分辨率失败:', error);
            // 如果失败，回退到原始图片
            resolve(this.data.imagePath);
          }
        }, this);
      };
      
      img.onerror = () => {
        console.error('加载图片失败');
        resolve(this.data.imagePath); // 失败时使用原图
      };
      
      img.src = this.data.imagePath;
    });
  },
  
  // 获取文件信息
  getFileInfo: function(filePath) {
    return new Promise((resolve, reject) => {
      wx.getFileInfo({
        filePath: filePath,
        success: (res) => {
          resolve(res);
        },
        fail: (err) => {
          console.error('获取文件信息失败:', err);
          reject(err);
        }
      });
    });
  },
  
  // 保存图片到本地相册
  saveToAlbum: function() {
    this.setData({ 
      isSaving: true,
      savingProgress: 0
    });
    
    // 开始进度动画
    this.startProgressAnimation();
    
    // 根据选择的选项处理图片
    this.processImageByOption().then(processedImagePath => {
      // 调用微信保存图片API
      wx.saveImageToPhotosAlbum({
        filePath: processedImagePath,
        success: (res) => {
          // 保存成功
          this.setData({
            savingProgress: 100,
            isSaving: false,
            saveSuccess: true,
            savePath: processedImagePath
          });
          
          // 添加到历史记录
          this.addToHistory(processedImagePath);
          
          // 显示成功提示
          wx.showToast({
            title: '保存成功',
            icon: 'success',
            duration: 1500
          });
        },
        fail: (error) => {
          // 保存失败
          this.setData({ 
            isSaving: false,
            saveSuccess: false,
            savingProgress: 0
          });
          
          wx.showToast({
            title: '保存失败，请检查权限',
            icon: 'none'
          });
        }
      });
    }).catch(error => {
      console.error('处理图片失败:', error);
      this.setData({ 
        isSaving: false,
        saveSuccess: false,
        savingProgress: 0
      });
    });
  },
  
  // 添加到历史记录
  addToHistory: function(imagePath) {
    const now = new Date();
    const historyItem = {
      path: imagePath,
      date: now.toISOString(),
      timestamp: now.getTime()
    };
    
    // 获取现有历史记录
    let historyImages = wx.getStorageSync('historyImages') || [];
    
    // 添加新记录
    historyImages.unshift(historyItem);
    
    // 限制历史记录数量
    if (historyImages.length > 100) {
      historyImages = historyImages.slice(0, 100);
    }
    
    // 保存到本地存储
    wx.setStorageSync('historyImages', historyImages);
  },
  
  // 分享图片到微信
  shareToWechat: function() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },
  
  // 跳转到拼图页面
  createCollage: function() {
    wx.navigateTo({
      url: `/pages/collage/collage?imagePath=${encodeURIComponent(this.data.imagePath)}`
    });
  },
  
  // 返回编辑器
  backToEditor: function() {
    wx.navigateBack();
  },
  
  // 返回首页
  goToHome: function() {
    wx.reLaunch({
      url: '/pages/index/index'
    });
  },
  
  // 模拟进度动画
  startProgressAnimation: function() {
    let progress = 0;
    const timer = setInterval(() => {
      progress += 5;
      if (progress > 95) {
        clearInterval(timer);
      } else {
        this.setData({ savingProgress: progress });
      }
    }, 80);
  },
  
  // 用户分享到好友
  onShareAppMessage: function() {
    // 获取全局用户信息
    const app = getApp();
    const nickName = (app.globalData.userInfo && app.globalData.userInfo.nickName) || '用户';
    
    return {
      title: `${nickName}的作品`,
      path: '/pages/index/index',
      imageUrl: this.data.imagePath
    };
  },
  
  // 用户分享到朋友圈
  onShareTimeline: function() {
    return {
      title: '图片助手-分享我的图片',
      imageUrl: this.data.imagePath
    };
  }
});