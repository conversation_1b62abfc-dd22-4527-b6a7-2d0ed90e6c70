// 首页
const app = getApp();

Page({
  data: {
    userInfo: {},
    hasUserInfo: false,
    canIUseGetUserProfile: false,
    navBarHeight: app.globalData.navBarHeight || 44,
    statusBarHeight: 20,
    menuButtonHeight: 32,
    // 主题相关
    darkMode: false,
    highContrast: false,
    largeText: false,
    simplifiedUI: false,
    showDebugEntry: false  // 是否显示调试入口
  },
  
  onLoad() {
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      });
    }
    
    // 设置导航栏高度
    this.setNavBarHeight();
    
    // 加载主题设置
    this.loadThemeSettings();

    // 检查是否显示调试入口
    this.checkDebugEntry();

    // 检查是否显示调试入口
    this.checkDebugEntry();
  },
  
  onShow() {
    // 更新主题设置
    this.loadThemeSettings();
  },
  
  // 设置导航栏高度
  setNavBarHeight() {
    let systemInfo = {};
    
    // 尝试使用新的API，如果不支持则回退到旧API
    try {
      // 使用新的API替代已废弃的wx.getSystemInfoSync
      systemInfo = {
        ...wx.getDeviceInfo(),
        ...wx.getSystemSetting(),
        ...wx.getAppBaseInfo(),
        ...wx.getWindowInfo()
      };
    } catch (apiError) {
      console.log('新API不可用，使用旧API替代');
      // 回退到旧API
      systemInfo = wx.getSystemInfoSync();
    }
    
    // 获取胶囊按钮信息，增加错误处理
    let menuButtonInfo;
    try {
      menuButtonInfo = wx.getMenuButtonBoundingClientRect();
    } catch (error) {
      console.log('获取胶囊按钮信息失败，使用默认值');
      // 使用默认值
      menuButtonInfo = {
        bottom: 56,
        height: 32,
        left: 281,
        right: 368,
        top: 24,
        width: 87
      };
    }
    
    const statusBarHeight = systemInfo.statusBarHeight;
    const menuButtonHeight = menuButtonInfo.height;
    const menuButtonTop = menuButtonInfo.top;
    
    // 计算导航栏高度 = 状态栏高度 + 菜单按钮上下边距 * 2 + 菜单按钮高度
    const navBarHeight = statusBarHeight + (menuButtonTop - statusBarHeight) * 2 + menuButtonHeight;
    
    this.setData({
      navBarHeight,
      statusBarHeight,
      menuButtonHeight
    });
    
    // 更新全局数据
    app.globalData.navBarHeight = navBarHeight;
  },
  
  // 加载主题设置
  loadThemeSettings() {
    const settings = app.globalData.settings || {};
    // 确保设置中的值不为undefined
    this.setData({
      darkMode: settings.darkMode || false,
      highContrast: settings.highContrast || false,
      largeText: settings.largeText || false,
      simplifiedUI: settings.simplifiedUI || false
    });
  },
  
  // 切换主题
  toggleTheme() {
    app.toggleTheme();
    this.setData({
      darkMode: !this.data.darkMode
    });
  },
  
  // 跳转到设置页面
  goToSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    });
  },
  
  // 跳转到拍照页面
  goToCamera() {
    console.log('goToCamera方法被调用');
    wx.switchTab({
      url: '/pages/camera/camera',
      success: (res) => {
        console.log('跳转到camera页面成功');
      },
      fail: (err) => {
        console.error('跳转到camera页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 跳转到相册选择页面
  goToAlbum() {
    // 显示加载提示
    wx.showLoading({
      title: '选择图片中...',
      mask: true
    });

    wx.chooseMedia({
      count: 9, // 最多可以选择的图片张数
      mediaType: ['image'],
      sourceType: ['album'],
      success: (res) => {
        wx.hideLoading();

        const tempFiles = res.tempFiles;
        console.log('选择的图片文件:', JSON.stringify(tempFiles));

        // 验证选择的文件
        if (!tempFiles || tempFiles.length === 0) {
          wx.showToast({
            title: '未选择任何图片',
            icon: 'none'
          });
          return;
        }

        // 验证每个文件的有效性
        const validFiles = tempFiles.filter(file => {
          if (!file.tempFilePath) {
            console.warn('图片文件路径为空:', file);
            return false;
          }

          if (file.size === 0) {
            console.warn('图片文件大小为0:', file);
            return false;
          }

          if (file.size > 50 * 1024 * 1024) { // 50MB限制
            console.warn('图片文件过大:', file);
            wx.showToast({
              title: '图片文件过大，请选择小于50MB的图片',
              icon: 'none'
            });
            return false;
          }

          return true;
        });

        if (validFiles.length === 0) {
          wx.showToast({
            title: '所选图片都无效，请重新选择',
            icon: 'none'
          });
          return;
        }

        const images = validFiles.map(file => {
          console.log('处理图片路径:', file.tempFilePath);
          return file.tempFilePath;
        });

        console.log('最终传递的图片路径数组:', images);

        // 显示跳转加载提示
        wx.showLoading({
          title: '加载图片中...',
          mask: true
        });

        // 所有图片都进入多图编辑页面，无论图片数量
        wx.navigateTo({
          url: '/pages/multi-editor/multi-editor',
          success: (res) => {
            res.eventChannel.emit('acceptDataFromOpenerPage', {
              images: images
            });
            console.log('成功传递图片数据到多图编辑页面:', images);
            wx.hideLoading();
          },
          fail: (err) => {
            console.error('跳转到多图编辑页面失败:', err);
            wx.hideLoading();
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('选择图片失败:', err);

        // 只有当用户不是取消操作时才显示错误提示
        if (err.errMsg !== "chooseMedia:fail cancel") {
          let errorMessage = '选择图片失败';

          // 根据错误类型提供更具体的提示
          if (err.errMsg.includes('permission')) {
            errorMessage = '没有相册访问权限，请在设置中开启';
          } else if (err.errMsg.includes('system')) {
            errorMessage = '系统错误，请稍后重试';
          } else if (err.errMsg.includes('network')) {
            errorMessage = '网络错误，请检查网络连接';
          }

          wx.showModal({
            title: '选择图片失败',
            content: errorMessage,
            showCancel: false,
            confirmText: '确定'
          });
        }
      }
    });
  },
  
  // 跳转到裁剪页面
  goToCrop() {
    // 显示加载提示
    wx.showLoading({
      title: '选择图片中...',
      mask: true
    });

    wx.chooseMedia({
      count: 1, // 裁剪功能一次只能选择一张图片
      mediaType: ['image'],
      sourceType: ['album'], // 移除camera选项，禁用拍照入口
      success: (res) => {
        wx.hideLoading();

        if (!res.tempFiles || res.tempFiles.length === 0) {
          wx.showToast({
            title: '未选择图片',
            icon: 'none'
          });
          return;
        }

        const tempFile = res.tempFiles[0];
        const tempFilePath = tempFile.tempFilePath;

        console.log('选择的裁剪图片:', tempFilePath);
        console.log('图片文件信息:', tempFile);

        // 验证图片文件
        if (!tempFilePath) {
          wx.showToast({
            title: '图片路径无效',
            icon: 'none'
          });
          return;
        }

        if (tempFile.size === 0) {
          wx.showToast({
            title: '图片文件损坏',
            icon: 'none'
          });
          return;
        }

        if (tempFile.size > 50 * 1024 * 1024) { // 50MB限制
          wx.showToast({
            title: '图片文件过大，请选择小于50MB的图片',
            icon: 'none'
          });
          return;
        }

        // 显示跳转加载提示
        wx.showLoading({
          title: '加载图片中...',
          mask: true
        });

        wx.navigateTo({
          url: '/pages/crop/crop',
          success: (res) => {
            res.eventChannel.emit('acceptDataFromOpenerPage', {
              imagePath: tempFilePath
            });
            console.log('成功传递图片数据到裁剪页面:', tempFilePath);
            wx.hideLoading();
          },
          fail: (err) => {
            console.error('跳转到裁剪页面失败:', err);
            wx.hideLoading();
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('选择裁剪图片失败:', err);

        // 只有当用户不是取消操作时才显示错误提示
        if (err.errMsg !== "chooseMedia:fail cancel") {
          let errorMessage = '选择图片失败';

          // 根据错误类型提供更具体的提示
          if (err.errMsg.includes('permission')) {
            errorMessage = '没有相册访问权限，请在设置中开启';
          } else if (err.errMsg.includes('system')) {
            errorMessage = '系统错误，请稍后重试';
          } else if (err.errMsg.includes('network')) {
            errorMessage = '网络错误，请检查网络连接';
          }

          wx.showModal({
            title: '选择图片失败',
            content: errorMessage,
            showCancel: false,
            confirmText: '确定'
          });
        }
      }
    });
  },

  // 支持右上角转发到好友
  onShareAppMessage: function() {
    // 获取全局用户信息
    const app = getApp();
    const nickName = (app.globalData.userInfo && app.globalData.userInfo.nickName) || '用户';
    
    return {
      title: `${nickName}邀请您使用图片助手`,
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.png' // 使用一个默认的分享封面图
    };
  },
  
  // 支持右上角转发到朋友圈
  onShareTimeline: function() {
    return {
      title: '图片助手-一键美化您的照片',
      imageUrl: '/images/share-cover.png' // 使用一个默认的分享封面图
    };
  },

  // 检查是否显示调试入口
  checkDebugEntry: function() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      const isIOS = systemInfo.platform === 'ios';
      const isDevTool = systemInfo.platform === 'devtools';

      // 仅在iOS真机环境下显示调试入口
      const showDebug = isIOS && !isDevTool;

      console.log('调试入口检查:', {
        platform: systemInfo.platform,
        isIOS,
        isDevTool,
        showDebug
      });

      this.setData({
        showDebugEntry: showDebug
      });
    } catch (error) {
      console.error('检查调试入口失败:', error);
    }
  },

  // 跳转到调试页面
  goToDebug: function() {
    console.log('跳转到调试页面');

    wx.navigateTo({
      url: '/pages/debug/debug',
      success: () => {
        console.log('跳转到调试页面成功');
      },
      fail: (err) => {
        console.error('跳转到调试页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  }
});