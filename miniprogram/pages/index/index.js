// 首页
const app = getApp();

Page({
  data: {
    userInfo: {},
    hasUserInfo: false,
    canIUseGetUserProfile: false,
    navBarHeight: app.globalData.navBarHeight || 44,
    statusBarHeight: 20,
    menuButtonHeight: 32,
    // 主题相关
    darkMode: false,
    highContrast: false,
    largeText: false,
    simplifiedUI: false
  },
  
  onLoad() {
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      });
    }
    
    // 设置导航栏高度
    this.setNavBarHeight();
    
    // 加载主题设置
    this.loadThemeSettings();
  },
  
  onShow() {
    // 更新主题设置
    this.loadThemeSettings();
  },
  
  // 设置导航栏高度
  setNavBarHeight() {
    let systemInfo = {};
    
    // 尝试使用新的API，如果不支持则回退到旧API
    try {
      // 使用新的API替代已废弃的wx.getSystemInfoSync
      systemInfo = {
        ...wx.getDeviceInfo(),
        ...wx.getSystemSetting(),
        ...wx.getAppBaseInfo(),
        ...wx.getWindowInfo()
      };
    } catch (apiError) {
      console.log('新API不可用，使用旧API替代');
      // 回退到旧API
      systemInfo = wx.getSystemInfoSync();
    }
    
    // 获取胶囊按钮信息，增加错误处理
    let menuButtonInfo;
    try {
      menuButtonInfo = wx.getMenuButtonBoundingClientRect();
    } catch (error) {
      console.log('获取胶囊按钮信息失败，使用默认值');
      // 使用默认值
      menuButtonInfo = {
        bottom: 56,
        height: 32,
        left: 281,
        right: 368,
        top: 24,
        width: 87
      };
    }
    
    const statusBarHeight = systemInfo.statusBarHeight;
    const menuButtonHeight = menuButtonInfo.height;
    const menuButtonTop = menuButtonInfo.top;
    
    // 计算导航栏高度 = 状态栏高度 + 菜单按钮上下边距 * 2 + 菜单按钮高度
    const navBarHeight = statusBarHeight + (menuButtonTop - statusBarHeight) * 2 + menuButtonHeight;
    
    this.setData({
      navBarHeight,
      statusBarHeight,
      menuButtonHeight
    });
    
    // 更新全局数据
    app.globalData.navBarHeight = navBarHeight;
  },
  
  // 加载主题设置
  loadThemeSettings() {
    const settings = app.globalData.settings || {};
    // 确保设置中的值不为undefined
    this.setData({
      darkMode: settings.darkMode || false,
      highContrast: settings.highContrast || false,
      largeText: settings.largeText || false,
      simplifiedUI: settings.simplifiedUI || false
    });
  },
  
  // 切换主题
  toggleTheme() {
    app.toggleTheme();
    this.setData({
      darkMode: !this.data.darkMode
    });
  },
  
  // 跳转到设置页面
  goToSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    });
  },
  
  // 跳转到拍照页面
  goToCamera() {
    console.log('goToCamera方法被调用');
    wx.switchTab({
      url: '/pages/camera/camera',
      success: (res) => {
        console.log('跳转到camera页面成功');
      },
      fail: (err) => {
        console.error('跳转到camera页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 跳转到相册选择页面
  goToAlbum() {
    wx.chooseMedia({
      count: 9, // 最多可以选择的图片张数
      mediaType: ['image'],
      sourceType: ['album'],
      success: (res) => {
        const tempFiles = res.tempFiles;
        // 打印原始tempFiles数据，查看完整结构
        console.log('选择的图片文件:', JSON.stringify(tempFiles));
        
        const images = tempFiles.map(file => {
          console.log('处理图片路径:', file.tempFilePath);
          return file.tempFilePath;
        });
        
        if (images.length === 0) {
          return;
        }
        
        console.log('最终传递的图片路径数组:', images);
        
        // 所有图片都进入多图编辑页面，无论图片数量
        wx.navigateTo({
          url: '/pages/multi-editor/multi-editor',
          success: (res) => {
            res.eventChannel.emit('acceptDataFromOpenerPage', { 
              images: images
            });
            console.log('成功传递图片数据到多图编辑页面:', images);
          },
          fail: (err) => {
            console.error('跳转到多图编辑页面失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        // 只有当用户不是取消操作时才显示错误提示
        if (err.errMsg !== "chooseMedia:fail cancel") {
          wx.showToast({
            title: '选择图片失败',
            icon: 'none'
          });
        }
      }
    });
  },
  
  // 跳转到裁剪页面
  goToCrop() {
    wx.chooseMedia({
      count: 1, // 裁剪功能一次只能选择一张图片
      mediaType: ['image'],
      sourceType: ['album'], // 移除camera选项，禁用拍照入口
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        console.log('选择的裁剪图片:', tempFilePath);
        
        wx.navigateTo({
          url: '/pages/crop/crop',
          success: (res) => {
            res.eventChannel.emit('acceptDataFromOpenerPage', { 
              imagePath: tempFilePath
            });
            console.log('成功传递图片数据到裁剪页面:', tempFilePath);
          },
          fail: (err) => {
            console.error('跳转到裁剪页面失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      },
      fail: (err) => {
        console.error('选择裁剪图片失败:', err);
        // 只有当用户不是取消操作时才显示错误提示
        if (err.errMsg !== "chooseMedia:fail cancel") {
          wx.showToast({
            title: '选择图片失败',
            icon: 'none'
          });
        }
      }
    });
  },

  // 支持右上角转发到好友
  onShareAppMessage: function() {
    // 获取全局用户信息
    const app = getApp();
    const nickName = (app.globalData.userInfo && app.globalData.userInfo.nickName) || '用户';
    
    return {
      title: `${nickName}邀请您使用图片助手`,
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.png' // 使用一个默认的分享封面图
    };
  },
  
  // 支持右上角转发到朋友圈
  onShareTimeline: function() {
    return {
      title: '图片助手-一键美化您的照片',
      imageUrl: '/images/share-cover.png' // 使用一个默认的分享封面图
    };
  }
});