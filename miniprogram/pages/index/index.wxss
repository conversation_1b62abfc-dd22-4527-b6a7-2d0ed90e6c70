/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.page {
  min-height: 100vh;
  background-color: var(--color-bg-light);
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 20rpx;
  box-sizing: border-box;
  width: 100%;
}

/* 功能卡片区域样式 */
.function-cards {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-bottom: 40rpx;
}

.function-card {
  background-color: var(--color-bg-base);
  border-radius: var(--border-radius-base);
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: var(--shadow-base);
  transition: all 0.3s;
}

.function-card:active {
  transform: translateY(2rpx);
  box-shadow: var(--shadow-base);
  opacity: 0.9;
}

.function-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.function-icon .iconfont {
  font-size: 40rpx;
  color: #ffffff;
}

.camera-icon {
  background-color: #FF7D2C;
}

.album-icon {
  background-color: #4CAF50;
}

.crop-icon {
  background-color: #3F51B5;
}

.history-icon {
  background-color: #2196F3;
}

.share-icon {
  background-color: #9C27B0;
}

.function-info {
  flex: 1;
}

.function-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--color-text-primary);
  display: block;
  margin-bottom: 8rpx;
}

.function-desc {
  font-size: 26rpx;
  color: var(--color-text-secondary);
  display: block;
}

.function-arrow {
  width: 36rpx;
  height: 36rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23bbbbbb'%3E%3Cpath d='M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.dark-mode .function-arrow {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666666'%3E%3Cpath d='M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z'/%3E%3C/svg%3E");
}

/* 主题相关样式 */
.dark-mode {
  background-color: var(--color-bg-dark);
  color: var(--color-text-dark);
}

.high-contrast {
  --color-text-primary: #000;
  --color-text-secondary: #222;
  --color-text-placeholder: #444;
  --color-bg-base: #fff;
  --color-bg-light: #f0f0f0;
}

.large-text {
  font-size: 120%;
}

.large-text .function-title {
  font-size: 38rpx;
}

.large-text .function-desc {
  font-size: 30rpx;
}

.simplified-ui .function-desc {
  display: none;
}

.simplified-ui .function-card {
  padding: 24rpx;
}

.simplified-ui .function-icon {
  width: 70rpx;
  height: 70rpx;
}

/* 设置按钮样式 */
.header-right {
  display: flex;
  align-items: center;
  height: 100%;
}

.icon-button {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.header-right .iconfont {
  font-size: 40rpx;
  color: var(--color-text-primary);
} 