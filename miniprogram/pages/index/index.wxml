<!--index.wxml-->
<view class="page {{darkMode ? 'dark-mode' : ''}} {{highContrast ? 'high-contrast' : ''}} {{largeText ? 'large-text' : ''}} {{simplifiedUI ? 'simplified-ui' : ''}}">
  <navigation-bar 
    title="" 
    back="{{false}}" 
    backgroundColor="{{darkMode ? '#1E1E1E' : '#ffffff'}}"
    textColor="{{darkMode ? '#ffffff' : '#000000'}}">
  </navigation-bar>
  
  <scroll-view class="scrollarea" scroll-y type="list" style="padding-top: {{statusBarHeight + navBarHeight}}px;">
    <view class="container">
      <!-- 功能卡片区域 -->
      <view class="function-cards">

        
        <view class="function-card" bindtap="goToAlbum">
          <view class="function-icon album-icon">
            <text class="iconfont icon-picture"></text>
          </view>
          <view class="function-info">
            <text class="function-title">编辑</text>
            <text class="function-desc">从相册选择图片</text>
          </view>
          <view class="function-arrow"></view>
        </view>
        
        <view class="function-card" bindtap="goToCrop">
          <view class="function-icon crop-icon">
            <text class="iconfont icon-crop"></text>
          </view>
          <view class="function-info">
            <text class="function-title">裁剪</text>
            <text class="function-desc">裁剪图片大小/尺寸</text>
          </view>
          <view class="function-arrow"></view>
        </view>

        <!-- 调试入口（仅iOS真机环境显示） -->
        <view class="function-card debug-card" bindtap="goToDebug" wx:if="{{showDebugEntry}}">
          <view class="function-icon debug-icon">
            <text class="iconfont icon-bug">🐛</text>
          </view>
          <view class="function-info">
            <text class="function-title">调试工具</text>
            <text class="function-desc">诊断图片导入问题</text>
          </view>
          <view class="function-arrow"></view>
        </view>
      </view>
    </view>
  </scroll-view>
</view>