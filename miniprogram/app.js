// app.js
const cacheManager = require('./utils/cacheManager.js')

App({
  onLaunch: function() {
    // 获取设备信息
    try {
      let systemInfo = {};
      
      // 尝试使用新的API，如果不支持则回退到旧API
      try {
        // 使用新的API替代已废弃的wx.getSystemInfoSync
        systemInfo = {
          ...wx.getDeviceInfo(),
          ...wx.getSystemSetting(),
          ...wx.getAppBaseInfo(),
          ...wx.getWindowInfo()
        }
      } catch (apiError) {
        console.log('新API不可用，使用旧API替代');
        // 回退到旧API
        systemInfo = wx.getSystemInfoSync();
      }
      
      this.globalData.systemInfo = systemInfo
      // 计算安全区域
      this.globalData.safeArea = systemInfo.safeArea
      // 计算胶囊按钮位置信息
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
      this.globalData.menuButtonInfo = menuButtonInfo
      // 计算导航栏高度 = 胶囊底部到顶部的距离 + 胶囊的高度 + 10px
      this.globalData.navBarHeight = menuButtonInfo.bottom + 10
      
      // 设置系统信息初始化完成标志
      this.globalData.systemInfoReady = true
      
      // 如果有等待系统信息的回调，执行它们
      if (this.systemInfoReadyCallback) {
        this.systemInfoReadyCallback(systemInfo)
      }
    } catch (error) {
      console.error('获取系统信息失败:', error)
      wx.showToast({
        title: '获取系统信息失败',
        icon: 'none'
      })
    }
    
    // 初始化缓存管理器
    try {
      cacheManager.init();
    } catch (error) {
      console.error('初始化缓存管理器失败:', error)
    }
    
    // 加载用户设置
    this.loadUserSettings();
    
    // 应用主题设置
    this.applyTheme();
    
    // 捕获全局错误
    wx.onError((error) => {
      console.error('小程序发生错误:', error);
    });
    
    // 开启分享功能
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
    
    // 监听设备电量变化
    this.startBatteryMonitor();

    // 初始化用户信息
    this.globalData = {
      userInfo: {
        nickName: '用户',  // 提供默认昵称
        avatarUrl: '',
        gender: 0,
        province: '',
        city: '',
        country: ''
      }
    }
    
    // 尝试获取用户信息
    wx.getUserInfo({
      success: res => {
        this.globalData.userInfo = res.userInfo
        console.log('获取用户信息成功:', res.userInfo)
      },
      fail: err => {
        console.log('获取用户信息失败:', err)
      }
    })
  },
  
  // 获取系统信息的方法
  getSystemInfo: function(callback) {
    if (this.globalData.systemInfoReady) {
      callback(this.globalData.systemInfo)
    } else {
      this.systemInfoReadyCallback = callback
    }
  },
  
  // 加载用户设置
  loadUserSettings: function() {
    try {
      const settings = wx.getStorageSync('userSettings');
      if (settings) {
        // 合并存储的设置与默认设置
        this.globalData.settings = {
          ...this.globalData.settings,
          ...settings
        };
      }
    } catch (error) {
      console.error('加载用户设置失败:', error);
    }
  },
  
  // 保存用户设置
  saveUserSettings: function() {
    try {
      wx.setStorageSync('userSettings', this.globalData.settings);
    } catch (error) {
      console.error('保存用户设置失败:', error);
    }
  },
  
  // 切换主题模式
  toggleTheme: function() {
    this.globalData.settings.darkMode = !this.globalData.settings.darkMode;
    this.saveUserSettings();
    this.applyTheme();
  },
  
  // 应用主题设置
  applyTheme: function() {
    const darkMode = this.globalData.settings.darkMode;
    
    // 根据主题模式选择对应的主题颜色
    const theme = darkMode ? this.globalData.themes.dark : this.globalData.themes.light;
    
    // 更新当前主题
    this.globalData.currentTheme = theme;
    
    // 动态设置导航栏颜色
    wx.setNavigationBarColor({
      frontColor: darkMode ? '#ffffff' : '#000000',
      backgroundColor: theme.navBarColor,
      animation: {
        duration: 300,
        timingFunc: 'easeInOut'
      }
    });
  },
  
  // 切换详细模式
  toggleDetailMode: function() {
    this.globalData.settings.detailMode = !this.globalData.settings.detailMode;
    this.saveUserSettings();
    return this.globalData.settings.detailMode;
  },
  
  // 获取当前模式预设
  getCurrentModePreset: function() {
    // 如果处于低电量模式，返回低电量模式预设
    if (this.globalData.isLowPowerMode) {
      return this.globalData.modePresets.lowPower;
    }
    
    // 否则根据当前模式返回对应的预设配置
    return this.globalData.settings.detailMode ? 
      this.globalData.modePresets.detail : 
      this.globalData.modePresets.simple;
  },
  
  // 应用食堂环境预设
  applyFoodHallPreset: function() {
    // 应用食堂环境的专用预设
    const preset = this.globalData.environmentPresets.foodHall;
    
    // 返回预设配置，供编辑器使用
    return preset;
  },
  
  // 开始监控电池状态
  startBatteryMonitor: function() {
    // 获取初始电量
    this.getBatteryInfo();
    
    // 定时检查电量（每5分钟）
    setInterval(() => {
      this.getBatteryInfo();
    }, 5 * 60 * 1000);
  },
  
  // 获取电池信息
  getBatteryInfo: function() {
    try {
      wx.getBatteryInfo({
        success: (res) => {
          const batteryLevel = res.level;
          const isCharging = res.isCharging;
          
          console.log('电池电量:', batteryLevel + '%', '是否充电:', isCharging);
          
          // 如果电量低于20%且未充电，进入低电量模式
          const wasInLowPowerMode = this.globalData.isLowPowerMode;
          this.globalData.isLowPowerMode = (batteryLevel <= 20 && !isCharging);
          
          // 如果低电量模式状态发生变化，通知页面
          if (wasInLowPowerMode !== this.globalData.isLowPowerMode) {
            this.notifyLowPowerModeChange();
          }
        },
        fail: (err) => {
          console.error('获取电池信息失败:', err);
        }
      });
    } catch (error) {
      console.error('获取电池信息异常:', error);
    }
  },
  
  // 通知低电量模式变化
  notifyLowPowerModeChange: function() {
    // 获取当前所有页面
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      
      // 如果当前页面有onLowPowerModeChange方法，调用它
      if (currentPage && typeof currentPage.onLowPowerModeChange === 'function') {
        currentPage.onLowPowerModeChange(this.globalData.isLowPowerMode);
      }
    }
    
    // 如果进入低电量模式，显示提示
    if (this.globalData.isLowPowerMode) {
      wx.showToast({
        title: '已进入低电量模式',
        icon: 'none',
        duration: 2000
      });
    }
  },
  
  // 手动切换低电量模式（用于测试）
  toggleLowPowerMode: function() {
    this.globalData.isLowPowerMode = !this.globalData.isLowPowerMode;
    this.notifyLowPowerModeChange();
    return this.globalData.isLowPowerMode;
  },
  
  // 全局分享设置
  onShareAppMessage() {
    // 获取用户昵称，如果不存在则使用默认值
    const nickName = (this.globalData.userInfo && this.globalData.userInfo.nickName) || '用户';
    
    return {
      title: `${nickName}推荐的图片编辑助手`,
      path: 'pages/index/index',
      imageUrl: '/images/app.png'
    }
  },
    
  globalData: {
    userInfo: null,
    systemInfo: null,
    safeArea: null,
    menuButtonInfo: null,
    navBarHeight: 0,
    systemInfoReady: false, // 系统信息是否准备好
    // 应用默认主题色
    themeColor: '#FF7D2C',
    // 应用默认设置
    settings: {
      saveHighQuality: false,  // 默认不使用高质量保存
      autoBeautify: false,     // 默认不自动美化
      showTips: true,         // 默认显示操作提示
      darkMode: false,        // 默认不使用暗黑模式
      simplifiedUI: false,    // 默认不使用简化界面
      largeText: false,       // 默认不使用大字体
      highContrast: false,    // 默认不使用高对比度
      gestures: true,         // 默认启用手势操作
      detailMode: false       // 默认使用简单模式
    },
    // 主题配置
    themes: {
      // 浅色主题
      light: {
        primary: '#FF7D2C',
        primaryLight: '#FF9F5A',
        primaryDark: '#E56B1F',
        textPrimary: '#333333',
        textSecondary: '#666666',
        background: '#FFFFFF',
        backgroundLight: '#F5F5F5',
        card: '#FFFFFF',
        border: '#EEEEEE',
        navBarColor: '#FFFFFF'
      },
      // 深色主题
      dark: {
        primary: '#FF9F5A',
        primaryLight: '#FFBB86',
        primaryDark: '#E56B1F',
        textPrimary: '#FFFFFF',
        textSecondary: '#CCCCCC',
        background: '#121212',
        backgroundLight: '#1E1E1E',
        card: '#2C2C2C',
        border: '#3D3D3D',
        navBarColor: '#1E1E1E'
      }
    },
    // 当前主题（默认为浅色主题）
    currentTheme: null,
    // 模式预设
    modePresets: {
      // 简单模式 - 显示基础功能，隐藏高级选项
      simple: {
        showAdvancedOptions: false,
        showDetailedControls: false,
        autoApplyFilters: true,
        quickSaveEnabled: true,
        defaultSaveQuality: 'medium',
        toolbarItems: ['crop', 'rotate', 'filter', 'text', 'save']
      },
      // 详细模式 - 显示全部功能和选项
      detail: {
        showAdvancedOptions: true,
        showDetailedControls: true,
        autoApplyFilters: false,
        quickSaveEnabled: false,
        defaultSaveQuality: 'high',
        toolbarItems: ['crop', 'rotate', 'adjust', 'filter', 'text', 'sticker', 'collage', 'save']
      },
      // 低电量模式 - 精简功能，优化性能
      lowPower: {
        showAdvancedOptions: false,
        showDetailedControls: false,
        autoApplyFilters: true,
        quickSaveEnabled: true,
        defaultSaveQuality: 'low',
        disableAnimations: true,
        disableShadows: true,
        disableAutoRefresh: true,
        reduceImageQuality: true,
        toolbarItems: ['crop', 'filter', 'text', 'save']
      }
    },
    // 环境预设
    environmentPresets: {
      // 食堂环境预设 - 针对食堂环境的特殊优化
      foodHall: {
        brightness: 10,      // 增加亮度
        contrast: 15,        // 增加对比度
        saturation: 20,      // 增加饱和度
        sharpness: 30,       // 增加锐度
        filterStrength: 0.7, // 食物增鲜滤镜强度
        defaultFilter: 'food', // 默认使用食物滤镜
        autoWhiteBalance: true, // 自动白平衡
        autoExposure: true,  // 自动曝光调整
        noiseSuppression: true // 噪点抑制
      }
    },
    // 食堂餐次模板
    mealTemplates: {
      breakfast: '今日早餐',
      lunch: '今日午餐',
      dinner: '今日晚餐'
    },
    // 低电量模式状态
    isLowPowerMode: false,
    
    // iOS13图片缓存相关
    ios13ImageData: null,
    ios13ImageTimestamp: null,
    ios13ImageId: null
  }
})