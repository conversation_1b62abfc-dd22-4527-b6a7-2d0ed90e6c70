<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>平台检测和路径处理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .platform-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .platform-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .test-case {
            background: #f8f9fa;
            margin: 10px 0;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .test-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .test-input, .test-output {
            margin: 5px 0;
            font-family: monospace;
            background: #e9ecef;
            padding: 5px 8px;
            border-radius: 4px;
            font-size: 14px;
        }
        .test-input {
            border-left: 3px solid #28a745;
        }
        .test-output {
            border-left: 3px solid #ffc107;
        }
        .result {
            margin-top: 8px;
            font-weight: bold;
        }
        .pass {
            color: #28a745;
        }
        .fail {
            color: #dc3545;
        }
        .summary {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>平台检测和路径处理测试</h1>
    <div id="test-results"></div>
    <div id="summary" class="summary"></div>

    <script>
        // 模拟fixImagePath函数
        function fixImagePath(imagePath, mockPlatform) {
            if (!imagePath || typeof imagePath !== 'string') {
                return imagePath;
            }
            
            let fixedPath = imagePath;
            
            // 模拟平台检测
            const isDevTool = mockPlatform === 'devtools';
            
            if (!isDevTool && fixedPath.startsWith('http://tmp/')) {
                // 只在真机环境下转换 http://tmp/ 到 /tmp/
                fixedPath = fixedPath.replace('http://tmp/', '/tmp/');
                console.log('真机环境修正HTTP临时文件路径:', fixedPath);
            } else if (fixedPath.startsWith('http://tmp/')) {
                console.log('开发工具环境保持HTTP临时文件路径:', fixedPath);
            }
            
            // 处理URL编码问题
            if (fixedPath.indexOf('%') >= 0) {
                try {
                    fixedPath = decodeURIComponent(fixedPath);
                    console.log('解码路径:', fixedPath);
                } catch (e) {
                    console.warn('路径解码失败:', e);
                }
            }
            
            // 确保临时文件路径格式正确（仅在非开发工具环境）
            if (!isDevTool && fixedPath.indexOf('tmp/') >= 0 && !fixedPath.startsWith('/') && !fixedPath.startsWith('http://')) {
                // 提取tmp路径部分
                const tmpIndex = fixedPath.indexOf('tmp/');
                fixedPath = fixedPath.substring(tmpIndex);
                if (!fixedPath.startsWith('/')) {
                    fixedPath = '/' + fixedPath;
                }
                console.log('修正临时文件路径:', fixedPath);
            }
            
            // 移除wxfile://前缀（如果存在）
            if (fixedPath.startsWith('wxfile://')) {
                fixedPath = fixedPath.replace('wxfile://', '');
                console.log('移除wxfile前缀:', fixedPath);
            }
            
            return fixedPath;
        }

        // 测试数据
        const mockPlatforms = [
            { platform: 'devtools', description: '开发工具环境', color: '#007bff' },
            { platform: 'ios', description: 'iOS真机环境', color: '#28a745' },
            { platform: 'android', description: 'Android真机环境', color: '#ffc107' }
        ];

        const testCases = [
            {
                input: 'http://tmp/VPTH6rialTS6ed92532913e5f044cc38cbf36665e56b.png',
                description: '开发工具临时文件路径'
            },
            {
                input: '/tmp/test.jpg',
                description: '标准临时文件路径'
            },
            {
                input: 'wxfile:///tmp/test.png',
                description: 'wxfile前缀路径'
            },
            {
                input: 'tmp/test.png',
                description: '相对临时文件路径'
            },
            {
                input: 'https://example.com/image.jpg',
                description: '真正的网络图片'
            }
        ];

        // 运行测试
        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            const summaryDiv = document.getElementById('summary');
            
            let totalTests = 0;
            let passedTests = 0;
            
            mockPlatforms.forEach(platform => {
                const platformDiv = document.createElement('div');
                platformDiv.className = 'platform-section';
                
                const titleDiv = document.createElement('div');
                titleDiv.className = 'platform-title';
                titleDiv.style.borderBottomColor = platform.color;
                titleDiv.textContent = `${platform.description} (${platform.platform})`;
                platformDiv.appendChild(titleDiv);
                
                testCases.forEach((testCase, index) => {
                    totalTests++;
                    
                    const result = fixImagePath(testCase.input, platform.platform);
                    
                    // 验证结果
                    let isExpected = false;
                    let expectedBehavior = '';
                    
                    if (platform.platform === 'devtools') {
                        // 开发工具环境应该保持http://tmp/格式
                        if (testCase.input.startsWith('http://tmp/')) {
                            isExpected = result === testCase.input;
                            expectedBehavior = '应保持原始http://tmp/格式';
                        } else {
                            isExpected = true;
                            expectedBehavior = '应正常处理';
                        }
                    } else {
                        // 真机环境应该转换http://tmp/为/tmp/
                        if (testCase.input.startsWith('http://tmp/')) {
                            isExpected = result.startsWith('/tmp/');
                            expectedBehavior = '应转换为/tmp/格式';
                        } else {
                            isExpected = true;
                            expectedBehavior = '应正常处理';
                        }
                    }
                    
                    if (isExpected) passedTests++;
                    
                    const testDiv = document.createElement('div');
                    testDiv.className = 'test-case';
                    testDiv.innerHTML = `
                        <div class="test-title">测试 ${index + 1}: ${testCase.description}</div>
                        <div class="test-input">输入: ${testCase.input}</div>
                        <div class="test-output">输出: ${result}</div>
                        <div class="result ${isExpected ? 'pass' : 'fail'}">
                            ${isExpected ? '✅ 符合预期' : '❌ 不符合预期'} - ${expectedBehavior}
                        </div>
                    `;
                    
                    platformDiv.appendChild(testDiv);
                });
                
                resultsDiv.appendChild(platformDiv);
            });
            
            summaryDiv.innerHTML = `
                <h2>测试结果总结</h2>
                <p>总测试数: ${totalTests}</p>
                <p>通过测试: ${passedTests}</p>
                <p class="${passedTests === totalTests ? 'pass' : 'fail'}">
                    ${passedTests === totalTests ? '🎉 所有测试通过！' : '⚠️ 部分测试失败'}
                </p>
                <p>通过率: ${Math.round(passedTests / totalTests * 100)}%</p>
            `;
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
