/// <reference path="./types/index.d.ts" />

interface IAppOption {
  globalData: {
    userInfo?: WechatMiniprogram.UserInfo,
    menuButtonInfo?: {
      left: number,
      top: number,
      width: number,
      height: number,
      bottom: number
    },
    systemInfo?: {
      statusBarHeight: number,
      platform: string,
      [key: string]: any
    }
  }
  userInfoReadyCallback?: WechatMiniprogram.GetUserInfoSuccessCallback,
}